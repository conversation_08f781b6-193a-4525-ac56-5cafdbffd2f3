<?php
/**
 * 🔑 重置密码API接口
 * 
 * 功能：处理密码重置请求
 * 方法：POST
 * 参数：token, password, confirm_password
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求'
    ]);
    exit;
}

// 引入认证类
require_once '../auth/Auth.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 如果不是JSON请求，尝试从POST获取
    if (!$input) {
        $input = $_POST;
    }
    
    $token = trim($input['token'] ?? '');
    $password = trim($input['password'] ?? '');
    $confirm_password = trim($input['confirm_password'] ?? '');
    
    // 验证输入
    if (empty($token)) {
        throw new Exception('重置令牌不能为空');
    }
    
    if (empty($password)) {
        throw new Exception('请输入新密码');
    }
    
    // 验证密码强度 - 使用动态配置
    require_once '../auth/SystemConfig.php';
    $passwordValidation = SystemConfig::validatePasswordComplexity($password);
    if (!$passwordValidation['valid']) {
        throw new Exception(implode('；', $passwordValidation['errors']));
    }

    if ($password !== $confirm_password) {
        throw new Exception('两次输入的密码不一致');
    }
    
    // 重置密码
    $result = Auth::resetPassword($token, $password);
    
    if ($result['success']) {
        // 记录密码重置日志
        error_log("密码重置成功: 令牌 {$token} - " . date('Y-m-d H:i:s'));
        
        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'redirect' => '/login.php?reset=1',
            'timestamp' => time()
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $result['message'],
            'timestamp' => time()
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => time()
    ]);
}

/**
 * 计算密码强度 - 使用动态配置
 */
function calculatePasswordStrength($password) {
    require_once '../auth/SystemConfig.php';
    $rules = SystemConfig::getPasswordComplexityRules();

    $score = 0;
    $checks = [
        'length' => strlen($password) >= $rules['min_length'],
        'lowercase' => preg_match('/[a-z]/', $password),
        'uppercase' => preg_match('/[A-Z]/', $password),
        'numbers' => preg_match('/\d/', $password),
        'symbols' => preg_match('/[^A-Za-z0-9]/', $password)
    ];

    // 基础长度检查
    if ($checks['length']) $score++;

    // 额外长度加分
    if (strlen($password) >= $rules['min_length'] + 2) $score++;
    if (strlen($password) >= 12) $score++;

    // 复杂度检查
    if ($checks['lowercase']) $score++;
    if ($checks['uppercase']) $score++;
    if ($checks['numbers']) $score++;
    if ($checks['symbols']) $score++;

    return [
        'score' => $score,
        'checks' => $checks,
        'rules' => $rules
    ];
}
?>
