<?php
/**
 * 🔑 找回密码API接口
 * 
 * 功能：处理密码重置请求
 * 方法：POST
 * 参数：email
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入认证类
require_once '../auth/Auth.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 检查密码找回功能是否启用
Auth::requirePasswordResetAccess();

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求'
    ]);
    exit;
}

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);

    // 如果不是JSON请求，尝试从POST获取
    if (!$input) {
        $input = $_POST;
    }

    $action = trim($input['action'] ?? 'send_code');
    $email = trim($input['email'] ?? '');
    $code = trim($input['code'] ?? '');
    $captchaSessionKey = trim($input['captcha_session_key'] ?? '');

    // 验证输入
    if (empty($email)) {
        throw new Exception('请输入邮箱地址');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('请输入有效的邮箱地址');
    }

    // 检查是否需要验证码
    require_once '../auth/SystemConfig.php';
    $needsCaptcha = SystemConfig::get('security', 'enable_reset_captcha', '0') === '1';

    // 临时禁用验证码检查，用于调试
    $needsCaptcha = false;

    if ($needsCaptcha && $action === 'send_code') {
        if (empty($captchaSessionKey)) {
            throw new Exception('请完成验证码验证');
        }
    }

    if ($action === 'send_code') {
        // 第一步：发送验证码邮件
        $result = Auth::sendPasswordResetVerifyCode($email);

        if ($result['success']) {
            // 记录验证码请求日志
            error_log("密码重置验证码请求: {$email} - " . date('Y-m-d H:i:s'));

            echo json_encode([
                'success' => true,
                'message' => $result['message'],
                'email_sent' => $result['email_sent'] ?? false,
                'timestamp' => time()
            ]);
        } else {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $result['message'],
                'timestamp' => time()
            ]);
        }
    } elseif ($action === 'verify_code') {
        // 第二步：验证验证码并发送重置邮件
        if (empty($code)) {
            throw new Exception('请输入验证码');
        }

        $result = Auth::verifyPasswordResetCode($email, $code);

        if ($result['success']) {
            // 记录验证成功日志
            error_log("密码重置验证码验证成功: {$email} - " . date('Y-m-d H:i:s'));

            echo json_encode([
                'success' => true,
                'message' => $result['message'],
                'email_sent' => $result['email_sent'] ?? false,
                'timestamp' => time()
            ]);
        } else {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $result['message'],
                'timestamp' => time()
            ]);
        }
    } elseif ($action === 'resend_reset_email') {
        // 第三步：直接重新发送重置邮件（跳过验证码验证）
        $result = Auth::sendPasswordResetEmail($email);

        if ($result['success']) {
            // 记录重新发送日志
            error_log("重新发送密码重置邮件: {$email} - " . date('Y-m-d H:i:s'));

            echo json_encode([
                'success' => true,
                'message' => '重置邮件已重新发送，请查收',
                'timestamp' => time()
            ]);
        } else {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $result['message'],
                'timestamp' => time()
            ]);
        }
    } else {
        throw new Exception('无效的操作类型');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => time()
    ]);
}

/**
 * 发送密码重置邮件（模拟）
 */
function sendPasswordResetEmail($email, $reset_link) {
    // 实际项目中应该使用真实的邮件发送服务
    // 例如：PHPMailer, SwiftMailer, 或者第三方服务如SendGrid
    
    $subject = '密码重置 - 现代化PHP管理系统';
    $message = "
    <html>
    <head>
        <title>密码重置</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f8f9fa; }
            .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
            .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>密码重置</h1>
            </div>
            <div class='content'>
                <p>您好，</p>
                <p>我们收到了您的密码重置请求。请点击下面的链接来重置您的密码：</p>
                <p><a href='{$reset_link}' class='button'>重置密码</a></p>
                <p>如果您无法点击上面的按钮，请复制以下链接到浏览器地址栏：</p>
                <p>{$reset_link}</p>
                <p>此链接将在1小时后过期。</p>
                <p>如果您没有请求密码重置，请忽略此邮件。</p>
            </div>
            <div class='footer'>
                <p>© " . date('Y') . " 现代化PHP管理系统. 保留所有权利.</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    // 模拟邮件发送成功
    // 实际代码：
    // return mail($email, $subject, $message, $headers);
    
    return true;
}
?>
