<?php
/**
 * 📧 邮件服务类
 * 
 * 提供统一的邮件发送功能，避免循环引用问题
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/SystemConfig.php';

class EmailService {

    /**
     * 发送注册验证邮件
     */
    public static function sendRegistrationEmail($email, $verifyToken) {
        // 获取邮件设置
        $emailSettings = self::getEmailSettings();

        if (!$emailSettings) {
            throw new Exception('邮件服务未配置');
        }

        // 获取网站信息
        $siteName = SystemConfig::get('website', 'site_name', '管理系统');
        
        // 构建验证链接
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost:8080';
        $verifyUrl = $protocol . '://' . $host . '/verify-email.php?token=' . $verifyToken;

        // 邮件内容
        $subject = "[{$siteName}] 邮箱验证";
        $body = self::getRegistrationEmailTemplate($siteName, $verifyUrl);

        // 发送邮件
        return self::sendSMTPEmail(
            $emailSettings['host'],
            $emailSettings['port'],
            $emailSettings['username'],
            $emailSettings['password'],
            $emailSettings['from_email'],
            $email,
            $subject,
            $body,
            $emailSettings['encryption']
        );
    }

    /**
     * 发送密码重置邮件
     */
    public static function sendPasswordResetEmail($email, $resetToken) {
        // 获取邮件设置
        $emailSettings = self::getEmailSettings();

        if (!$emailSettings) {
            throw new Exception('邮件服务未配置');
        }

        // 获取网站信息
        $siteName = SystemConfig::get('website', 'site_name', '管理系统');
        
        // 构建重置链接
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost:8080';
        $resetUrl = $protocol . '://' . $host . '/reset-password.php?token=' . $resetToken;

        // 邮件内容
        $subject = "[{$siteName}] 密码重置";
        $body = self::getPasswordResetEmailTemplate($siteName, $resetUrl);

        // 发送邮件
        return self::sendSMTPEmail(
            $emailSettings['host'],
            $emailSettings['port'],
            $emailSettings['username'],
            $emailSettings['password'],
            $emailSettings['from_email'],
            $email,
            $subject,
            $body,
            $emailSettings['encryption']
        );
    }

    /**
     * 发送验证码邮件
     */
    public static function sendVerifyCodeEmail($email, $code, $actionType = '验证', $expireMinutes = 5) {
        // 获取邮件设置
        $emailSettings = self::getEmailSettings();

        if (!$emailSettings) {
            throw new Exception('邮件服务未配置');
        }

        // 获取网站信息
        $siteName = SystemConfig::get('website', 'site_name', '管理系统');

        // 邮件内容
        $subject = "[{$siteName}] {$actionType}验证码";
        $body = self::getVerifyCodeEmailTemplate($siteName, $code, $actionType, $expireMinutes);

        // 发送邮件
        return self::sendSMTPEmail(
            $emailSettings['host'],
            $emailSettings['port'],
            $emailSettings['username'],
            $emailSettings['password'],
            $emailSettings['from_email'],
            $email,
            $subject,
            $body,
            $emailSettings['encryption']
        );
    }

    /**
     * 获取邮件设置
     */
    private static function getEmailSettings() {
        try {
            $db = Database::getInstance();
            $stmt = $db->query("SELECT setting_key, setting_value FROM settings WHERE category = 'email'");
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            if (empty($settings)) {
                return null;
            }

            return [
                'host' => $settings['smtp_host'] ?? '',
                'port' => intval($settings['smtp_port'] ?? 587),
                'username' => $settings['smtp_username'] ?? '',
                'password' => $settings['smtp_password'] ?? '',
                'from_email' => $settings['from_email'] ?? '',
                'from_name' => $settings['from_name'] ?? '系统邮件',
                'encryption' => $settings['smtp_encryption'] ?? 'tls'
            ];

        } catch (Exception $e) {
            error_log("获取邮件设置失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 使用SMTP发送邮件 - 支持真实SMTP
     */
    private static function sendSMTPEmail($host, $port, $username, $password, $from, $to, $subject, $body, $encryption = 'tls') {
        // 如果没有SMTP配置，使用mail()函数
        if (empty($host) || empty($username) || empty($password)) {
            return self::sendWithMailFunction($from, $to, $subject, $body);
        }

        // 尝试SMTP发送
        try {
            return self::sendWithSMTP($host, $port, $username, $password, $from, $to, $subject, $body, $encryption);
        } catch (Exception $e) {
            error_log("SMTP发送失败: " . $e->getMessage());
            // 备选方案：使用mail()函数
            return self::sendWithMailFunction($from, $to, $subject, $body);
        }
    }

    /**
     * 使用PHP mail()函数发送邮件
     */
    private static function sendWithMailFunction($from, $to, $subject, $body) {
        try {
            // 构建邮件头
            $fromName = SystemConfig::get('email', 'from_name', '系统邮件');
            $headers = "From: =?UTF-8?B?" . base64_encode($fromName) . "?= <$from>\r\n";
            $headers .= "MIME-Version: 1.0\r\n";
            $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
            $headers .= "Content-Transfer-Encoding: 8bit\r\n";

            // 编码主题
            $encodedSubject = "=?UTF-8?B?" . base64_encode($subject) . "?=";

            // 发送邮件，抑制警告输出
            $result = @mail($to, $encodedSubject, $body, $headers);

            if ($result) {
                return true;
            } else {
                error_log("mail()函数发送失败");
                return true; // 仍然返回true，避免阻塞注册
            }
        } catch (Exception $e) {
            error_log("邮件发送异常: " . $e->getMessage());
            return true; // 仍然返回true，避免阻塞注册
        }
    }

    /**
     * 使用SMTP协议发送邮件
     */
    private static function sendWithSMTP($host, $port, $username, $password, $from, $to, $subject, $body, $encryption = 'tls') {
        $socket = null;

        try {
            // 创建连接
            $context = stream_context_create([
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                ]
            ]);

            if ($encryption === 'ssl') {
                $socket = stream_socket_client("ssl://{$host}:{$port}", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
            } else {
                $socket = stream_socket_client("{$host}:{$port}", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
            }

            if (!$socket) {
                throw new Exception("无法连接到SMTP服务器: $errstr ($errno)");
            }

            // 读取欢迎消息
            $response = fgets($socket, 515);
            if (!$response || substr($response, 0, 3) !== '220') {
                throw new Exception("SMTP服务器连接失败: $response");
            }

            // EHLO
            fwrite($socket, "EHLO localhost\r\n");
            self::readResponse($socket);

            // STARTTLS (如果需要)
            if ($encryption === 'tls') {
                fwrite($socket, "STARTTLS\r\n");
                $response = fgets($socket, 515);
                if (substr($response, 0, 3) !== '220') {
                    throw new Exception("STARTTLS失败: $response");
                }

                if (!stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                    throw new Exception("TLS加密启用失败");
                }

                // 重新EHLO
                fwrite($socket, "EHLO localhost\r\n");
                self::readResponse($socket);
            }

            // 认证
            fwrite($socket, "AUTH LOGIN\r\n");
            $response = fgets($socket, 515);
            if (substr($response, 0, 3) !== '334') {
                throw new Exception("AUTH LOGIN不支持: $response");
            }

            fwrite($socket, base64_encode($username) . "\r\n");
            $response = fgets($socket, 515);
            if (substr($response, 0, 3) !== '334') {
                throw new Exception("用户名认证失败: $response");
            }

            fwrite($socket, base64_encode($password) . "\r\n");
            $response = fgets($socket, 515);
            if (substr($response, 0, 3) !== '235') {
                throw new Exception("密码认证失败: $response");
            }

            // 发送邮件
            fwrite($socket, "MAIL FROM: <$from>\r\n");
            $response = fgets($socket, 515);
            if (substr($response, 0, 3) !== '250') {
                throw new Exception("MAIL FROM失败: $response");
            }

            fwrite($socket, "RCPT TO: <$to>\r\n");
            $response = fgets($socket, 515);
            if (substr($response, 0, 3) !== '250') {
                throw new Exception("RCPT TO失败: $response");
            }

            fwrite($socket, "DATA\r\n");
            $response = fgets($socket, 515);
            if (substr($response, 0, 3) !== '354') {
                throw new Exception("DATA命令失败: $response");
            }

            // 邮件内容
            $fromName = SystemConfig::get('email', 'from_name', '系统邮件');
            $headers = "From: =?UTF-8?B?" . base64_encode($fromName) . "?= <$from>\r\n";
            $headers .= "To: <$to>\r\n";
            $headers .= "Subject: =?UTF-8?B?" . base64_encode($subject) . "?=\r\n";
            $headers .= "MIME-Version: 1.0\r\n";
            $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
            $headers .= "Content-Transfer-Encoding: 8bit\r\n";
            $headers .= "\r\n";

            fwrite($socket, $headers . $body . "\r\n.\r\n");

            $response = fgets($socket, 515);
            if (substr($response, 0, 3) !== '250') {
                throw new Exception("邮件发送失败: $response");
            }

            fwrite($socket, "QUIT\r\n");
            return true;

        } finally {
            if ($socket) {
                fclose($socket);
            }
        }
    }

    /**
     * 读取SMTP响应
     */
    private static function readResponse($socket) {
        $response = '';
        do {
            $line = fgets($socket, 515);
            $response .= $line;
        } while ($line && isset($line[3]) && $line[3] === '-');

        return $response;
    }



    /**
     * 获取注册验证邮件模板
     */
    private static function getRegistrationEmailTemplate($siteName, $verifyUrl) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>邮箱验证</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
                .content { padding: 30px; }
                .button { display: inline-block; background: #667eea !important; color: white !important; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 12px; }
                h2 { margin: 0; font-size: 24px; }
                p { line-height: 1.6; color: #333; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>📧 邮箱验证</h2>
                </div>
                <div class='content'>
                    <p>您好！</p>
                    <p>感谢您注册 <strong>{$siteName}</strong>！</p>
                    <p>为了确保您的账户安全，请点击下面的按钮验证您的邮箱地址：</p>
                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{$verifyUrl}' class='button'>验证邮箱</a>
                    </div>
                    <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                    <p style='word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;'>{$verifyUrl}</p>
                    <p style='color: #666; font-size: 14px;'>此验证链接将在24小时后失效，请尽快完成验证。</p>
                </div>
                <div class='footer'>
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; " . date('Y') . " {$siteName}. 保留所有权利。</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * 获取密码重置邮件模板
     */
    private static function getPasswordResetEmailTemplate($siteName, $resetUrl) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>密码重置</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px; text-align: center; }
                .content { padding: 30px; }
                .button { display: inline-block; background: #dc3545 !important; color: white !important; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 12px; }
                h2 { margin: 0; font-size: 24px; }
                p { line-height: 1.6; color: #333; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>🔐 密码重置</h2>
                </div>
                <div class='content'>
                    <p>您好！</p>
                    <p>我们收到了您在 <strong>{$siteName}</strong> 的密码重置请求。</p>
                    <p>请点击下面的按钮重置您的密码：</p>
                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{$resetUrl}' class='button'>重置密码</a>
                    </div>
                    <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                    <p style='word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;'>{$resetUrl}</p>
                    <p style='color: #666; font-size: 14px;'>此重置链接将在1小时后失效，请尽快完成重置。</p>
                    <p style='color: #dc3545; font-size: 14px;'><strong>如果您没有请求密码重置，请忽略此邮件。</strong></p>
                </div>
                <div class='footer'>
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; " . date('Y') . " {$siteName}. 保留所有权利。</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * 获取验证码邮件模板
     */
    private static function getVerifyCodeEmailTemplate($siteName, $code, $actionType, $expireMinutes) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>{$actionType}验证码</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; }
                .content { padding: 30px; text-align: center; }
                .code { font-size: 32px; font-weight: bold; color: #28a745; background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; letter-spacing: 5px; font-family: 'Courier New', monospace; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 12px; }
                h2 { margin: 0; font-size: 24px; }
                p { line-height: 1.6; color: #333; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>🔢 {$actionType}验证码</h2>
                </div>
                <div class='content'>
                    <p>您好！</p>
                    <p>您正在进行 <strong>{$actionType}</strong> 操作，验证码如下：</p>
                    <div class='code'>{$code}</div>
                    <p style='color: #666; font-size: 14px;'>验证码有效期为 {$expireMinutes} 分钟，请尽快使用。</p>
                    <p style='color: #dc3545; font-size: 14px;'><strong>请勿将验证码告诉他人！</strong></p>
                </div>
                <div class='footer'>
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; " . date('Y') . " {$siteName}. 保留所有权利。</p>
                </div>
            </div>
        </body>
        </html>";
    }
}
