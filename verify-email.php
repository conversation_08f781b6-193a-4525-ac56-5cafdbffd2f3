<?php
// 邮箱验证页面
session_start();

// 引入必要的文件
require_once 'auth/SystemConfig.php';

$page_title = '邮箱验证';
$error_message = '';
$success_message = '';
$verification_status = 'pending'; // pending, success, error

// 获取验证令牌
$token = $_GET['token'] ?? '';
$code = $_GET['code'] ?? '';

// 如果有令牌，尝试自动验证
if (!empty($token)) {
    // 这里会通过API进行验证
    $verification_status = 'auto_verify';
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle($page_title); ?></title>
    
    <!-- 引入全局样式和组件 -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/components/modal.css">
    
    <style>
        .verify-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        
        .verify-card {
            background: var(--bg-primary);
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            padding: 3rem 2rem;
            width: 100%;
            max-width: 500px;
            text-align: center;
            border: 1px solid var(--border-color);
        }
        
        .verify-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
        }
        
        .verify-icon.pending {
            background: var(--warning-light);
            color: var(--warning-color);
        }
        
        .verify-icon.success {
            background: var(--success-light);
            color: var(--success-color);
            animation: pulse 2s infinite;
        }

        .verify-icon.already-verified {
            background: var(--info-light);
            color: var(--info-color);
        }

        .verify-icon.error {
            background: var(--error-light);
            color: var(--error-color);
        }
        
        .verify-title {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }
        
        .verify-message {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .verify-form {
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 16px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            transition: all 0.2s ease;
            text-align: center;
            letter-spacing: 2px;
            font-family: 'Courier New', monospace;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
            outline: none;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .btn-secondary:hover {
            background: var(--bg-secondary);
        }
        
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .action-links {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }
        
        .action-links a {
            color: var(--primary-color);
            text-decoration: none;
            margin: 0 1rem;
        }
        
        .action-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="verify-container">
        <div class="verify-card">
            <!-- 验证状态图标 -->
            <div class="verify-icon pending" id="verifyIcon">
                ⏳
            </div>
            
            <!-- 标题 -->
            <h1 class="verify-title" id="verifyTitle">邮箱验证</h1>
            
            <!-- 消息 -->
            <div class="verify-message" id="verifyMessage">
                请等待自动验证完成，或点击邮件中的验证链接。
            </div>
            
            <!-- 操作链接 -->
            <div class="action-links">
                <a href="login.php">返回登录</a>
                <a href="#" id="resendLink">重新发送邮件</a>
            </div>
        </div>
    </div>

    <!-- 引入全局脚本 -->
    <script src="assets/js/main.js"></script>
    <script src="assets/components/modal.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const verifyIcon = document.getElementById('verifyIcon');
            const verifyTitle = document.getElementById('verifyTitle');
            const verifyMessage = document.getElementById('verifyMessage');
            const resendLink = document.getElementById('resendLink');
            
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            const code = urlParams.get('code');

            // 当前验证状态
            let currentVerificationStatus = 'pending'; // pending, success, already_verified, invalid_token
            
            // 如果有令牌，尝试自动验证
            if (token) {
                autoVerifyWithToken(token);
            } else {
                // 显示等待验证状态
                showWaitingForVerification();
            }
            
            // 自动验证（通过令牌）
            async function autoVerifyWithToken(token) {
                try {
                    console.log('开始自动验证，token:', token);

                    verifyIcon.className = 'verify-icon pending';
                    verifyIcon.textContent = '⏳';
                    verifyTitle.textContent = '正在验证...';
                    verifyMessage.textContent = '请稍候，正在自动验证您的邮箱...';

                    const requestData = {
                        action: 'verify_token',
                        token: token
                    };
                    console.log('发送请求数据:', requestData);

                    const response = await fetch('api/verify-email.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestData)
                    });

                    console.log('响应状态:', response.status);
                    console.log('响应头:', response.headers);

                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }

                    const responseText = await response.text();
                    console.log('原始响应:', responseText);

                    const data = JSON.parse(responseText);
                    console.log('解析后数据:', data);

                    if (data.success) {
                        console.log('验证成功，状态:', data.status);

                        if (data.status === 'just_verified') {
                            // 刚刚验证成功
                            currentVerificationStatus = 'just_verified';
                            showSuccess('🎉 邮箱验证成功！', '您的邮箱已成功验证，现在可以正常登录了。');
                        } else if (data.status === 'already_verified') {
                            // 之前已经验证过了
                            currentVerificationStatus = 'already_verified';
                            showAlreadyVerified('✅ 邮箱已验证', '您的邮箱之前已经验证过了，可以直接登录。');
                        } else {
                            // 默认成功状态
                            currentVerificationStatus = 'success';
                            showSuccess('邮箱验证成功！', '您的邮箱已成功验证，现在可以正常登录了。');
                        }

                        setTimeout(() => {
                            window.location.href = 'login.php?verified=1';
                        }, 3000);
                    } else {
                        console.log('验证失败:', data.message, '状态:', data.status);

                        if (data.status === 'invalid_token') {
                            currentVerificationStatus = 'invalid_token';
                            showError('❌ 验证链接无效', '验证令牌无效或已过期，请重新注册或联系管理员。');
                            // 无效令牌不显示等待状态
                        } else {
                            currentVerificationStatus = 'error';
                            showError('验证失败', data.message || '验证过程中发生错误，请稍后重试。');
                            showWaitingForVerification();
                        }
                    }

                } catch (error) {
                    console.error('自动验证错误:', error);
                    showError('验证失败', '网络错误，请稍后重试或重新发送验证邮件。错误: ' + error.message);
                    showWaitingForVerification();
                }
            }

            // 显示等待验证状态
            function showWaitingForVerification() {
                verifyIcon.className = 'verify-icon pending';
                verifyIcon.textContent = '📧';
                verifyTitle.textContent = '等待邮箱验证';
                verifyMessage.textContent = '请查收邮件并点击其中的验证链接完成验证。如果未收到邮件，可以重新发送。';
            }
            
            // 显示成功状态
            function showSuccess(title, message) {
                verifyIcon.className = 'verify-icon success';
                verifyIcon.textContent = '✅';
                verifyTitle.textContent = title;
                verifyMessage.textContent = message;
                // 隐藏重发链接
                if (resendLink) {
                    resendLink.style.display = 'none';
                }
            }
            
            // 显示已验证状态
            function showAlreadyVerified(title, message) {
                verifyIcon.className = 'verify-icon already-verified';
                verifyIcon.textContent = '✅';
                verifyTitle.textContent = title;
                verifyMessage.textContent = message;
                // 隐藏重发链接
                if (resendLink) {
                    resendLink.style.display = 'none';
                }
            }

            // 显示错误状态
            function showError(title, message) {
                verifyIcon.className = 'verify-icon error';
                verifyIcon.textContent = '❌';
                verifyTitle.textContent = title;
                verifyMessage.textContent = message;
            }



            // 重新发送邮件
            resendLink.addEventListener('click', async function(e) {
                e.preventDefault();

                // 根据当前状态显示不同的弹窗
                switch (currentVerificationStatus) {
                    case 'just_verified':
                        if (window.modalManager) {
                            window.modalManager.info(
                                '✅ 邮箱已验证成功',
                                '您的邮箱已经验证成功了，无需重新发送验证邮件。即将跳转到登录页面。',
                                {
                                    confirmText: '前往登录',
                                    onConfirm: () => {
                                        window.location.href = 'login.php?verified=1';
                                    }
                                }
                            );
                        } else {
                            alert('您的邮箱已经验证成功了，无需重新发送验证邮件。');
                        }
                        return;

                    case 'already_verified':
                        if (window.modalManager) {
                            window.modalManager.info(
                                '✅ 邮箱已验证',
                                '您的邮箱之前已经验证过了，无需重新发送验证邮件。您可以直接登录。',
                                {
                                    confirmText: '前往登录',
                                    onConfirm: () => {
                                        window.location.href = 'login.php';
                                    }
                                }
                            );
                        } else {
                            alert('您的邮箱已经验证过了，无需重新发送验证邮件。');
                        }
                        return;

                    case 'invalid_token':
                        if (window.modalManager) {
                            window.modalManager.warning(
                                '⚠️ 验证链接已失效',
                                '当前验证链接已失效，无法重新发送邮件。请重新注册账户或联系管理员获取新的验证链接。',
                                {
                                    confirmText: '前往注册',
                                    cancelText: '返回登录',
                                    onConfirm: () => {
                                        window.location.href = 'register.php';
                                    },
                                    onCancel: () => {
                                        window.location.href = 'login.php';
                                    }
                                }
                            );
                        } else {
                            alert('验证链接已失效，请重新注册或联系管理员。');
                        }
                        return;

                    case 'error':
                    case 'pending':
                    default:
                        // 可以重发邮件的状态
                        break;
                }

                // 执行重发邮件
                try {
                    const response = await fetch('api/verify-email.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'resend_email'
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        if (window.modalManager) {
                            window.modalManager.success(
                                '📧 邮件发送成功',
                                '验证邮件已重新发送到您的邮箱，请查收并点击邮件中的验证链接。',
                                {
                                    confirmText: '我知道了'
                                }
                            );
                        } else {
                            alert('验证邮件已重新发送，请查收');
                        }
                    } else {
                        if (window.modalManager) {
                            window.modalManager.error(
                                '❌ 发送失败',
                                data.message || '邮件发送失败，请稍后重试或联系管理员。',
                                {
                                    confirmText: '重试'
                                }
                            );
                        } else {
                            alert(data.message || '发送失败，请稍后重试');
                        }
                    }

                } catch (error) {
                    console.error('重发邮件错误:', error);
                    if (window.modalManager) {
                        window.modalManager.error(
                            '❌ 网络错误',
                            '网络连接失败，请检查网络连接后重试。',
                            {
                                confirmText: '重试'
                            }
                        );
                    } else {
                        alert('网络错误，请稍后重试');
                    }
                }
            });
        });
    </script>
</body>
</html>
