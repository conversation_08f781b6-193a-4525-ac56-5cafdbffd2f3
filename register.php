<?php
/**
 * 📝 现代化PHP管理系统 - 注册页面
 * 
 * 功能：用户注册界面
 * 设计：采用现代化UI设计语言，与登录页面保持一致
 * 特色：响应式布局、主题切换、表单验证、密码强度检测
 */

session_start();

// 引入系统配置
require_once 'auth/SystemConfig.php';
require_once 'auth/auth.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 检查用户是否已经登录，如果已登录则跳转到仪表盘
if (Auth::isLoggedIn()) {
    header('Location: users/index.php');
    exit;
}

// 检查注册功能是否启用
Auth::requireRegisterAccess();

// 处理注册逻辑
$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $confirm_password = trim($_POST['confirm_password'] ?? '');
    $agree_terms = isset($_POST['agree_terms']);
    
    // 基础验证
    if (empty($username)) {
        $error_message = '请输入用户名';
    } elseif (strlen($username) < 3) {
        $error_message = '用户名至少需要3个字符';
    } elseif (empty($email)) {
        $error_message = '请输入邮箱地址';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = '请输入有效的邮箱地址';
    } elseif (empty($password)) {
        $error_message = '请输入密码';
    } elseif (strlen($password) < 6) {
        $error_message = '密码至少需要6个字符';
    } elseif ($password !== $confirm_password) {
        $error_message = '两次输入的密码不一致';
    } elseif (!$agree_terms) {
        $error_message = '请同意服务条款和隐私政策';
    } else {
        // 这里应该连接数据库保存用户信息
        // 示例：简单的演示验证
        if ($username === 'admin') {
            $error_message = '用户名已存在，请选择其他用户名';
        } else {
            // 模拟注册成功
            $success_message = '注册成功！请登录您的账户';
            // 实际项目中应该：
            // 1. 密码加密存储
            // 2. 发送验证邮件
            // 3. 跳转到登录页面
            // header('Location: login.php?registered=1');
            // exit();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle('注册'); ?></title>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- SEO和社交媒体标签 -->
    <meta name="description" content="现代化PHP管理系统注册页面，快速创建您的账户">
    <meta name="keywords" content="PHP, 管理系统, 注册, 现代化设计">
    <meta name="author" content="现代化PHP管理系统">
    
    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📝</text></svg>">
</head>
<body>
    <!-- 注册容器 -->
    <div class="login-container">
        <!-- 注册卡片 -->
        <div class="login-card">
            <!-- 注册头部 -->
            <div class="login-header">
                <h1 class="login-title"><?php echo SystemConfig::getSiteName(); ?></h1>
                <p class="login-subtitle"><?php echo SystemConfig::getSiteDescription(); ?></p>
            </div>
            
            <!-- 注册表单 -->
            <div class="login-body">
                <form id="registerForm" method="POST" action="" data-validate>
                    <!-- 用户名输入框 -->
                    <div class="form-group">
                        <label for="username" class="form-label">用户名</label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            class="form-control" 
                            placeholder="请输入用户名（至少3个字符）"
                            value="<?php echo htmlspecialchars($username ?? ''); ?>"
                            autocomplete="username"
                            required
                        >
                    </div>
                    
                    <!-- 邮箱输入框 -->
                    <div class="form-group">
                        <label for="email" class="form-label">邮箱地址</label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-control" 
                            placeholder="请输入邮箱地址"
                            value="<?php echo htmlspecialchars($email ?? ''); ?>"
                            autocomplete="email"
                            required
                        >
                    </div>
                    
                    <!-- 密码输入框 -->
                    <div class="form-group">
                        <label for="password" class="form-label">密码</label>
                        <div class="password-input-wrapper">
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="form-control" 
                                placeholder="请输入密码（至少6个字符）"
                                autocomplete="new-password"
                                required
                            >
                            <button type="button" class="password-toggle" aria-label="显示/隐藏密码">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                        <!-- 密码强度指示器 -->
                        <div class="password-strength" id="passwordStrength">
                            <div class="password-strength-bar">
                                <div class="password-strength-fill"></div>
                            </div>
                            <div class="password-strength-text">密码强度：<span>弱</span></div>
                        </div>
                    </div>
                    
                    <!-- 确认密码输入框 -->
                    <div class="form-group">
                        <label for="confirm_password" class="form-label">确认密码</label>
                        <input 
                            type="password" 
                            id="confirm_password" 
                            name="confirm_password" 
                            class="form-control" 
                            placeholder="请再次输入密码"
                            autocomplete="new-password"
                            required
                        >
                    </div>
                    
                    <!-- 服务条款同意 -->
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="agree_terms" class="checkbox-input" required>
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-text">
                                我已阅读并同意 
                                <a href="#" class="text-primary hover:underline">服务条款</a> 
                                和 
                                <a href="#" class="text-primary hover:underline">隐私政策</a>
                            </span>
                        </label>
                    </div>
                    
                    <!-- 错误信息显示 -->
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-error mb-4">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="15" y1="9" x2="9" y2="15"></line>
                                <line x1="9" y1="9" x2="15" y2="15"></line>
                            </svg>
                            <span><?php echo htmlspecialchars($error_message); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <!-- 成功信息显示 -->
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success mb-4">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="20,6 9,17 4,12"></polyline>
                            </svg>
                            <span><?php echo htmlspecialchars($success_message); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <!-- 注册按钮 -->
                    <button type="submit" class="btn btn-primary w-full mb-4" data-tooltip="注册即表示您同意我们的服务条款和隐私政策">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="8.5" cy="7" r="4"></circle>
                            <line x1="20" y1="8" x2="20" y2="14"></line>
                            <line x1="23" y1="11" x2="17" y2="11"></line>
                        </svg>
                        创建账户
                    </button>
                    
                    <!-- 其他操作链接 -->
                    <div class="text-center">
                        <span class="text-sm text-muted">已有账户？</span>
                        <a href="login.php" class="text-sm text-primary hover:underline ml-1">立即登录</a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 页脚信息 -->
        <div class="page-footer">
            <p class="text-center text-sm text-muted">
                © <?php echo date('Y'); ?> <?php echo SystemConfig::getSiteName(); ?>. 保留所有权利.
            </p>
        </div>
    </div>
    
    <!-- 统一组件加载器 - 只需要引入这一个文件 -->
    <script src="assets/js/main.js"></script>
    
    <!-- 页面特定脚本 -->
    <script>
        /**
         * 页面特定的初始化逻辑
         */
        function initializeRegisterPage() {
            // 显示服务器端消息
            <?php if (!empty($error_message)): ?>
                showToast('<?php echo addslashes($error_message); ?>', 'error');
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                showToast('<?php echo addslashes($success_message); ?>', 'success');
                // 成功注册后延迟跳转到登录页面
                setTimeout(() => {
                    window.location.href = 'login.php?registered=1';
                }, 2000);
            <?php endif; ?>

            // 密码显示/隐藏切换
            initPasswordToggle();
            
            // 密码强度检测
            initPasswordStrength();
            
            // 密码确认验证
            initPasswordConfirmation();
            
            // 注册表单提交处理
            initRegisterFormSubmit();

            console.log('📝 注册页面特定功能已初始化完成');
        }
        
        // 密码显示/隐藏功能
        function initPasswordToggle() {
            const passwordInput = document.getElementById('password');
            const toggleButton = document.querySelector('.password-toggle');
            
            if (toggleButton && passwordInput) {
                toggleButton.addEventListener('click', function() {
                    const isPassword = passwordInput.type === 'password';
                    passwordInput.type = isPassword ? 'text' : 'password';
                    
                    // 更新图标
                    const icon = toggleButton.querySelector('svg');
                    if (isPassword) {
                        // 显示"隐藏"图标
                        icon.innerHTML = `
                            <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                            <line x1="1" y1="1" x2="23" y2="23"/>
                        `;
                    } else {
                        // 显示"显示"图标
                        icon.innerHTML = `
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                            <circle cx="12" cy="12" r="3"/>
                        `;
                    }
                });
            }
        }
        
        // 密码强度检测
        function initPasswordStrength() {
            const passwordInput = document.getElementById('password');
            const strengthIndicator = document.getElementById('passwordStrength');
            
            if (passwordInput && strengthIndicator) {
                const strengthBar = strengthIndicator.querySelector('.password-strength-fill');
                const strengthText = strengthIndicator.querySelector('.password-strength-text span');
                
                passwordInput.addEventListener('input', async function() {
                    const password = this.value;
                    const strength = await calculatePasswordStrength(password);

                    // 更新强度条
                    strengthBar.style.width = `${strength.percentage}%`;
                    strengthBar.className = `password-strength-fill strength-${strength.level}`;

                    // 更新文本
                    let displayText = strength.text;
                    if (strength.feedback && strength.feedback.length > 0) {
                        displayText += ` (${strength.feedback.join(', ')})`;
                    }
                    strengthText.textContent = displayText;

                    // 显示/隐藏强度指示器
                    strengthIndicator.style.display = password.length > 0 ? 'block' : 'none';
                });
            }
        }
        
        // 安全配置缓存
        let securityConfig = null;

        // 获取安全配置
        async function getSecurityConfig() {
            if (securityConfig) return securityConfig;

            try {
                const response = await fetch('auth/SystemConfig.php?action=get_security_config');
                const data = await response.json();
                if (data.success) {
                    securityConfig = data.config;
                    return securityConfig;
                }
            } catch (error) {
                console.error('获取安全配置失败:', error);
            }

            // 使用默认配置
            return {
                password_min_length: 6,
                password_complexity: {
                    require_uppercase: false,
                    require_lowercase: false,
                    require_numbers: false,
                    require_symbols: false
                }
            };
        }

        // 计算密码强度 - 使用动态配置
        async function calculatePasswordStrength(password) {
            const config = await getSecurityConfig();
            let score = 0;
            let feedback = [];

            // 基础长度检查
            if (password.length >= config.password_min_length) score += 1;
            if (password.length >= config.password_min_length + 2) score += 1;
            if (password.length >= 12) score += 1;

            // 复杂度检查
            const hasLowercase = /[a-z]/.test(password);
            const hasUppercase = /[A-Z]/.test(password);
            const hasNumbers = /[0-9]/.test(password);
            const hasSymbols = /[^A-Za-z0-9]/.test(password);

            if (hasLowercase) score += 1;
            if (hasUppercase) score += 1;
            if (hasNumbers) score += 1;
            if (hasSymbols) score += 1;

            // 检查必需的复杂度要求
            if (config.password_complexity.require_lowercase && !hasLowercase) {
                feedback.push('需要小写字母');
            }
            if (config.password_complexity.require_uppercase && !hasUppercase) {
                feedback.push('需要大写字母');
            }
            if (config.password_complexity.require_numbers && !hasNumbers) {
                feedback.push('需要数字');
            }
            if (config.password_complexity.require_symbols && !hasSymbols) {
                feedback.push('需要特殊字符');
            }

            const levels = [
                { level: 'weak', text: '弱', percentage: 20 },
                { level: 'fair', text: '一般', percentage: 40 },
                { level: 'good', text: '良好', percentage: 60 },
                { level: 'strong', text: '强', percentage: 80 },
                { level: 'very-strong', text: '很强', percentage: 100 }
            ];

            const levelIndex = Math.min(Math.floor(score / 1.4), levels.length - 1);
            return {
                ...levels[levelIndex],
                feedback: feedback,
                config: config
            };
        }
        
        // 密码确认验证
        function initPasswordConfirmation() {
            const passwordInput = document.getElementById('password');
            const confirmInput = document.getElementById('confirm_password');
            
            if (passwordInput && confirmInput) {
                function validatePasswordMatch() {
                    if (confirmInput.value && passwordInput.value !== confirmInput.value) {
                        confirmInput.setCustomValidity('密码不匹配');
                        confirmInput.classList.add('error');
                    } else {
                        confirmInput.setCustomValidity('');
                        confirmInput.classList.remove('error');
                    }
                }
                
                passwordInput.addEventListener('input', validatePasswordMatch);
                confirmInput.addEventListener('input', validatePasswordMatch);
            }
        }
        
        // 注册表单提交处理
        function initRegisterFormSubmit() {
            const registerForm = document.getElementById('registerForm');
            const submitBtn = registerForm.querySelector('button[type="submit"]');
            let captchaSessionKey = null; // 存储验证码会话密钥

            registerForm.addEventListener('submit', async function(e) {
                e.preventDefault(); // 阻止默认提交

                // 获取表单数据
                const formData = new FormData(registerForm);
                const username = formData.get('username');
                const email = formData.get('email');
                const password = formData.get('password');
                const confirmPassword = formData.get('confirm_password');

                // 基础验证
                if (!username || !email || !password || !confirmPassword) {
                    showToast('请填写所有必填字段', 'error');
                    return;
                }

                // 调试信息
                console.log('密码验证:', {
                    password: password,
                    confirmPassword: confirmPassword,
                    passwordLength: password.length,
                    confirmPasswordLength: confirmPassword.length,
                    isEqual: password === confirmPassword
                });

                if (password !== confirmPassword) {
                    showToast('两次输入的密码不一致', 'error');
                    console.error('密码不匹配:', password, '!=', confirmPassword);
                    return;
                }

                // 执行注册流程（包含验证码检查）
                await executeRegisterFlow(username, email, password);
            });

            // 执行注册流程
            async function executeRegisterFlow(username, email, password) {
                try {
                    // 检查是否需要验证码
                    const needsCaptcha = await checkCaptchaRequired('register');

                    if (needsCaptcha) {
                        // 显示验证码模态弹窗
                        const captchaResult = await showCaptchaModal();
                        if (!captchaResult.success) {
                            return; // 用户取消或验证失败
                        }
                        captchaSessionKey = captchaResult.sessionKey;
                    }

                    // 执行注册
                    await performRegister(username, email, password, captchaSessionKey);

                } catch (error) {
                    console.error('注册过程出错:', error);
                    showToast(error.message || '注册失败，请重试', 'error');
                    resetRegisterButton();
                }
            }

            // 设置注册按钮加载状态
            function setRegisterButtonLoading(loading) {
                if (loading) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = `
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="loading-spinner">
                            <path d="M21 12a9 9 0 11-6.219-8.56"/>
                        </svg>
                        创建中...
                    `;
                } else {
                    resetRegisterButton();
                }
            }

            // 重置注册按钮
            function resetRegisterButton() {
                submitBtn.disabled = false;
                submitBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="8.5" cy="7" r="4"></circle>
                        <line x1="20" y1="8" x2="20" y2="14"></line>
                        <line x1="23" y1="11" x2="17" y2="11"></line>
                    </svg>
                    创建账户
                `;
            }

            // 执行注册
            async function performRegister(username, email, password, captchaSessionKey) {
                setRegisterButtonLoading(true);

                try {
                    const registerData = {
                        username: username,
                        email: email,
                        password: password,
                        confirm_password: password, // 添加确认密码字段
                        agree_terms: true // 添加同意条款字段
                    };

                    if (captchaSessionKey) {
                        registerData.captcha_session_key = captchaSessionKey;
                    }

                    const response = await fetch('api/register.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(registerData)
                    });

                    const data = await response.json();

                    if (data.success) {
                        // 显示邮箱验证提示弹窗
                        showEmailVerificationSuccessModal(data.email_sent, data.user_email);
                    } else {
                        // 根据错误类型进行不同处理
                        const errorType = data.error_type || 'unknown_error';

                        if (errorType === 'captcha_error' || errorType === 'captcha_required') {
                            // 验证码错误：显示错误信息，刷新验证码
                            showToast(data.message || '验证码错误', 'error');
                            // 刷新验证码
                            refreshCurrentCaptcha();
                            resetRegisterButton();
                        } else {
                            // 其他错误：显示错误信息
                            showToast(data.message || '注册失败', 'error');
                            resetRegisterButton();
                        }
                    }

                } catch (error) {
                    console.error('注册请求失败:', error);
                    showToast('网络错误，请重试', 'error');
                    resetRegisterButton();
                }
            }
        }

        // 检查是否需要验证码
        async function checkCaptchaRequired(type = 'register') {
            try {
                const response = await fetch(`auth/SystemConfig.php?action=get_setting&key=security.enable_${type}_captcha`);
                const data = await response.json();
                return data.success && data.value === '1';
            } catch (error) {
                console.error('检查验证码设置失败:', error);
                return false; // 默认不需要验证码
            }
        }

        // 刷新当前验证码
        async function refreshCurrentCaptcha() {
            // 查找当前显示的验证码元素
            const captchaContainer = document.querySelector('.captcha-container');
            if (captchaContainer) {
                // 重新生成验证码内容
                await regenerateCaptchaContent(captchaContainer);
            }
        }

        // 重新生成验证码内容
        async function regenerateCaptchaContent(container) {
            try {
                // 获取验证码设置
                const captchaType = await getCaptchaSetting('captcha_type', 'math');
                const captchaDifficulty = await getCaptchaSetting('captcha_difficulty', 'medium');

                // 生成新的验证码
                const captchaResponse = await fetch('api/captcha.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'generate',
                        type: captchaType,
                        difficulty: captchaDifficulty
                    })
                });

                const captchaData = await captchaResponse.json();
                if (captchaData.success) {
                    // 更新验证码内容
                    const newContent = createCaptchaContent(captchaData);
                    container.innerHTML = newContent;

                    // 更新全局的验证码数据
                    window.currentCaptchaData = captchaData;
                }
            } catch (error) {
                console.error('刷新验证码失败:', error);
            }
        }

        // 全局刷新验证码函数
        window.refreshCaptchaFunction = async function(btn) {
            if (btn.disabled) return;

            btn.disabled = true;
            const originalHTML = btn.innerHTML;
            btn.innerHTML = `
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 4px;">
                    <path d="M21 12a9 9 0 11-6.219-8.56"/>
                </svg>
                <span>刷新中...</span>
            `;

            try {
                const container = document.querySelector('.captcha-container');
                if (container) {
                    await regenerateCaptchaContent(container);

                    // 重新聚焦输入框
                    setTimeout(() => {
                        const newAnswerInput = document.querySelector('#captcha-answer');
                        if (newAnswerInput && newAnswerInput.type !== 'hidden') {
                            newAnswerInput.focus();
                        }
                    }, 100);
                }
            } catch (error) {
                console.error('刷新验证码失败:', error);
                btn.disabled = false;
                btn.innerHTML = originalHTML;
            }
        };

        // 获取验证码设置
        async function getCaptchaSetting(key, defaultValue) {
            try {
                const response = await fetch(`auth/SystemConfig.php?action=get_setting&key=security.${key}`);
                const data = await response.json();
                return data.success ? data.value : defaultValue;
            } catch (error) {
                return defaultValue;
            }
        }

        // 显示验证码模态弹窗
        async function showCaptchaModal() {
            try {
                // 获取验证码设置
                const captchaType = await getCaptchaSetting('captcha_type', 'math');
                const captchaDifficulty = await getCaptchaSetting('captcha_difficulty', 'medium');

                // 生成验证码
                const captchaResponse = await fetch('api/captcha.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'generate',
                        type: captchaType,
                        difficulty: captchaDifficulty
                    })
                });

                const captchaData = await captchaResponse.json();
                if (!captchaData.success) {
                    throw new Error(captchaData.message || '生成验证码失败');
                }

                // 创建验证码内容
                const captchaContent = createCaptchaContent(captchaData);

                // 显示模态弹窗
                return new Promise((resolve) => {
                    showModal.custom({
                        title: '🔐 安全验证',
                        content: captchaContent,
                        buttons: [
                            { text: '取消', type: 'secondary', value: 'cancel' },
                            { text: '验证', type: 'primary', value: 'verify' }
                        ],
                        closeOnBackdrop: false,
                        closeOnEscape: false,
                        onShow: function() {
                            // 如果是滑块验证码，初始化滑块功能
                            if (captchaData.type === 'slider') {
                                initSliderCaptcha(captchaData.target_position || 75);
                            }

                            // 自动聚焦到输入框
                            const answerInput = document.querySelector('#captcha-answer');
                            if (answerInput && answerInput.type !== 'hidden') {
                                setTimeout(() => answerInput.focus(), 100);
                            }

                            // 添加回车键监听
                            if (answerInput && answerInput.type !== 'hidden') {
                                answerInput.addEventListener('keypress', function(e) {
                                    if (e.key === 'Enter') {
                                        e.preventDefault();
                                        document.querySelector('.modal-btn-primary').click();
                                    }
                                });
                            }
                        }
                    }).then(async (result) => {
                        if (result === 'cancel') {
                            resolve({ success: false });
                            return;
                        }

                        if (result === 'verify') {
                            // 获取用户输入的答案
                            const answerInput = document.querySelector('#captcha-answer');
                            const answer = answerInput ? answerInput.value.trim() : '';

                            if (!answer) {
                                showToast('请输入验证码', 'warning');
                                // 重新显示验证码弹窗
                                const retryResult = await showCaptchaModal();
                                resolve(retryResult);
                                return;
                            }

                            try {
                                // 验证答案
                                const verifyResponse = await fetch('api/captcha.api.php', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify({
                                        action: 'verify',
                                        answer: answer,
                                        session_key: captchaData.session_key
                                    })
                                });

                                const verifyData = await verifyResponse.json();
                                if (!verifyData.success) {
                                    showToast(verifyData.message || '验证码错误', 'error');
                                    // 重新显示验证码弹窗
                                    const retryResult = await showCaptchaModal();
                                    resolve(retryResult);
                                    return;
                                }

                                // 验证成功，直接继续注册流程，不显示成功提示
                                resolve({
                                    success: true,
                                    sessionKey: captchaData.session_key
                                });

                            } catch (error) {
                                console.error('验证码验证失败:', error);
                                showToast('验证失败，请重试', 'error');
                                // 重新显示验证码弹窗
                                const retryResult = await showCaptchaModal();
                                resolve(retryResult);
                            }
                        }
                    });
                });

            } catch (error) {
                console.error('验证码模态弹窗错误:', error);
                showToast(error.message || '验证码验证失败', 'error');
                return { success: false };
            }
        }

        // 创建验证码内容
        function createCaptchaContent(captchaData) {
            let content = `
                <div class="captcha-container" style="text-align: center; padding: 1.5rem;">
                    <p style="margin-bottom: 1.5rem; color: var(--text-secondary); font-size: 14px;">为了您的账户安全，请完成以下验证：</p>
            `;

            if (captchaData.type === 'math') {
                content += `
                    <div class="math-captcha" style="margin-bottom: 1.5rem;">
                        <div style="font-size: 28px; font-weight: 600; color: var(--text-primary); margin-bottom: 1rem; font-family: 'Courier New', monospace;">
                            ${captchaData.question}
                        </div>
                        <input type="text" id="captcha-answer" placeholder="请输入答案"
                               style="width: 200px; padding: 12px 16px; border: 2px solid var(--border-color); border-radius: 8px; text-align: center; font-size: 16px; background: var(--bg-secondary); color: var(--text-primary); transition: all 0.2s ease;"
                               autocomplete="off">
                    </div>
                `;
            } else if (captchaData.type === 'alphanumeric') {
                content += `
                    <div class="alphanumeric-captcha" style="margin-bottom: 1.5rem;">
                        <div style="font-size: 24px; font-weight: 600; color: var(--text-primary); margin-bottom: 1rem; letter-spacing: 4px; font-family: 'Courier New', monospace; background: var(--bg-tertiary); padding: 12px 20px; border-radius: 8px; border: 2px solid var(--border-color);">
                            ${captchaData.question}
                        </div>
                        <input type="text" id="captcha-answer" placeholder="请输入验证码"
                               style="width: 200px; padding: 12px 16px; border: 2px solid var(--border-color); border-radius: 8px; text-align: center; font-size: 16px; letter-spacing: 2px; background: var(--bg-secondary); color: var(--text-primary); transition: all 0.2s ease;"
                               autocomplete="off">
                    </div>
                `;
            } else if (captchaData.type === 'slider') {
                content += `
                    <div class="slider-captcha" style="margin-bottom: 1.5rem;">
                        <p style="margin-bottom: 1rem; color: var(--text-secondary);">${captchaData.question}</p>
                        <div style="width: 300px; height: 44px; background: var(--bg-tertiary); border: 2px solid var(--border-color); border-radius: 22px; position: relative; margin: 0 auto; overflow: hidden;">
                            <div id="slider-track" style="width: 100%; height: 100%; border-radius: 20px; position: relative; cursor: pointer;">
                                <div id="slider-thumb" style="width: 40px; height: 40px; background: var(--primary-color); border-radius: 50%; position: absolute; top: 2px; left: 2px; cursor: grab; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; box-shadow: 0 2px 8px rgba(0,0,0,0.2); transition: all 0.2s ease;">
                                    →
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="captcha-answer" value="">
                    </div>
                `;
            }

            content += `
                    <div style="margin-top: 1rem; text-align: center;">
                        <button type="button" class="refresh-captcha-btn" id="refresh-captcha-btn"
                                style="background: #f8f9fa; border: 1px solid #dee2e6; color: #6c757d; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px; transition: all 0.2s ease; margin-bottom: 1rem; user-select: none;"
                                onmouseover="this.style.background='#e9ecef'"
                                onmouseout="this.style.background='#f8f9fa'"
                                onclick="window.refreshCaptchaFunction(this)">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 4px; pointer-events: none;">
                                <polyline points="1 4 1 10 7 10"/>
                                <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                            </svg>
                            <span style="pointer-events: none;">刷新验证码</span>
                        </button>
                    </div>
                    <p style="font-size: 12px; color: var(--text-tertiary); opacity: 0.8;">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 4px;">
                            <circle cx="12" cy="12" r="10"/>
                            <polyline points="12,6 12,12 16,14"/>
                        </svg>
                        验证码有效期5分钟，请及时完成验证
                    </p>
                </div>
                <style>
                    #captcha-answer:focus {
                        border-color: var(--primary-color) !important;
                        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1) !important;
                        outline: none !important;
                    }
                    #captcha-answer:hover {
                        border-color: var(--primary-color) !important;
                    }
                    #slider-thumb:hover {
                        transform: scale(1.05);
                        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    }
                    #slider-thumb:active {
                        transform: scale(0.95);
                    }
                </style>
            `;

            return content;
        }

        // 初始化滑块验证码
        function initSliderCaptcha(targetPosition) {
            const slider = document.getElementById('slider-thumb');
            const track = document.getElementById('slider-track');
            const answerInput = document.getElementById('captcha-answer');

            if (!slider || !track || !answerInput) return;

            let isDragging = false;
            let startX = 0;
            let currentX = 0;

            // 鼠标事件
            slider.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);

            // 触摸事件（移动端支持）
            slider.addEventListener('touchstart', startDrag);
            document.addEventListener('touchmove', drag);
            document.addEventListener('touchend', endDrag);

            function startDrag(e) {
                isDragging = true;
                startX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
                slider.style.cursor = 'grabbing';
                e.preventDefault();
            }

            function drag(e) {
                if (!isDragging) return;

                currentX = (e.type === 'mousemove' ? e.clientX : e.touches[0].clientX) - startX;
                const trackWidth = track.offsetWidth - slider.offsetWidth;
                const newPosition = Math.max(0, Math.min(currentX, trackWidth));

                slider.style.left = newPosition + 'px';

                // 计算完成百分比
                const percentage = (newPosition / trackWidth) * 100;

                // 检查是否接近目标位置（允许5%的误差）
                if (Math.abs(percentage - targetPosition) <= 5) {
                    slider.style.background = 'var(--success-color)';
                    slider.innerHTML = '✓';
                    slider.style.boxShadow = '0 4px 12px rgba(var(--success-rgb), 0.4)';
                    track.style.background = 'linear-gradient(90deg, var(--success-color) 0%, var(--bg-tertiary) 100%)';
                    answerInput.value = 'success';
                } else {
                    slider.style.background = 'var(--primary-color)';
                    slider.innerHTML = '→';
                    slider.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                    track.style.background = 'var(--bg-tertiary)';
                    answerInput.value = '';
                }

                e.preventDefault();
            }

            function endDrag() {
                if (!isDragging) return;
                isDragging = false;
                slider.style.cursor = 'grab';
            }
        }

        // 显示邮箱验证成功提示弹窗
        function showEmailVerificationSuccessModal(emailSent, userEmail) {
            const content = `
                <div style="text-align: center; padding: 2rem;">
                    <div style="font-size: 4rem; margin-bottom: 1.5rem;">📧</div>
                    <h3 style="color: var(--text-primary); margin-bottom: 1rem;">注册成功！</h3>
                    ${emailSent ? `
                        <p style="color: var(--text-secondary); margin-bottom: 1.5rem; line-height: 1.6;">
                            验证邮件已发送到 <strong>${userEmail || '您的邮箱'}</strong><br>
                            请查收邮件并点击其中的验证链接完成邮箱验证。
                        </p>
                        <div style="background: var(--bg-tertiary); border-radius: 8px; padding: 1rem; margin-bottom: 1.5rem; border-left: 4px solid var(--primary-color);">
                            <p style="margin: 0; font-size: 14px; color: var(--text-secondary);">
                                <strong>📌 重要提示：</strong><br>
                                • 验证链接有效期为 30 分钟<br>
                                • 请检查垃圾邮件文件夹<br>
                                • 验证后即可正常登录系统
                            </p>
                        </div>
                    ` : `
                        <p style="color: var(--text-secondary); margin-bottom: 1.5rem; line-height: 1.6;">
                            邮件发送失败，但您的账户已创建成功。<br>
                            请联系管理员或稍后重试验证。
                        </p>
                    `}
                    <div style="margin-bottom: 1rem;">
                        <button type="button" class="btn btn-primary" onclick="goToLogin()">
                            前往登录页面
                        </button>
                    </div>
                    ${emailSent ? `
                        <div style="margin-bottom: 1rem;">
                            <button type="button" class="btn btn-secondary" onclick="resendRegisterEmail()">
                                重新发送验证邮件
                            </button>
                        </div>
                    ` : ''}
                    <p style="font-size: 12px; color: var(--text-tertiary); opacity: 0.8; margin-top: 1rem;">
                        如果长时间未收到邮件，请检查邮箱设置或联系管理员
                    </p>
                </div>
            `;

            showModal.custom({
                title: '🎉 注册成功',
                content: content,
                buttons: [],
                closeOnBackdrop: false,
                closeOnEscape: false
            });
        }

        // 前往登录页面
        function goToLogin() {
            window.location.href = 'login.php?registered=1';
        }

        // 重新发送注册验证邮件
        async function resendRegisterEmail() {
            try {
                const response = await fetch('api/verify-email.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'resend_email'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showToast('验证邮件已重新发送，请查收', 'success');
                } else {
                    showToast(data.message || '发送失败，请稍后重试', 'error');
                }

            } catch (error) {
                console.error('重发邮件错误:', error);
                showToast('网络错误，请稍后重试', 'error');
            }
        }

        // 等待统一组件系统初始化完成后执行页面特定逻辑
        document.addEventListener('DOMContentLoaded', function() {
            // 检查系统是否已初始化
            const checkSystemReady = () => {
                if (window.appManager && window.appManager.initialized) {
                    // 系统已就绪，执行页面特定初始化
                    initializeRegisterPage();
                    console.log('📝 注册页面已完全初始化');
                } else {
                    // 系统尚未就绪，继续等待
                    setTimeout(checkSystemReady, 100);
                }
            };

            checkSystemReady();
        });
    </script>
</body>
</html>
