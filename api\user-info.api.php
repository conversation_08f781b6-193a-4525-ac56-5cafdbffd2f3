<?php
/**
 * 👤 用户信息API接口
 * 
 * 功能：获取当前用户信息
 * 方法：GET
 * 权限：需要登录
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入认证类
require_once '../auth/auth.php';

try {
    // 检查用户是否已登录
    if (!Auth::isLoggedIn()) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => '请先登录',
            'redirect' => '/login.php'
        ]);
        exit;
    }
    
    // 获取当前用户信息
    $user = Auth::getCurrentUser();
    
    if ($user) {
        // 计算在线时长
        $online_duration = time() - $user['login_time'];
        $hours = floor($online_duration / 3600);
        $minutes = floor(($online_duration % 3600) / 60);
        
        // 格式化最后活动时间
        $last_activity_formatted = date('Y-m-d H:i:s', $user['last_activity']);
        $login_time_formatted = date('Y-m-d H:i:s', $user['login_time']);
        
        echo json_encode([
            'success' => true,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role'],
                'role_name' => getRoleName($user['role']),
                'login_time' => $login_time_formatted,
                'last_activity' => $last_activity_formatted,
                'online_duration' => [
                    'total_seconds' => $online_duration,
                    'hours' => $hours,
                    'minutes' => $minutes,
                    'formatted' => $hours > 0 ? "{$hours}小时{$minutes}分钟" : "{$minutes}分钟"
                ],
                'permissions' => getUserPermissions($user['role'])
            ],
            'timestamp' => time()
        ]);
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => '用户信息不存在'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器错误：' . $e->getMessage(),
        'timestamp' => time()
    ]);
}

/**
 * 获取角色名称
 */
function getRoleName($role) {
    $role_names = [
        Auth::ROLE_ADMIN => '系统管理员',
        Auth::ROLE_USER => '普通用户',
        Auth::ROLE_GUEST => '访客'
    ];
    
    return $role_names[$role] ?? '未知角色';
}

/**
 * 获取用户权限列表
 */
function getUserPermissions($role) {
    $permissions = [
        Auth::ROLE_ADMIN => [
            'dashboard.view' => '查看仪表盘',
            'users.view' => '查看用户',
            'users.create' => '创建用户',
            'users.edit' => '编辑用户',
            'users.delete' => '删除用户',
            'settings.view' => '查看设置',
            'settings.edit' => '编辑设置',
            'logs.view' => '查看日志',
            'system.manage' => '系统管理'
        ],
        Auth::ROLE_USER => [
            'dashboard.view' => '查看仪表盘',
            'profile.view' => '查看个人资料',
            'profile.edit' => '编辑个人资料'
        ],
        Auth::ROLE_GUEST => [
            'dashboard.view' => '查看仪表盘'
        ]
    ];
    
    return $permissions[$role] ?? [];
}
?>
