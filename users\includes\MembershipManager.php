<?php
/**
 * 🎯 会员管理类
 * 
 * 功能：用户分组管理、会员升级、权限控制
 * 特色：简单易用的会员管理系统
 */

require_once '../config/database.php';

class MembershipManager {
    
    private static $db = null;
    
    /**
     * 获取数据库实例
     */
    private static function getDB() {
        if (self::$db === null) {
            self::$db = Database::getInstance();
        }
        return self::$db;
    }
    
    /**
     * 获取所有用户分组
     */
    public static function getAllGroups() {
        $db = self::getDB();
        return $db->fetchAll(
            "SELECT * FROM user_groups WHERE is_active = 1 ORDER BY priority DESC, id ASC"
        );
    }
    
    /**
     * 获取默认用户分组
     */
    public static function getDefaultGroup() {
        $db = self::getDB();
        return $db->fetch(
            "SELECT * FROM user_groups WHERE is_default = 1 AND is_active = 1 LIMIT 1"
        );
    }
    
    /**
     * 根据分组key获取分组信息
     */
    public static function getGroupByKey($groupKey) {
        $db = self::getDB();
        return $db->fetch(
            "SELECT * FROM user_groups WHERE group_key = ? AND is_active = 1",
            [$groupKey]
        );
    }
    
    /**
     * 获取用户当前会员信息
     */
    public static function getUserMembership($userId) {
        $db = self::getDB();
        return $db->fetch(
            "SELECT um.*, ug.group_key, ug.group_name, ug.group_type, ug.permissions, ug.features,
                    CASE 
                        WHEN um.end_date IS NULL THEN 'permanent'
                        WHEN um.end_date > NOW() THEN 'active'
                        ELSE 'expired'
                    END as validity,
                    DATEDIFF(um.end_date, NOW()) as days_remaining
             FROM user_memberships um
             JOIN user_groups ug ON um.group_id = ug.id
             WHERE um.user_id = ? AND um.status = 'active'
             ORDER BY um.id DESC LIMIT 1",
            [$userId]
        );
    }
    
    /**
     * 检查用户是否有特定权限
     */
    public static function hasPermission($userId, $permission) {
        $membership = self::getUserMembership($userId);
        if (!$membership) {
            return false;
        }
        
        $permissions = json_decode($membership['permissions'], true);
        return isset($permissions[$permission]) && $permissions[$permission] === true;
    }
    
    /**
     * 检查用户是否有特定功能
     */
    public static function hasFeature($userId, $feature) {
        $membership = self::getUserMembership($userId);
        if (!$membership) {
            return false;
        }
        
        $features = json_decode($membership['features'], true);
        return isset($features[$feature]) && $features[$feature] !== false;
    }
    
    /**
     * 为新用户分配默认分组
     */
    public static function assignDefaultGroup($userId) {
        $defaultGroup = self::getDefaultGroup();
        if (!$defaultGroup) {
            throw new Exception('未找到默认用户分组');
        }
        
        return self::assignUserToGroup($userId, $defaultGroup['id'], 'free', null, 'auto_assign');
    }
    
    /**
     * 将用户分配到指定分组
     */
    public static function assignUserToGroup($userId, $groupId, $membershipType = 'free', $endDate = null, $reason = null) {
        $db = self::getDB();
        
        try {
            $db->beginTransaction();
            
            // 停用用户当前的会员记录
            $db->update(
                "UPDATE user_memberships SET status = 'cancelled' WHERE user_id = ? AND status = 'active'",
                [$userId]
            );
            
            // 创建新的会员记录
            $membershipId = $db->insert(
                "INSERT INTO user_memberships (user_id, group_id, membership_type, end_date, status, upgrade_reason) 
                 VALUES (?, ?, ?, ?, 'active', ?)",
                [$userId, $groupId, $membershipType, $endDate, $reason]
            );
            
            // 更新用户表的user_group字段
            $group = $db->fetch("SELECT group_key FROM user_groups WHERE id = ?", [$groupId]);
            if ($group) {
                $db->update(
                    "UPDATE users SET user_group = ? WHERE id = ?",
                    [$group['group_key'], $userId]
                );
            }
            
            $db->commit();
            return $membershipId;
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }
    
    /**
     * 升级用户会员
     */
    public static function upgradeUser($userId, $targetGroupKey, $duration = null, $operatorId = null, $reason = null) {
        $db = self::getDB();
        
        // 获取当前会员信息
        $currentMembership = self::getUserMembership($userId);
        $targetGroup = self::getGroupByKey($targetGroupKey);
        
        if (!$targetGroup) {
            throw new Exception('目标分组不存在');
        }
        
        // 计算结束时间
        $endDate = null;
        if ($duration) {
            $endDate = date('Y-m-d H:i:s', strtotime("+{$duration} days"));
        }
        
        try {
            $db->beginTransaction();
            
            // 分配新分组
            $membershipId = self::assignUserToGroup($userId, $targetGroup['id'], 'paid', $endDate, $reason);
            
            // 记录升级日志
            $db->insert(
                "INSERT INTO membership_logs (user_id, action_type, from_group_id, to_group_id, from_end_date, to_end_date, reason, operator_id, operator_type, ip_address) 
                 VALUES (?, 'upgrade', ?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    $userId,
                    $currentMembership ? $currentMembership['group_id'] : null,
                    $targetGroup['id'],
                    $currentMembership ? $currentMembership['end_date'] : null,
                    $endDate,
                    $reason,
                    $operatorId,
                    $operatorId ? 'admin' : 'system',
                    $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
                ]
            );
            
            $db->commit();
            return $membershipId;
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }
    
    /**
     * 续费用户会员
     */
    public static function renewMembership($userId, $duration, $operatorId = null, $reason = null) {
        $db = self::getDB();
        
        $membership = self::getUserMembership($userId);
        if (!$membership) {
            throw new Exception('用户没有有效的会员记录');
        }
        
        // 计算新的结束时间
        $currentEndDate = $membership['end_date'] ? strtotime($membership['end_date']) : time();
        $newEndDate = date('Y-m-d H:i:s', $currentEndDate + ($duration * 24 * 3600));
        
        try {
            $db->beginTransaction();
            
            // 更新会员结束时间
            $db->update(
                "UPDATE user_memberships SET end_date = ?, updated_at = NOW() WHERE id = ?",
                [$newEndDate, $membership['id']]
            );
            
            // 记录续费日志
            $db->insert(
                "INSERT INTO membership_logs (user_id, action_type, to_group_id, from_end_date, to_end_date, reason, operator_id, operator_type, ip_address) 
                 VALUES (?, 'renew', ?, ?, ?, ?, ?, ?, ?)",
                [
                    $userId,
                    $membership['group_id'],
                    $membership['end_date'],
                    $newEndDate,
                    $reason,
                    $operatorId,
                    $operatorId ? 'admin' : 'system',
                    $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
                ]
            );
            
            $db->commit();
            return true;
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }
    
    /**
     * 获取用户会员历史
     */
    public static function getUserMembershipHistory($userId, $limit = 10) {
        $db = self::getDB();
        return $db->fetchAll(
            "SELECT ml.*, ug1.group_name as from_group_name, ug2.group_name as to_group_name,
                    u.username as operator_name
             FROM membership_logs ml
             LEFT JOIN user_groups ug1 ON ml.from_group_id = ug1.id
             LEFT JOIN user_groups ug2 ON ml.to_group_id = ug2.id
             LEFT JOIN users u ON ml.operator_id = u.id
             WHERE ml.user_id = ?
             ORDER BY ml.created_at DESC
             LIMIT ?",
            [$userId, $limit]
        );
    }
    
    /**
     * 检查并处理过期会员
     */
    public static function processExpiredMemberships() {
        $db = self::getDB();
        
        try {
            $db->beginTransaction();
            
            // 获取过期的会员
            $expiredMemberships = $db->fetchAll(
                "SELECT um.*, u.username 
                 FROM user_memberships um
                 JOIN users u ON um.user_id = u.id
                 WHERE um.status = 'active' 
                 AND um.end_date IS NOT NULL 
                 AND um.end_date <= NOW()"
            );
            
            $defaultGroup = self::getDefaultGroup();
            $processedCount = 0;
            
            foreach ($expiredMemberships as $membership) {
                // 将过期会员设为过期状态
                $db->update(
                    "UPDATE user_memberships SET status = 'expired' WHERE id = ?",
                    [$membership['id']]
                );
                
                // 分配到默认分组
                if ($defaultGroup) {
                    self::assignUserToGroup(
                        $membership['user_id'], 
                        $defaultGroup['id'], 
                        'free', 
                        null, 
                        'membership_expired'
                    );
                }
                
                // 记录过期日志
                $db->insert(
                    "INSERT INTO membership_logs (user_id, action_type, from_group_id, to_group_id, reason, operator_type, ip_address) 
                     VALUES (?, 'expire', ?, ?, 'automatic_expiry', 'system', '127.0.0.1')",
                    [
                        $membership['user_id'],
                        $membership['group_id'],
                        $defaultGroup ? $defaultGroup['id'] : null
                    ]
                );
                
                $processedCount++;
            }
            
            $db->commit();
            return $processedCount;
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }
    
    /**
     * 获取会员统计信息
     */
    public static function getMembershipStats() {
        $db = self::getDB();
        
        $stats = [];
        
        // 按分组统计
        $stats['by_group'] = $db->fetchAll(
            "SELECT ug.group_name, ug.group_type, COUNT(um.id) as count
             FROM user_groups ug
             LEFT JOIN user_memberships um ON ug.id = um.group_id AND um.status = 'active'
             WHERE ug.is_active = 1
             GROUP BY ug.id, ug.group_name, ug.group_type
             ORDER BY ug.priority DESC"
        );
        
        // 按类型统计
        $stats['by_type'] = $db->fetchAll(
            "SELECT membership_type, COUNT(*) as count
             FROM user_memberships 
             WHERE status = 'active'
             GROUP BY membership_type"
        );
        
        // 即将过期的会员
        $stats['expiring_soon'] = $db->fetch(
            "SELECT COUNT(*) as count
             FROM user_memberships 
             WHERE status = 'active' 
             AND end_date IS NOT NULL 
             AND end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)"
        )['count'];
        
        return $stats;
    }

    /**
     * 获取用户列表（包含会员信息）
     */
    public static function getUsersWithMembership($page = 1, $limit = 20, $search = '', $groupFilter = '') {
        $db = self::getDB();

        $offset = ($page - 1) * $limit;
        $whereConditions = [];
        $params = [];

        if ($search) {
            $whereConditions[] = "(u.username LIKE ? OR u.email LIKE ? OR u.nickname LIKE ?)";
            $searchTerm = "%{$search}%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }

        if ($groupFilter) {
            $whereConditions[] = "ug.group_key = ?";
            $params[] = $groupFilter;
        }

        $whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // 获取用户列表
        $users = $db->fetchAll(
            "SELECT u.id, u.username, u.email, u.nickname, u.role, u.status, u.created_at,
                    ug.group_name, ug.group_type, ug.group_key,
                    um.membership_type, um.start_date, um.end_date, um.status as membership_status,
                    CASE
                        WHEN um.end_date IS NULL THEN 'permanent'
                        WHEN um.end_date > NOW() THEN 'active'
                        ELSE 'expired'
                    END as validity,
                    DATEDIFF(um.end_date, NOW()) as days_remaining
             FROM users u
             LEFT JOIN user_memberships um ON u.id = um.user_id AND um.status = 'active'
             LEFT JOIN user_groups ug ON um.group_id = ug.id
             {$whereClause}
             ORDER BY u.created_at DESC
             LIMIT ? OFFSET ?",
            array_merge($params, [$limit, $offset])
        );

        // 获取总数
        $total = $db->fetch(
            "SELECT COUNT(DISTINCT u.id) as count
             FROM users u
             LEFT JOIN user_memberships um ON u.id = um.user_id AND um.status = 'active'
             LEFT JOIN user_groups ug ON um.group_id = ug.id
             {$whereClause}",
            $params
        )['count'];

        return [
            'users' => $users,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
}
?>
