<?php
/**
 * 👥 现代化PHP管理系统 - 用户管理页面
 * 
 * 功能：用户管理，包括用户列表、添加、编辑、删除等功能
 * 权限：仅管理员可访问
 * 设计：三段式布局（头部导航栏、主内容区域、页脚）
 */

// 引入认证中间件
require_once '../auth/Auth.php';
require_once '../auth/SystemConfig.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 要求用户登录
Auth::requireLogin('../login.php');

// 检查管理员权限
if (!Auth::hasRole(Auth::ROLE_ADMIN)) {
    header('Location: index.php?error=permission_denied');
    exit;
}

// 获取当前用户信息
$current_user = Auth::getCurrentUser();

// 引入数据库配置
require_once '../config/database.php';
$db = Database::getInstance();

// 分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 15;
$offset = ($page - 1) * $per_page;

// 搜索和筛选参数
$search = trim($_GET['search'] ?? '');
$role_filter = $_GET['role'] ?? '';
$status_filter = $_GET['status'] ?? '';
$email_verified_filter = $_GET['email_verified'] ?? '';

// 构建查询条件
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(username LIKE ? OR email LIKE ? OR nickname LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if (!empty($role_filter)) {
    $where_conditions[] = "role = ?";
    $params[] = $role_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if ($email_verified_filter !== '') {
    $where_conditions[] = "email_verified = ?";
    $params[] = $email_verified_filter === '1' ? 1 : 0;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// 获取用户总数
$total_sql = "SELECT COUNT(*) as total FROM users {$where_clause}";
$total_result = $db->fetch($total_sql, $params);
$total_users = $total_result['total'];
$total_pages = ceil($total_users / $per_page);

// 获取用户列表
$users_sql = "SELECT id, username, email, nickname, role, user_group, status, email_verified, 
                     last_login_time, last_login_ip, login_count, created_at, updated_at 
              FROM users {$where_clause} 
              ORDER BY created_at DESC 
              LIMIT {$per_page} OFFSET {$offset}";
$users = $db->fetchAll($users_sql, $params);

// 获取统计信息
$stats = [
    'total' => $db->fetch("SELECT COUNT(*) as count FROM users")['count'],
    'verified' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE email_verified = 1")['count'],
    'unverified' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE email_verified = 0")['count'],
    'active' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE status = 'active'")['count']
];

// 角色和状态选项
$roles = [
    'admin' => '管理员',
    'user' => '普通用户',
    'guest' => '访客'
];

$statuses = [
    'active' => '正常',
    'inactive' => '未激活',
    'banned' => '已禁用'
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle('用户管理'); ?></title>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- SEO和社交媒体标签 -->
    <meta name="description" content="用户管理页面，管理系统用户账户">
    <meta name="keywords" content="用户管理, 账户管理, 权限管理, 系统管理">
    <meta name="author" content="现代化PHP管理系统">
    
    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>👥</text></svg>">

    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="users-page">
    <!-- 仪表盘容器 -->
    <div class="dashboard-container">
        <!-- 头部导航栏 -->
        <?php include 'includes/header.php'; ?>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                            <circle cx="9" cy="7" r="4"/>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                        </svg>
                        用户管理
                    </h1>
                    <p class="page-subtitle">管理系统用户账户、权限和状态</p>
                </div>

                <!-- 操作工具栏 -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <!-- 搜索框 -->
                        <div class="search-box">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="M21 21l-4.35-4.35"/>
                            </svg>
                            <input type="text" id="searchInput" placeholder="搜索用户名、邮箱或昵称..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        
                        <!-- 筛选器 -->
                        <div class="filter-group">
                            <select id="roleFilter" class="filter-select">
                                <option value="">所有角色</option>
                                <?php foreach ($roles as $role => $role_name): ?>
                                <option value="<?php echo $role; ?>" <?php echo ($role_filter === $role) ? 'selected' : ''; ?>>
                                    <?php echo $role_name; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                            
                            <select id="statusFilter" class="filter-select">
                                <option value="">所有状态</option>
                                <?php foreach ($statuses as $status => $status_name): ?>
                                <option value="<?php echo $status; ?>" <?php echo ($status_filter === $status) ? 'selected' : ''; ?>>
                                    <?php echo $status_name; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>

                            <select id="emailVerifiedFilter" class="filter-select">
                                <option value="">邮箱验证状态</option>
                                <option value="1" <?php echo ($email_verified_filter === '1') ? 'selected' : ''; ?>>已验证</option>
                                <option value="0" <?php echo ($email_verified_filter === '0') ? 'selected' : ''; ?>>未验证</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="toolbar-right">
                        <!-- 清理未验证账户按钮 -->
                        <button class="btn btn-warning" onclick="showCleanupUnverifiedModal()" title="清理未验证账户">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 6h18"/>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                                <line x1="10" y1="11" x2="10" y2="17"/>
                                <line x1="14" y1="11" x2="14" y2="17"/>
                            </svg>
                            清理未验证
                        </button>

                        <!-- 添加用户按钮 -->
                        <button class="btn btn-primary" onclick="showAddUserModal()">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <line x1="19" y1="8" x2="19" y2="14"/>
                                <line x1="22" y1="11" x2="16" y2="11"/>
                            </svg>
                            添加用户
                        </button>
                    </div>
                </div>

                <!-- 统计信息 - 用户管理专用设计 -->
                <div class="stats-grid">
                    <div class="card-google card-blue">
                        <div class="card-content">
                            <div class="card-left">
                                <div class="card-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="card-title">总用户数</div>
                            </div>
                            <div class="card-right">
                                <div class="card-value" id="total-users"><?php echo number_format($stats['total']); ?></div>
                                <div class="card-description">系统注册用户总数</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-google card-green">
                        <div class="card-content">
                            <div class="card-left">
                                <div class="card-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="card-title">已验证邮箱</div>
                            </div>
                            <div class="card-right">
                                <div class="card-value" id="verified-users"><?php echo number_format($stats['verified']); ?></div>
                                <div class="card-description">邮箱验证完成用户</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-google card-orange">
                        <div class="card-content">
                            <div class="card-left">
                                <div class="card-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="card-title">未验证邮箱</div>
                            </div>
                            <div class="card-right">
                                <div class="card-value" id="unverified-users"><?php echo number_format($stats['unverified']); ?></div>
                                <div class="card-description">待验证邮箱用户</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-google card-red">
                        <div class="card-content">
                            <div class="card-left">
                                <div class="card-icon">
                                    <i class="fas fa-user-check"></i>
                                </div>
                                <div class="card-title">活跃用户</div>
                            </div>
                            <div class="card-right">
                                <div class="card-value" id="active-users"><?php echo number_format($stats['active']); ?></div>
                                <div class="card-description">状态正常的用户</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="users-table-container">
                    <div class="table-header">
                        <h2 class="table-title">用户列表</h2>
                        <div class="table-info">
                            共 <?php echo number_format($total_users); ?> 个用户
                        </div>
                    </div>
                    
                    <?php if (empty($users)): ?>
                    <div class="empty-state">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                            <circle cx="9" cy="7" r="4"/>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                        </svg>
                        <h3>暂无用户</h3>
                        <p>没有找到符合条件的用户</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="users-table">
                            <thead>
                                <tr>
                                    <th>用户信息</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>最后登录</th>
                                    <th>注册时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="user-profile-card">
                                            <div class="user-avatar-wrapper">
                                                <div class="user-avatar">
                                                    <?php if (!empty($user['avatar'])): ?>
                                                    <img src="<?php echo htmlspecialchars($user['avatar']); ?>" alt="头像">
                                                    <?php else: ?>
                                                    <div class="avatar-placeholder">
                                                        <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="user-info-wrapper">
                                                <div class="user-name-row">
                                                    <h4 class="user-display-name">
                                                        <?php echo htmlspecialchars($user['nickname'] ?: $user['username']); ?>
                                                    </h4>
                                                    <span class="user-handle">@<?php echo htmlspecialchars($user['username']); ?></span>
                                                    <?php if ($user['email_verified']): ?>
                                                    <span class="verified-icon" title="邮箱已验证">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                            <polyline points="20,6 9,17 4,12"/>
                                                        </svg>
                                                    </span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="user-details-row">
                                                    <span class="user-contact"><?php echo htmlspecialchars($user['email']); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="role-badge role-<?php echo $user['role']; ?>">
                                            <?php echo $roles[$user['role']] ?? $user['role']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $user['status']; ?>">
                                            <?php echo $statuses[$user['status']] ?? $user['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($user['last_login_time']): ?>
                                        <div class="login-info">
                                            <div class="login-time"><?php echo date('Y-m-d H:i', strtotime($user['last_login_time'])); ?></div>
                                            <div class="login-ip"><?php echo htmlspecialchars($user['last_login_ip']); ?></div>
                                        </div>
                                        <?php else: ?>
                                        <span class="text-muted">从未登录</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="date-info">
                                            <?php echo date('Y-m-d', strtotime($user['created_at'])); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon" onclick="editUser(<?php echo $user['id']; ?>)" title="编辑用户">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                                </svg>
                                            </button>
                                            
                                            <?php if ($user['status'] === 'active'): ?>
                                            <button class="btn-icon warning" onclick="toggleUserStatus(<?php echo $user['id']; ?>, 'banned')" title="禁用用户">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <circle cx="12" cy="12" r="10"/>
                                                    <line x1="4.93" y1="4.93" x2="19.07" y2="19.07"/>
                                                </svg>
                                            </button>
                                            <?php else: ?>
                                            <button class="btn-icon success" onclick="toggleUserStatus(<?php echo $user['id']; ?>, 'active')" title="启用用户">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </button>
                                            <?php endif; ?>
                                            
                                            <?php if ($user['id'] != $current_user['id']): ?>
                                            <button class="btn-icon error" onclick="deleteUser(<?php echo $user['id']; ?>)" title="删除用户">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <polyline points="3,6 5,6 21,6"/>
                                                    <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                                                    <line x1="10" y1="11" x2="10" y2="17"/>
                                                    <line x1="14" y1="11" x2="14" y2="17"/>
                                                </svg>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php
                        $query_params = $_GET;
                        unset($query_params['page']);
                        $base_url = '?' . http_build_query($query_params);
                        $base_url = $base_url === '?' ? '?' : $base_url . '&';
                        ?>
                        
                        <?php if ($page > 1): ?>
                        <a href="<?php echo $base_url; ?>page=<?php echo $page - 1; ?>" class="pagination-btn">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                            上一页
                        </a>
                        <?php endif; ?>
                        
                        <span class="pagination-info">
                            第 <?php echo $page; ?> 页，共 <?php echo $total_pages; ?> 页
                        </span>
                        
                        <?php if ($page < $total_pages): ?>
                        <a href="<?php echo $base_url; ?>page=<?php echo $page + 1; ?>" class="pagination-btn">
                            下一页
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"/>
                            </svg>
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </main>
        
        <!-- 页脚 -->
        <?php include 'includes/footer.php'; ?>
    </div>

    <!-- 用户管理页面脚本 -->
    <script>
        /**
         * 用户管理页面初始化
         */
        function initializeUsers() {
            console.log('👥 用户管理页面已加载');

            // 初始化统计卡片动画
            initializeStatsCards();

            // 初始化搜索和筛选
            initializeSearchAndFilter();

            // 初始化表格功能
            initializeTableFeatures();
        }

        /**
         * 数字动画函数
         */
        function animateNumber(element, start, end, duration) {
            const startTime = performance.now();
            const startValue = parseInt(start);
            const endValue = parseInt(end);

            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 使用缓动函数
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.floor(startValue + (endValue - startValue) * easeOutQuart);

                element.textContent = currentValue.toLocaleString();

                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }

            requestAnimationFrame(update);
        }

        /**
         * 初始化统计卡片动画
         */
        function initializeStatsCards() {
            const cards = document.querySelectorAll('.card-google');

            // 卡片入场动画
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px) scale(0.95)';

                setTimeout(() => {
                    card.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0) scale(1)';
                }, index * 150);
            });

            // 数字动画
            setTimeout(() => {
                const values = document.querySelectorAll('.card-value');
                values.forEach((value, index) => {
                    const finalValue = value.textContent.replace(/,/g, '');

                    setTimeout(() => {
                        animateNumber(value, 0, finalValue, 2000);
                    }, index * 200);
                });
            }, 800);

            // 趋势标签动画
            setTimeout(() => {
                const trends = document.querySelectorAll('.card-trend');
                trends.forEach((trend, index) => {
                    trend.style.opacity = '0';
                    trend.style.transform = 'translateX(-20px)';

                    setTimeout(() => {
                        trend.style.transition = 'all 0.5s ease';
                        trend.style.opacity = '1';
                        trend.style.transform = 'translateX(0)';
                    }, index * 100);
                });
            }, 2000);
        }

        /**
         * 初始化搜索和筛选功能
         */
        function initializeSearchAndFilter() {
            const searchInput = document.getElementById('searchInput');
            const roleFilter = document.getElementById('roleFilter');
            const statusFilter = document.getElementById('statusFilter');
            const emailVerifiedFilter = document.getElementById('emailVerifiedFilter');

            // 搜索防抖
            let searchTimeout;
            searchInput?.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    applyFilters();
                }, 500);
            });

            // 筛选器变化
            roleFilter?.addEventListener('change', applyFilters);
            statusFilter?.addEventListener('change', applyFilters);
            emailVerifiedFilter?.addEventListener('change', applyFilters);
        }

        /**
         * 应用搜索和筛选
         */
        function applyFilters() {
            const search = document.getElementById('searchInput')?.value || '';
            const role = document.getElementById('roleFilter')?.value || '';
            const status = document.getElementById('statusFilter')?.value || '';
            const emailVerified = document.getElementById('emailVerifiedFilter')?.value || '';

            const params = new URLSearchParams();
            if (search) params.set('search', search);
            if (role) params.set('role', role);
            if (status) params.set('status', status);
            if (emailVerified) params.set('email_verified', emailVerified);

            const url = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
            window.location.href = url;
        }

        /**
         * 初始化表格功能
         */
        function initializeTableFeatures() {
            // 添加表格行悬停效果
            const tableRows = document.querySelectorAll('.users-table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'var(--bg-hover)';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });
        }

        /**
         * 显示添加用户模态框
         */
        function showAddUserModal() {
            const formHtml = `
                <form id="addUserForm" class="user-form">
                    <div class="form-section">
                        <h4 class="section-title">基本信息</h4>

                        <div class="form-group">
                            <label for="add_username">用户名 *</label>
                            <input type="text" id="add_username" name="username" required
                                   placeholder="请输入用户名" autocomplete="off">
                        </div>

                        <div class="form-group">
                            <label for="add_email">邮箱 *</label>
                            <input type="email" id="add_email" name="email" required
                                   placeholder="请输入邮箱地址" autocomplete="off">
                        </div>

                        <div class="form-group">
                            <label for="add_nickname">昵称</label>
                            <input type="text" id="add_nickname" name="nickname"
                                   placeholder="请输入昵称（可选）">
                        </div>
                    </div>

                    <div class="form-section">
                        <h4 class="section-title">账户设置</h4>

                        <div class="form-group">
                            <label for="add_role">角色 *</label>
                            <select id="add_role" name="role" required>
                                <option value="user">普通用户</option>
                                <option value="admin">管理员</option>
                                <option value="guest">访客</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="add_status">状态 *</label>
                            <select id="add_status" name="status" required>
                                <option value="active">正常</option>
                                <option value="inactive">未激活</option>
                                <option value="banned">已禁用</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="add_email_verified">邮箱验证状态 *</label>
                            <select id="add_email_verified" name="email_verified" required>
                                <option value="0">未验证</option>
                                <option value="1">已验证</option>
                            </select>
                            <small class="form-help">设置用户的邮箱验证状态</small>
                        </div>

                        <div class="form-group">
                            <label for="add_password">密码 *</label>
                            <input type="password" id="add_password" name="password" required
                                   placeholder="请输入密码" autocomplete="new-password">
                        </div>

                        <div class="form-group">
                            <label for="add_confirm_password">确认密码 *</label>
                            <input type="password" id="add_confirm_password" name="confirm_password" required
                                   placeholder="请再次输入密码" autocomplete="new-password">
                        </div>
                    </div>
                </form>
            `;

            if (window.showModal && typeof window.showModal.custom === 'function') {
                window.showModal.custom({
                    title: '添加用户',
                    content: formHtml,
                    maxWidth: 'large',
                    buttons: [
                        { text: '取消', type: 'secondary', action: 'reject' },
                        { text: '添加用户', type: 'primary', action: () => submitAddUser() }
                    ]
                });
            } else {
                alert('模态框组件未加载，请刷新页面重试');
            }
        }

        /**
         * 提交添加用户表单
         */
        async function submitAddUser() {
            const form = document.getElementById('addUserForm');
            if (!form) return;

            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            // 表单验证
            if (!data.username || !data.email || !data.password || !data.confirm_password) {
                showToast('请填写所有必填字段', 'error');
                return;
            }

            if (data.password !== data.confirm_password) {
                showToast('两次输入的密码不一致', 'error');
                return;
            }

            if (data.password.length < 6) {
                showToast('密码长度至少6位', 'error');
                return;
            }

            try {
                const response = await fetch('../api/users.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'create',
                        ...data
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showToast('用户添加成功', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showToast(result.message || '添加用户失败', 'error');
                }
            } catch (error) {
                console.error('添加用户错误:', error);
                showToast('网络错误，请重试', 'error');
            }
        }

        /**
         * 编辑用户
         */
        async function editUser(userId) {
            try {
                // 获取用户信息
                const response = await fetch(`../api/users.api.php?action=get&id=${userId}`);
                const result = await response.json();

                if (!result.success) {
                    showToast(result.message || '获取用户信息失败', 'error');
                    return;
                }

                const user = result.user;

                const formHtml = `
                    <form id="editUserForm" class="user-form">
                        <input type="hidden" name="id" value="${user.id}">

                        <div class="form-section">
                            <h4 class="section-title">基本信息</h4>

                            <div class="form-group">
                                <label for="edit_username">用户名 *</label>
                                <input type="text" id="edit_username" name="username" required
                                       value="${escapeHtml(user.username)}" readonly>
                                <small class="form-help">用户名不可修改</small>
                            </div>

                            <div class="form-group">
                                <label for="edit_email">邮箱 *</label>
                                <input type="email" id="edit_email" name="email" required
                                       value="${escapeHtml(user.email)}">
                            </div>

                            <div class="form-group">
                                <label for="edit_nickname">昵称</label>
                                <input type="text" id="edit_nickname" name="nickname"
                                       value="${escapeHtml(user.nickname || '')}">
                            </div>
                        </div>

                        <div class="form-section">
                            <h4 class="section-title">账户设置</h4>

                            <div class="form-group">
                                <label for="edit_role">角色 *</label>
                                <select id="edit_role" name="role" required>
                                    <option value="user" ${user.role === 'user' ? 'selected' : ''}>普通用户</option>
                                    <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>管理员</option>
                                    <option value="guest" ${user.role === 'guest' ? 'selected' : ''}>访客</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="edit_status">状态 *</label>
                                <select id="edit_status" name="status" required>
                                    <option value="active" ${user.status === 'active' ? 'selected' : ''}>正常</option>
                                    <option value="inactive" ${user.status === 'inactive' ? 'selected' : ''}>未激活</option>
                                    <option value="banned" ${user.status === 'banned' ? 'selected' : ''}>已禁用</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="edit_email_verified">邮箱验证状态 *</label>
                                <select id="edit_email_verified" name="email_verified" required>
                                    <option value="0" ${user.email_verified == 0 ? 'selected' : ''}>未验证</option>
                                    <option value="1" ${user.email_verified == 1 ? 'selected' : ''}>已验证</option>
                                </select>
                                <small class="form-help">修改用户的邮箱验证状态</small>
                            </div>
                        </div>

                        <div class="form-section">
                            <h4 class="section-title">重置密码（可选）</h4>

                            <div class="form-group">
                                <label for="edit_password">新密码</label>
                                <input type="password" id="edit_password" name="password"
                                       placeholder="留空则不修改密码">
                            </div>

                            <div class="form-group">
                                <label for="edit_confirm_password">确认新密码</label>
                                <input type="password" id="edit_confirm_password" name="confirm_password"
                                       placeholder="请再次输入新密码">
                            </div>
                        </div>
                    </form>
                `;

                if (window.showModal && typeof window.showModal.custom === 'function') {
                    window.showModal.custom({
                        title: '编辑用户',
                        content: formHtml,
                        maxWidth: 'large',
                        buttons: [
                            { text: '取消', type: 'secondary', action: 'reject' },
                            { text: '保存修改', type: 'primary', action: () => submitEditUser() }
                        ]
                    });
                }
            } catch (error) {
                console.error('获取用户信息错误:', error);
                showToast('网络错误，请重试', 'error');
            }
        }

        /**
         * 提交编辑用户表单
         */
        async function submitEditUser() {
            const form = document.getElementById('editUserForm');
            if (!form) return;

            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            // 表单验证
            if (!data.email) {
                showToast('请输入邮箱地址', 'error');
                return;
            }

            if (data.password && data.password !== data.confirm_password) {
                showToast('两次输入的密码不一致', 'error');
                return;
            }

            if (data.password && data.password.length < 6) {
                showToast('密码长度至少6位', 'error');
                return;
            }

            try {
                const response = await fetch('../api/users.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'update',
                        ...data
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showToast('用户信息更新成功', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showToast(result.message || '更新用户信息失败', 'error');
                }
            } catch (error) {
                console.error('更新用户信息错误:', error);
                showToast('网络错误，请重试', 'error');
            }
        }

        /**
         * 切换用户状态
         */
        async function toggleUserStatus(userId, newStatus) {
            const statusNames = {
                'active': '启用',
                'banned': '禁用',
                'inactive': '设为未激活'
            };

            const actionName = statusNames[newStatus] || '修改状态';

            if (window.showModal && typeof window.showModal.confirm === 'function') {
                try {
                    await window.showModal.confirm(
                        '确认操作',
                        `确定要${actionName}这个用户吗？`
                    );

                    const response = await fetch('../api/users.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'toggle_status',
                            id: userId,
                            status: newStatus
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        showToast(`用户${actionName}成功`, 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showToast(result.message || `${actionName}失败`, 'error');
                    }
                } catch (error) {
                    if (error !== false) { // 用户没有取消
                        console.error('切换用户状态错误:', error);
                        showToast('网络错误，请重试', 'error');
                    }
                }
            }
        }

        /**
         * 删除用户
         */
        async function deleteUser(userId) {
            if (window.showModal && typeof window.showModal.confirm === 'function') {
                try {
                    await window.showModal.confirm(
                        '确认删除',
                        '确定要删除这个用户吗？此操作不可撤销！'
                    );

                    const response = await fetch('../api/users.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'delete',
                            id: userId
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        showToast('用户删除成功', 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showToast(result.message || '删除用户失败', 'error');
                    }
                } catch (error) {
                    if (error !== false) { // 用户没有取消
                        console.error('删除用户错误:', error);
                        showToast('网络错误，请重试', 'error');
                    }
                }
            }
        }

        /**
         * HTML转义函数
         */
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        /**
         * 显示清理未验证账户确认弹窗
         */
        function showCleanupUnverifiedModal() {
            // 首先获取未验证账户数量
            fetch('../api/users.api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_unverified_count'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const count = data.count;
                    if (count === 0) {
                        showToast('没有未验证的账户需要清理', 'info');
                        return;
                    }

                    const content = `
                        <div style="text-align: center; padding: 2rem;">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">⚠️</div>
                            <h3 style="color: var(--text-primary); margin-bottom: 1rem;">清理未验证账户</h3>
                            <p style="color: var(--text-secondary); margin-bottom: 1.5rem; line-height: 1.6;">
                                系统检测到 <strong style="color: var(--warning-color);">${count}</strong> 个未验证邮箱的账户。<br>
                                这些账户将被永久删除，此操作不可撤销。
                            </p>
                            <div style="background: var(--bg-tertiary); border-radius: 8px; padding: 1rem; margin-bottom: 1.5rem; border-left: 4px solid var(--warning-color);">
                                <p style="margin: 0; font-size: 14px; color: var(--text-secondary);">
                                    <strong>⚠️ 注意：</strong><br>
                                    • 将删除所有邮箱未验证的用户账户<br>
                                    • 管理员账户不会被删除<br>
                                    • 此操作无法撤销，请谨慎操作
                                </p>
                            </div>
                            <p style="font-size: 12px; color: var(--text-tertiary); opacity: 0.8;">
                                确定要继续清理这些未验证账户吗？
                            </p>
                        </div>
                    `;

                    if (window.showModal && typeof window.showModal.custom === 'function') {
                        window.showModal.custom({
                            title: '🗑️ 清理未验证账户',
                            content: content,
                            buttons: [
                                { text: '取消', type: 'secondary', value: 'cancel' },
                                { text: '确认清理', type: 'danger', value: 'confirm' }
                            ],
                            closeOnBackdrop: false,
                            closeOnEscape: true
                        }).then(result => {
                            if (result === 'confirm') {
                                performCleanupUnverified();
                            }
                        });
                    } else {
                        if (confirm(`确定要清理 ${count} 个未验证账户吗？此操作不可撤销！`)) {
                            performCleanupUnverified();
                        }
                    }
                } else {
                    showToast('获取未验证账户数量失败：' + (data.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('获取未验证账户数量失败:', error);
                showToast('网络错误，请稍后重试', 'error');
            });
        }

        /**
         * 执行清理未验证账户
         */
        function performCleanupUnverified() {
            fetch('../api/users.api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'cleanup_unverified'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`成功清理了 ${data.deleted_count} 个未验证账户`, 'success');
                    // 刷新页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showToast('清理失败：' + (data.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('清理未验证账户失败:', error);
                showToast('网络错误，请稍后重试', 'error');
            });
        }

        /**
         * 显示提示消息
         */
        function showToast(message, type = 'info') {
            // 优先使用 toastManager
            if (window.toastManager && typeof window.toastManager.show === 'function') {
                window.toastManager.show(message, type);
                return;
            }

            // 备用方案：使用全局 showToast（但要避免递归）
            if (window.showToast && typeof window.showToast === 'function' && window.showToast !== arguments.callee) {
                window.showToast(message, type);
                return;
            }

            // 最后备用方案：使用 alert
            console.warn('Toast组件未加载，使用alert显示消息:', message);
            alert(`[${type.toUpperCase()}] ${message}`);
        }

        // 当页面加载完成时初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeUsers);
        } else {
            initializeUsers();
        }
    </script>
