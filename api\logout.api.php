<?php
/**
 * 🚪 登出API接口
 * 
 * 功能：处理用户登出请求
 * 方法：POST
 * 权限：需要登录
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求'
    ]);
    exit;
}

// 引入认证类
require_once '../auth/Auth.php';

try {
    // 检查用户是否已登录
    if (!Auth::isLoggedIn()) {
        echo json_encode([
            'success' => true,
            'message' => '您已经处于登出状态',
            'redirect' => '/login.php'
        ]);
        exit;
    }
    
    // 获取当前用户信息（用于日志记录）
    $user = Auth::getCurrentUser();
    $username = $user ? $user['username'] : 'unknown';
    
    // 执行登出
    $result = Auth::logout();
    
    if ($result['success']) {
        // 记录登出日志
        error_log("用户登出: {$username} - " . date('Y-m-d H:i:s'));
        
        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'redirect' => '/login.php',
            'timestamp' => time()
        ]);
    } else {
        throw new Exception('登出失败');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器错误：' . $e->getMessage(),
        'timestamp' => time()
    ]);
}
?>
