<?php
/**
 * 📊 现代化PHP管理系统 - 日志查看页面
 * 
 * 功能：查看操作日志和登录日志
 * 权限：普通用户只能查看自己的日志，管理员可以查看所有日志
 * 设计：三段式布局（头部导航栏、主内容区域、页脚）
 */

// 引入认证中间件
require_once '../auth/Auth.php';
require_once '../auth/SystemConfig.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 要求用户登录
Auth::requireLogin('../login.php');

// 获取当前用户信息
$current_user = Auth::getCurrentUser();
$is_admin = Auth::hasRole(Auth::ROLE_ADMIN);

// 引入数据库配置
require_once '../config/database.php';
$db = Database::getInstance();

// 分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// 日志类型参数
$log_type = $_GET['type'] ?? 'operation'; // operation 或 login
$user_filter = $_GET['user'] ?? '';

// 构建查询条件
$where_conditions = [];
$params = [];

if (!$is_admin) {
    // 普通用户只能查看自己的日志
    $where_conditions[] = "user_id = ?";
    $params[] = $current_user['id'];
} elseif (!empty($user_filter)) {
    // 管理员可以按用户筛选
    $where_conditions[] = "(username LIKE ? OR email LIKE ?)";
    $params[] = "%{$user_filter}%";
    $params[] = "%{$user_filter}%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// 获取日志数据
if ($log_type === 'login') {
    // 登录日志
    $count_sql = "SELECT COUNT(*) as total FROM login_logs {$where_clause}";
    $data_sql = "SELECT * FROM login_logs {$where_clause} ORDER BY login_time DESC LIMIT {$per_page} OFFSET {$offset}";
} else {
    // 操作日志
    $count_sql = "SELECT COUNT(*) as total FROM operation_logs {$where_clause}";
    $data_sql = "SELECT * FROM operation_logs {$where_clause} ORDER BY created_at DESC LIMIT {$per_page} OFFSET {$offset}";
}

$total_count = $db->fetch($count_sql, $params)['total'];
$logs = $db->fetchAll($data_sql, $params);
$total_pages = ceil($total_count / $per_page);

// 获取用户列表（管理员用）
$users = [];
if ($is_admin) {
    $users = $db->fetchAll("SELECT id, username, email, nickname FROM users ORDER BY username");
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle('日志查看'); ?></title>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- SEO和社交媒体标签 -->
    <meta name="description" content="系统日志查看页面">
    <meta name="keywords" content="日志, 操作记录, 登录记录, 系统监控">
    <meta name="author" content="现代化PHP管理系统">
    
    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">

    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- 仪表盘容器 -->
    <div class="dashboard-container">
        <!-- 头部导航栏 -->
        <?php include 'includes/header.php'; ?>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                        </svg>
                        日志查看
                    </h1>
                    <p class="page-subtitle">
                        <?php if ($is_admin): ?>
                        查看系统操作日志和登录记录
                        <?php else: ?>
                        查看您的操作日志和登录记录
                        <?php endif; ?>
                    </p>
                </div>
                
                <!-- 日志内容 -->
                <div class="logs-content">
                    <div class="logs-grid">
                        <!-- 筛选和统计卡片 -->
                        <div class="logs-card filter-card">
                            <div class="card-header">
                                <h2 class="card-title">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"/>
                                    </svg>
                                    筛选条件
                                </h2>
                            </div>
                            <div class="card-content">
                                <form method="get" class="filter-form">
                                    <div class="form-group">
                                        <label for="type">日志类型</label>
                                        <select id="type" name="type" onchange="this.form.submit()">
                                            <option value="operation" <?php echo $log_type === 'operation' ? 'selected' : ''; ?>>操作日志</option>
                                            <option value="login" <?php echo $log_type === 'login' ? 'selected' : ''; ?>>登录日志</option>
                                        </select>
                                    </div>
                                    
                                    <?php if ($is_admin): ?>
                                    <div class="form-group">
                                        <label for="user">用户筛选</label>
                                        <input type="text" id="user" name="user" 
                                               value="<?php echo htmlspecialchars($user_filter); ?>" 
                                               placeholder="输入用户名或邮箱">
                                    </div>
                                    
                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary btn-sm">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <circle cx="11" cy="11" r="8"/>
                                                <path d="M21 21l-4.35-4.35"/>
                                            </svg>
                                            筛选
                                        </button>
                                        <a href="logs.php?type=<?php echo $log_type; ?>" class="btn btn-secondary btn-sm">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="1,4 1,10 7,10"/>
                                                <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                                            </svg>
                                            重置
                                        </a>
                                    </div>
                                    <?php endif; ?>
                                </form>
                                
                                <!-- 统计信息 -->
                                <div class="stats-section">
                                    <h4 class="section-title">统计信息</h4>
                                    <div class="stats-list">
                                        <div class="stat-item">
                                            <span class="stat-label">总记录数</span>
                                            <span class="stat-value"><?php echo number_format($total_count); ?></span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">当前页面</span>
                                            <span class="stat-value"><?php echo $page; ?> / <?php echo max(1, $total_pages); ?></span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">日志类型</span>
                                            <span class="stat-value">
                                                <?php echo $log_type === 'login' ? '登录日志' : '操作日志'; ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 日志列表卡片 -->
                        <div class="logs-card logs-list-card">
                            <div class="card-header">
                                <h2 class="card-title">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 12l2 2 4-4"/>
                                        <path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"/>
                                        <path d="M21 19c.552 0 1-.448 1-1v-6c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"/>
                                    </svg>
                                    <?php echo $log_type === 'login' ? '登录日志' : '操作日志'; ?>
                                </h2>
                                <div class="card-actions">
                                    <span class="record-count"><?php echo number_format($total_count); ?> 条记录</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <?php if (empty($logs)): ?>
                                <div class="empty-state">
                                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                        <polyline points="14,2 14,8 20,8"/>
                                    </svg>
                                    <h3>暂无日志记录</h3>
                                    <p>当前筛选条件下没有找到相关日志</p>
                                </div>
                                <?php else: ?>
                                <div class="logs-table-container">
                                    <table class="logs-table">
                                        <thead>
                                            <tr>
                                                <?php if ($log_type === 'login'): ?>
                                                <th>时间</th>
                                                <th>用户</th>
                                                <th>状态</th>
                                                <th>IP地址</th>
                                                <th>设备</th>
                                                <th>地区</th>
                                                <?php else: ?>
                                                <th>时间</th>
                                                <th>用户</th>
                                                <th>操作类型</th>
                                                <th>操作描述</th>
                                                <th>IP地址</th>
                                                <th>详情</th>
                                                <?php endif; ?>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($logs as $log): ?>
                                            <tr class="log-row">
                                                <?php if ($log_type === 'login'): ?>
                                                <td class="log-time">
                                                    <div class="time-info">
                                                        <span class="date"><?php echo date('Y-m-d', strtotime($log['login_time'])); ?></span>
                                                        <span class="time"><?php echo date('H:i:s', strtotime($log['login_time'])); ?></span>
                                                    </div>
                                                </td>
                                                <td class="log-user">
                                                    <div class="user-info">
                                                        <span class="username"><?php echo htmlspecialchars($log['username'] ?? '未知'); ?></span>
                                                        <?php if (!empty($log['email'])): ?>
                                                        <span class="email"><?php echo htmlspecialchars($log['email']); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td class="log-status">
                                                    <span class="status-badge status-<?php echo $log['login_status']; ?>">
                                                        <?php
                                                        $status_names = [
                                                            'success' => '成功',
                                                            'failed' => '失败',
                                                            'blocked' => '阻止'
                                                        ];
                                                        echo $status_names[$log['login_status']] ?? $log['login_status'];
                                                        ?>
                                                    </span>
                                                </td>
                                                <td class="log-ip"><?php echo htmlspecialchars($log['ip_address']); ?></td>
                                                <td class="log-device"><?php echo htmlspecialchars($log['device_info'] ?? '未知'); ?></td>
                                                <td class="log-location"><?php echo htmlspecialchars($log['location'] ?? '未知'); ?></td>
                                                <?php else: ?>
                                                <td class="log-time">
                                                    <div class="time-info">
                                                        <span class="date"><?php echo date('Y-m-d', strtotime($log['created_at'])); ?></span>
                                                        <span class="time"><?php echo date('H:i:s', strtotime($log['created_at'])); ?></span>
                                                    </div>
                                                </td>
                                                <td class="log-user">
                                                    <div class="user-info">
                                                        <span class="username"><?php echo htmlspecialchars($log['username'] ?? '系统'); ?></span>
                                                    </div>
                                                </td>
                                                <td class="log-type">
                                                    <span class="type-badge"><?php echo htmlspecialchars($log['operation_type']); ?></span>
                                                </td>
                                                <td class="log-description"><?php echo htmlspecialchars($log['operation_desc']); ?></td>
                                                <td class="log-ip"><?php echo htmlspecialchars($log['ip_address']); ?></td>
                                                <td class="log-details">
                                                    <?php if (!empty($log['operation_data'])): ?>
                                                    <button class="btn-details"
                                                            data-operation-data="<?php echo htmlspecialchars($log['operation_data'], ENT_QUOTES); ?>"
                                                            data-operation-type="<?php echo htmlspecialchars($log['operation_type'], ENT_QUOTES); ?>"
                                                            data-operation-desc="<?php echo htmlspecialchars($log['operation_desc'], ENT_QUOTES); ?>"
                                                            onclick="showLogDetailsFromButton(this)">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                            <circle cx="12" cy="12" r="10"/>
                                                            <line x1="12" y1="16" x2="12" y2="12"/>
                                                            <line x1="12" y1="8" x2="12.01" y2="8"/>
                                                        </svg>
                                                    </button>
                                                    <?php else: ?>
                                                    <span class="no-details">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <?php endif; ?>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- 分页 -->
                                <?php if ($total_pages > 1): ?>
                                <div class="pagination-container">
                                    <nav class="pagination">
                                        <?php if ($page > 1): ?>
                                        <a href="?type=<?php echo $log_type; ?>&user=<?php echo urlencode($user_filter); ?>&page=<?php echo $page - 1; ?>" class="pagination-btn">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="15,18 9,12 15,6"/>
                                            </svg>
                                            上一页
                                        </a>
                                        <?php endif; ?>
                                        
                                        <div class="pagination-info">
                                            第 <?php echo $page; ?> 页，共 <?php echo $total_pages; ?> 页
                                        </div>
                                        
                                        <?php if ($page < $total_pages): ?>
                                        <a href="?type=<?php echo $log_type; ?>&user=<?php echo urlencode($user_filter); ?>&page=<?php echo $page + 1; ?>" class="pagination-btn">
                                            下一页
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="9,18 15,12 9,6"/>
                                            </svg>
                                        </a>
                                        <?php endif; ?>
                                    </nav>
                                </div>
                                <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- 页脚 -->
        <?php include 'includes/footer.php'; ?>
    </div>
    

    
    <!-- 全局组件加载器 -->
    <script src="../assets/js/main.js"></script>
    
    <!-- 日志页面脚本 -->
    <script>
        /**
         * 日志页面初始化
         */
        function initializeLogs() {
            console.log('📊 日志页面已加载');
        }
        
        // 从按钮显示日志详情
        function showLogDetailsFromButton(button) {
            const operationData = button.getAttribute('data-operation-data');
            const operationType = button.getAttribute('data-operation-type');
            const operationDesc = button.getAttribute('data-operation-desc');

            showLogDetails(operationData, operationType, operationDesc);
        }

        // 显示日志详情
        function showLogDetails(jsonData, operationType, operationDesc) {
            try {
                const data = JSON.parse(jsonData);
                const formattedContent = formatLogDetails(data);

                // 使用全局模态弹窗组件
                window.showModal.custom({
                    title: `${operationType} - ${operationDesc}`,
                    content: formattedContent,
                    maxWidth: 'large',
                    buttons: [
                        { text: '关闭', type: 'secondary', action: 'resolve' }
                    ]
                });
            } catch (e) {
                console.error('数据解析错误:', e, '原始数据:', jsonData);

                // 显示错误弹窗
                window.showModal.error(
                    '数据解析失败',
                    `解析操作详情时发生错误: ${e.message}<br><br><small>原始数据: ${jsonData}</small>`
                );
            }
        }

        // 格式化日志详情
        function formatLogDetails(data) {
            let html = '<div class="details-container">';

            // 按重要性排序字段
            const fieldOrder = ['username', 'email', 'nickname', 'ip', 'device', 'password_changed'];
            const sortedEntries = [];

            // 先添加重要字段
            fieldOrder.forEach(key => {
                if (data.hasOwnProperty(key)) {
                    sortedEntries.push([key, data[key]]);
                }
            });

            // 再添加其他字段
            Object.entries(data).forEach(([key, value]) => {
                if (!fieldOrder.includes(key)) {
                    sortedEntries.push([key, value]);
                }
            });

            // 生成HTML
            sortedEntries.forEach(([key, value]) => {
                html += `
                    <div class="detail-row">
                        <div class="detail-label">${formatLabel(key)}</div>
                        <div class="detail-value">${formatValue(value)}</div>
                    </div>
                `;
            });

            html += '</div>';
            return html;
        }

        // 格式化标签
        function formatLabel(key) {
            const labels = {
                'username': '用户名',
                'email': '邮箱',
                'nickname': '昵称',
                'ip': 'IP地址',
                'device': '设备',
                'password_changed': '密码是否修改',
                'user_id': '用户ID',
                'old_value': '原值',
                'new_value': '新值',
                'timestamp': '时间戳',
                'session_id': '会话ID'
            };
            return labels[key] || key;
        }

        // 格式化值
        function formatValue(value) {
            if (value === null || value === undefined) {
                return '<span class="null-value">无</span>';
            }

            if (typeof value === 'boolean') {
                return `<span class="boolean-value ${value ? 'true' : 'false'}">${value ? '是' : '否'}</span>`;
            }

            if (typeof value === 'object') {
                return `<pre class="json-value">${JSON.stringify(value, null, 2)}</pre>`;
            }

            if (typeof value === 'string' && value.includes('@')) {
                return `<span class="email-value">${value}</span>`;
            }

            return `<span class="text-value">${value}</span>`;
        }
        

        
        // 页面加载完成时初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeLogs);
        } else {
            initializeLogs();
        }
    </script>
</body>
</html>
