/**
 * 全局设置管理器
 * 统一管理页面设置的保存、加载和应用
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class SettingsManager {
    constructor() {
        this.storageKey = 'pageSettings';
        this.settings = this.loadSettings();
        this.defaultSettings = {
            loadingAnimation: 'spinner',
            toastStyle: 'modern',
            theme: 'light',
            autoTheme: false,
            lightTime: '06:00',
            darkTime: '18:00'
        };

        // 待处理的Toast样式（用于延迟应用）
        this.pendingToastStyle = null;

        // 初始化时应用设置
        this.applyAllSettings();
    }

    /**
     * 加载设置
     */
    loadSettings() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            const settings = saved ? JSON.parse(saved) : {};
            console.log('📋 已加载保存的设置:', settings);
            return settings;
        } catch (error) {
            console.warn('⚠️ 加载设置失败:', error);
            return {};
        }
    }

    /**
     * 保存设置
     */
    saveSettings() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.settings));
            console.log('💾 设置已保存:', this.settings);
            return true;
        } catch (error) {
            console.warn('⚠️ 保存设置失败:', error);
            return false;
        }
    }

    /**
     * 获取设置值
     */
    get(key, defaultValue = null) {
        return this.settings[key] !== undefined ? this.settings[key] : (defaultValue || this.defaultSettings[key]);
    }

    /**
     * 设置值
     */
    set(key, value) {
        this.settings[key] = value;
        this.saveSettings();
        this.applySetting(key, value);
        
        // 触发设置变更事件
        this.dispatchEvent('settingChanged', { key, value });
        
        console.log(`⚙️ 设置已更新: ${key} = ${value}`);
    }

    /**
     * 批量设置
     */
    setMultiple(settings) {
        Object.assign(this.settings, settings);
        this.saveSettings();
        
        // 应用所有设置
        Object.entries(settings).forEach(([key, value]) => {
            this.applySetting(key, value);
        });
        
        // 触发批量设置变更事件
        this.dispatchEvent('settingsChanged', { settings });
    }

    /**
     * 应用单个设置
     */
    applySetting(key, value) {
        switch (key) {
            case 'loadingAnimation':
                this.applyLoadingAnimationSetting(value);
                break;
            case 'toastStyle':
                this.applyToastStyleSetting(value);
                break;
            case 'theme':
                this.applyThemeSetting(value);
                break;
            case 'autoTheme':
            case 'lightTime':
            case 'darkTime':
                this.applyAutoThemeSettings();
                break;
        }
    }

    /**
     * 应用所有设置
     */
    applyAllSettings() {
        // 应用加载动画设置
        const loadingType = this.get('loadingAnimation');
        this.applyLoadingAnimationSetting(loadingType);

        // 应用Toast样式设置
        const toastStyle = this.get('toastStyle');
        this.applyToastStyleSetting(toastStyle);

        // 应用主题设置
        const theme = this.get('theme');
        if (theme && theme !== 'light') {
            this.applyThemeSetting(theme);
        }

        // 应用自动主题设置
        this.applyAutoThemeSettings();

        console.log('🎯 所有设置已应用');
    }

    /**
     * 应用加载动画设置
     */
    applyLoadingAnimationSetting(type) {
        if (!type) return;
        
        // 更新LoadingSpinner默认类型
        if (window.LoadingSpinner) {
            window.LoadingSpinner.setDefaultType(type);
        }
        
        // 更新全局实例
        if (window.loadingSpinner) {
            window.loadingSpinner.options.type = type;
        }
        
        console.log(`🎨 加载动画类型已设置为: ${type}`);
    }

    /**
     * 应用Toast样式设置
     */
    applyToastStyleSetting(style) {
        if (!style) return;

        // 设置页面Toast样式属性（立即设置）
        document.documentElement.setAttribute('data-toast-style', style);

        // 更新Toast管理器默认样式（如果可用）
        if (window.toastManager && typeof window.toastManager.setDefaultStyle === 'function') {
            window.toastManager.setDefaultStyle(style);
            console.log(`🎨 Toast样式已设置为: ${style}`);
        } else {
            // Toast管理器还未加载，保存设置供后续应用
            this.pendingToastStyle = style;
            console.log(`🎨 Toast样式已保存: ${style}，将在Toast管理器加载后应用`);
        }
    }

    /**
     * 当Toast管理器可用时应用待处理的Toast样式
     */
    applyPendingToastStyle() {
        if (this.pendingToastStyle && window.toastManager && typeof window.toastManager.setDefaultStyle === 'function') {
            window.toastManager.setDefaultStyle(this.pendingToastStyle);
            console.log(`🎨 已应用待处理的Toast样式: ${this.pendingToastStyle}`);
            this.pendingToastStyle = null; // 清除待处理状态
        }
    }

    /**
     * 应用主题设置
     */
    applyThemeSetting(theme) {
        if (!theme) return;

        document.documentElement.setAttribute('data-theme', theme);

        // 更新主题切换器状态
        if (window.themeSwitcher) {
            window.themeSwitcher.currentTheme = theme;
            // 更新主题切换器的UI
            const toggleButton = document.querySelector('.theme-toggle');
            if (toggleButton) {
                toggleButton.innerHTML = window.themeSwitcher.getThemeIcon();
            }
        }

        console.log(`🎨 主题已设置为: ${theme}`);
    }

    /**
     * 应用自动主题设置
     */
    applyAutoThemeSettings() {
        const autoMode = this.get('autoTheme');
        const lightTime = this.get('lightTime');
        const darkTime = this.get('darkTime');
        
        if (window.themeSwitcher && autoMode) {
            window.themeSwitcher.setAutoMode(autoMode, lightTime, darkTime);
        }
    }

    /**
     * 重置所有设置
     */
    reset() {
        this.settings = {};
        this.saveSettings();
        this.applyAllSettings();
        
        // 触发重置事件
        this.dispatchEvent('settingsReset');
        
        console.log('🔄 设置已重置为默认值');
    }

    /**
     * 获取所有设置
     */
    getAll() {
        return { ...this.settings };
    }

    /**
     * 导出设置
     */
    export() {
        const exportData = {
            settings: this.settings,
            timestamp: Date.now(),
            version: '1.0.0'
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `page-settings-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log('📤 设置已导出');
    }

    /**
     * 导入设置
     */
    import(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    if (data.settings) {
                        this.setMultiple(data.settings);
                        console.log('📥 设置已导入');
                        resolve(data.settings);
                    } else {
                        reject(new Error('无效的设置文件格式'));
                    }
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsText(file);
        });
    }

    /**
     * 触发自定义事件
     */
    dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(`settingsManager:${eventName}`, {
            detail: {
                instance: this,
                ...detail
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 检查设置是否存在
     */
    hasSettings() {
        return Object.keys(this.settings).length > 0;
    }

    /**
     * 获取设置摘要
     */
    getSummary() {
        return {
            hasSettings: this.hasSettings(),
            settingsCount: Object.keys(this.settings).length,
            loadingAnimation: this.get('loadingAnimation'),
            toastStyle: this.get('toastStyle'),
            theme: this.get('theme'),
            autoTheme: this.get('autoTheme')
        };
    }
}

// 创建全局设置管理器实例
window.settingsManager = new SettingsManager();

// 导出类
window.SettingsManager = SettingsManager;
