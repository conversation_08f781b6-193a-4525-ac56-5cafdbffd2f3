/* ===== 设置面板组件样式 ===== */

/* 设置悬浮按钮 */
.settings-fab {
    position: fixed;
    bottom: 1.5rem;
    right: 1.5rem;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #e5e7eb);
    box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.settings-fab:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
    border-color: var(--primary-color, #3b82f6);
}

.settings-fab:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

.settings-fab.active {
    transform: rotate(135deg);
    border-color: var(--primary-color, #3b82f6);
}

/* 设置图标 */
.settings-icon {
    width: 18px;
    height: 18px;
    position: relative;
    transition: all 0.3s ease;
}

.settings-icon svg {
    position: absolute;
    inset: 0;
    color: var(--text-secondary, #6b7280);
    transition: all 0.3s ease;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.settings-fab:hover .settings-icon svg {
    color: var(--primary-color, #3b82f6);
}

.settings-fab.active .settings-icon svg {
    color: var(--primary-color, #3b82f6);
    transform: rotate(-135deg);
}

/* 设置面板 */
.settings-panel {
    position: fixed;
    bottom: 80px;
    right: 1.5rem;
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #e5e7eb);
    border-radius: 16px;
    box-shadow: var(--shadow-2xl, 0 25px 50px -12px rgba(0, 0, 0, 0.25));
    backdrop-filter: blur(20px);
    min-width: 320px;
    max-width: 400px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 999;
    overflow: hidden;
}

.settings-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* 气泡箭头 */
.settings-panel::after {
    content: '';
    position: absolute;
    bottom: -8px;
    right: 28px;
    width: 16px;
    height: 16px;
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #e5e7eb);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
    border-radius: 2px;
}

/* 设置面板头部 */
.settings-header {
    padding: var(--spacing-4, 1rem) var(--spacing-4, 1rem) var(--spacing-2, 0.5rem);
    border-bottom: 1px solid var(--border-light, #f3f4f6);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.settings-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary, #1f2937);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-2, 0.5rem);
}

.settings-close {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: var(--bg-tertiary, #f9fafb);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition, all 0.3s ease);
    color: var(--text-muted, #6b7280);
}

.settings-close:hover {
    background: var(--border-color, #e5e7eb);
    color: var(--text-primary, #1f2937);
}

/* 设置面板内容 */
.settings-body {
    padding: var(--spacing-2, 0.5rem);
    max-height: 60vh;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 自定义滚动条样式 */
.settings-body::-webkit-scrollbar,
.loading-selector::-webkit-scrollbar {
    width: 6px;
}

.settings-body::-webkit-scrollbar-track,
.loading-selector::-webkit-scrollbar-track {
    background: var(--bg-tertiary, #f1f5f9);
    border-radius: 3px;
}

.settings-body::-webkit-scrollbar-thumb,
.loading-selector::-webkit-scrollbar-thumb {
    background: var(--border-color, #e5e7eb);
    border-radius: 3px;
}

.settings-body::-webkit-scrollbar-thumb:hover,
.loading-selector::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted, #6b7280);
}

/* 设置项 */
.settings-item {
    padding: var(--spacing-3, 0.75rem);
    border-radius: 12px;
    margin-bottom: var(--spacing-2, 0.5rem);
    transition: var(--transition, all 0.3s ease);
    cursor: pointer;
}

.settings-item:hover {
    background: var(--bg-tertiary, #f9fafb);
}

.settings-item:last-child {
    margin-bottom: 0;
}

.settings-item-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-3, 0.75rem);
    margin-bottom: var(--spacing-2, 0.5rem);
}

.settings-item-icon {
    width: 20px;
    height: 20px;
    color: var(--primary-color, #3b82f6);
    flex-shrink: 0;
}

.settings-item-info {
    flex: 1;
}

.settings-item-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary, #1f2937);
    margin: 0 0 2px 0;
}

.settings-item-desc {
    font-size: 0.75rem;
    color: var(--text-muted, #6b7280);
    margin: 0;
}

.settings-item-arrow {
    width: 16px;
    height: 16px;
    color: var(--text-muted, #6b7280);
    transition: transform 0.3s ease;
}

.settings-item.expanded .settings-item-arrow {
    transform: rotate(90deg);
}

/* 设置项内容 */
.settings-item-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.settings-item.expanded .settings-item-content {
    max-height: 400px;
}

/* 加载动画选择器 */
.loading-selector {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-2, 0.5rem);
    padding: var(--spacing-2, 0.5rem) 0;
    max-height: 350px;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Toast样式选择器 */
.toast-selector {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-2, 0.5rem);
    padding: var(--spacing-2, 0.5rem) 0;
    max-height: 350px;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 模态弹窗动画选择器 */
.modal-animation-selector {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-2, 0.5rem);
    padding: var(--spacing-2, 0.5rem) 0;
    max-height: 350px;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 模态弹窗样式选择器 */
.modal-style-selector {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-2, 0.5rem);
    padding: var(--spacing-2, 0.5rem) 0;
    max-height: 350px;
    overflow-y: auto;
    overflow-x: hidden;
}

.loading-option,
.toast-option,
.modal-animation-option,
.modal-style-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2, 0.5rem);
    padding: var(--spacing-3, 0.75rem);
    border: 2px solid var(--border-color, #e5e7eb);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--bg-secondary, #f8fafc);
}

.loading-option:hover,
.toast-option:hover,
.modal-animation-option:hover,
.modal-style-option:hover {
    border-color: var(--primary-light, #60a5fa);
    background: var(--primary-light, #60a5fa);
    background-opacity: 0.1;
}

.loading-option.selected,
.toast-option.selected,
.modal-animation-option.selected,
.modal-style-option.selected {
    border-color: var(--primary-color, #3b82f6);
    background: var(--primary-light, #60a5fa);
    background-opacity: 0.1;
}

.loading-option-preview {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.loading-option-name {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-primary, #1f2937);
    text-align: center;
}

/* 迷你加载动画预览 */
.mini-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-light, #e5e7eb);
    border-top: 2px solid var(--primary-color, #3b82f6);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.mini-dots {
    display: flex;
    gap: 3px;
}

.mini-dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: var(--primary-color, #3b82f6);
    animation: pulse-dot 1.4s ease-in-out infinite both;
}

.mini-dot:nth-child(1) { animation-delay: -0.32s; }
.mini-dot:nth-child(2) { animation-delay: -0.16s; }
.mini-dot:nth-child(3) { animation-delay: 0s; }

.mini-wave {
    display: flex;
    gap: 2px;
    align-items: center;
}

.mini-wave-bar {
    width: 2px;
    height: 12px;
    background: var(--primary-color, #3b82f6);
    border-radius: 1px;
    animation: wave 1.2s ease-in-out infinite;
}

.mini-wave-bar:nth-child(1) { animation-delay: -1.1s; }
.mini-wave-bar:nth-child(2) { animation-delay: -1.0s; }
.mini-wave-bar:nth-child(3) { animation-delay: -0.9s; }
.mini-wave-bar:nth-child(4) { animation-delay: -0.8s; }

.mini-bounce {
    display: flex;
    gap: 2px;
}

.mini-bounce-ball {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: var(--primary-color, #3b82f6);
    animation: bounce 1.4s ease-in-out infinite both;
}

.mini-bounce-ball:nth-child(1) { animation-delay: -0.32s; }
.mini-bounce-ball:nth-child(2) { animation-delay: -0.16s; }
.mini-bounce-ball:nth-child(3) { animation-delay: 0s; }

.mini-ring {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: conic-gradient(from 0deg, transparent, var(--primary-color, #3b82f6), transparent);
    animation: rotate 1s linear infinite;
    position: relative;
}

.mini-ring::before {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: 50%;
    background: var(--bg-secondary, #f8fafc);
}

.mini-pulse {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color, #3b82f6);
    animation: pulse-ring 2s ease-in-out infinite;
    position: relative;
}

.mini-progress {
    width: 24px;
    height: 3px;
    background: var(--border-light, #e5e7eb);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.mini-progress-bar {
    height: 100%;
    background: var(--primary-color, #3b82f6);
    border-radius: 2px;
    animation: progress 2s ease-in-out infinite;
    transform: translateX(-100%);
}

/* Toast预览样式 */
.mini-toast {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    min-width: 40px;
    justify-content: center;
}

.mini-toast-icon {
    width: 8px;
    height: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    line-height: 1;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.mini-toast-text {
    font-size: 8px;
    line-height: 1;
}

/* Toast样式预览变体 */
.toast-modern-preview {
    background: var(--primary-color, #3b82f6);
    color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.toast-modern-preview .mini-toast-icon {
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.toast-minimal-preview {
    background: var(--bg-primary, #ffffff);
    color: var(--text-primary, #1f2937);
    border: 1px solid var(--border-color, #e5e7eb);
    border-radius: 4px;
}

.toast-minimal-preview .mini-toast-icon {
    color: var(--primary-color, #3b82f6);
    text-shadow: none;
}

.toast-rounded-preview {
    background: var(--success-color, #10b981);
    color: white;
    border-radius: 20px;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.toast-rounded-preview .mini-toast-icon {
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.toast-flat-preview {
    background: var(--warning-color, #f59e0b);
    color: white;
    border-radius: 0;
}

.toast-flat-preview .mini-toast-icon {
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.toast-gradient-preview {
    background: linear-gradient(135deg, var(--primary-color, #3b82f6), var(--primary-light, #60a5fa));
    color: white;
    border-radius: 6px;
}

.toast-gradient-preview .mini-toast-icon {
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.toast-shadow-preview {
    background: var(--bg-primary, #ffffff);
    color: var(--text-primary, #1f2937);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--border-light, #f3f4f6);
}

.toast-shadow-preview .mini-toast-icon {
    color: var(--success-color, #10b981);
    text-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .settings-panel {
        right: var(--spacing-4, 1rem);
        left: var(--spacing-4, 1rem);
        min-width: auto;
        max-width: none;
    }

    .settings-fab {
        bottom: var(--spacing-4, 1rem);
        right: var(--spacing-4, 1rem);
    }

    .settings-body {
        max-height: 50vh;
    }

    .loading-selector {
        grid-template-columns: repeat(2, 1fr);
        max-height: 300px;
    }

    .settings-item.expanded .settings-item-content {
        max-height: 350px;
    }
}

/* 动画关键帧 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse-dot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes wave {
    0%, 40%, 100% {
        transform: scaleY(0.4);
        opacity: 0.5;
    }
    20% {
        transform: scaleY(1);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0) translateY(0);
    }
    40% {
        transform: scale(1) translateY(-8px);
    }
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse-ring {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(2.5);
        opacity: 0;
    }
}

@keyframes progress {
    0% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(0%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* 暗色主题适配 */
[data-theme="dark"] .settings-fab {
    background: var(--bg-primary);
    border-color: var(--border-color);
    box-shadow: var(--shadow-lg);
}

[data-theme="dark"] .settings-panel {
    background: var(--bg-primary);
    border-color: var(--border-color);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .settings-panel::after {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

/* 暗色主题滚动条 */
[data-theme="dark"] .settings-body::-webkit-scrollbar-track,
[data-theme="dark"] .loading-selector::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

[data-theme="dark"] .settings-body::-webkit-scrollbar-thumb,
[data-theme="dark"] .loading-selector::-webkit-scrollbar-thumb {
    background: var(--border-color);
}

[data-theme="dark"] .settings-body::-webkit-scrollbar-thumb:hover,
[data-theme="dark"] .loading-selector::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* ===== 模态弹窗预览样式 ===== */

/* 模态弹窗动画预览 */
.mini-modal {
    width: 32px;
    height: 24px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.mini-modal-content {
    width: 20px;
    height: 16px;
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #e5e7eb);
    border-radius: 2px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
}

.mini-modal-header {
    height: 4px;
    background: var(--bg-secondary, #f8fafc);
    border-bottom: 1px solid var(--border-color, #e5e7eb);
    border-radius: 2px 2px 0 0;
}

.mini-modal-body {
    flex: 1;
    background: var(--bg-primary, #ffffff);
    border-radius: 0 0 2px 2px;
}

/* 模态弹窗动画预览变体 */
.modal-fade-preview .mini-modal-content {
    animation: mini-fade 2s ease-in-out infinite;
}

.modal-slide-preview .mini-modal-content {
    animation: mini-slide 2s ease-in-out infinite;
}

.modal-zoom-preview .mini-modal-content {
    animation: mini-zoom 2s ease-in-out infinite;
}

.modal-bounce-preview .mini-modal-content {
    animation: mini-bounce-modal 2s ease-in-out infinite;
}

/* 模态弹窗样式预览 */
.mini-modal-style {
    width: 32px;
    height: 24px;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color, #e5e7eb);
}

.mini-modal-header-style {
    height: 6px;
    border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.mini-modal-body-style {
    flex: 1;
}

/* 模态弹窗样式预览变体 */
.modern-style-preview {
    background: var(--bg-primary, #ffffff);
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modern-style-preview .mini-modal-header-style {
    background: var(--bg-secondary, #f8fafc);
}

.modern-style-preview .mini-modal-body-style {
    background: var(--bg-primary, #ffffff);
}

.minimal-style-preview {
    background: var(--bg-primary, #ffffff);
    border-radius: 2px;
    border: 1px solid var(--border-color, #e5e7eb);
}

.minimal-style-preview .mini-modal-header-style {
    background: var(--bg-primary, #ffffff);
}

.minimal-style-preview .mini-modal-body-style {
    background: var(--bg-primary, #ffffff);
}

.rounded-style-preview {
    background: var(--bg-primary, #ffffff);
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rounded-style-preview .mini-modal-header-style {
    background: var(--bg-secondary, #f8fafc);
    border-radius: 12px 12px 0 0;
}

.rounded-style-preview .mini-modal-body-style {
    background: var(--bg-primary, #ffffff);
    border-radius: 0 0 12px 12px;
}

.shadow-style-preview {
    background: var(--bg-primary, #ffffff);
    border-radius: 8px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border: none;
}

.shadow-style-preview .mini-modal-header-style {
    background: var(--bg-secondary, #f8fafc);
}

.shadow-style-preview .mini-modal-body-style {
    background: var(--bg-primary, #ffffff);
}

.glass-style-preview {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.glass-style-preview .mini-modal-header-style {
    background: rgba(248, 250, 252, 0.8);
}

.glass-style-preview .mini-modal-body-style {
    background: rgba(255, 255, 255, 0.6);
}

.flat-style-preview {
    background: var(--bg-primary, #ffffff);
    border-radius: 0;
    border: 2px solid var(--primary-color, #3b82f6);
}

.flat-style-preview .mini-modal-header-style {
    background: var(--primary-color, #3b82f6);
}

.flat-style-preview .mini-modal-body-style {
    background: var(--bg-primary, #ffffff);
}

/* 模态弹窗动画关键帧 */
@keyframes mini-fade {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

@keyframes mini-slide {
    0%, 100% { transform: translate(-50%, -70%); }
    50% { transform: translate(-50%, -50%); }
}

@keyframes mini-zoom {
    0%, 100% { transform: translate(-50%, -50%) scale(0.8); }
    50% { transform: translate(-50%, -50%) scale(1); }
}

@keyframes mini-bounce-modal {
    0%, 100% { transform: translate(-50%, -50%) scale(0.9); }
    25% { transform: translate(-50%, -50%) scale(1.1); }
    50% { transform: translate(-50%, -50%) scale(1); }
    75% { transform: translate(-50%, -50%) scale(1.05); }
}

/* ===== 响应式设计更新 ===== */
@media (max-width: 768px) {
    .loading-selector,
    .toast-selector,
    .modal-animation-selector,
    .modal-style-selector {
        grid-template-columns: 1fr;
    }

    .loading-option,
    .toast-option,
    .modal-animation-option,
    .modal-style-option {
        padding: var(--spacing-2, 0.5rem);
    }
}
