<?php
/**
 * 🔧 系统配置公共类
 *
 * 功能：动态获取系统设置，提供网站信息、配置等
 * 用途：全局调用系统设置，避免硬编码
 */

// 处理API请求
if (isset($_GET['action'])) {
    header('Content-Type: application/json; charset=utf-8');

    try {
        $action = $_GET['action'];

        switch ($action) {
            case 'get_setting':
                $key = $_GET['key'] ?? '';
                if (empty($key)) {
                    throw new Exception('缺少设置键名');
                }

                // 解析键名 (category.setting_key)
                $parts = explode('.', $key, 2);
                if (count($parts) !== 2) {
                    throw new Exception('设置键名格式错误');
                }

                $value = SystemConfig::get($parts[0], $parts[1]);
                echo json_encode([
                    'success' => true,
                    'value' => $value
                ]);
                break;

            case 'get_security_config':
                // 为前端提供安全策略配置
                $config = [
                    'password_min_length' => SystemConfig::getPasswordMinLength(),
                    'password_complexity' => [
                        'require_uppercase' => SystemConfig::get('security', 'password_require_uppercase', '0') === '1',
                        'require_lowercase' => SystemConfig::get('security', 'password_require_lowercase', '0') === '1',
                        'require_numbers' => SystemConfig::get('security', 'password_require_numbers', '0') === '1',
                        'require_symbols' => SystemConfig::get('security', 'password_require_symbols', '0') === '1'
                    ],
                    'session_timeout' => SystemConfig::getSessionTimeout(),
                    'max_login_attempts' => SystemConfig::getMaxLoginAttempts()
                ];

                echo json_encode([
                    'success' => true,
                    'config' => $config
                ]);
                break;

            default:
                throw new Exception('无效的操作');
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}

class SystemConfig {
    private static $instance = null;
    private static $settings = null;
    private static $db = null;
    
    /**
     * 获取单例实例
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 私有构造函数
     */
    private function __construct() {
        $this->loadSettings();
    }
    
    /**
     * 加载系统设置
     */
    private function loadSettings() {
        try {
            // 获取数据库连接
            if (self::$db === null) {
                require_once __DIR__ . '/../config/database.php';
                self::$db = Database::getInstance();
            }
            
            // 从数据库获取所有设置
            $stmt = self::$db->query("SELECT category, setting_key, setting_value FROM settings");
            $all_settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 按分类组织设置
            self::$settings = [];
            foreach ($all_settings as $setting) {
                self::$settings[$setting['category']][$setting['setting_key']] = $setting['setting_value'];
            }
            
        } catch (Exception $e) {
            // 如果数据库连接失败，使用默认设置
            self::$settings = $this->getDefaultSettings();
            error_log("SystemConfig: 无法加载设置，使用默认值 - " . $e->getMessage());
        }
    }
    
    /**
     * 获取默认设置
     */
    private function getDefaultSettings() {
        return [
            'website' => [
                'site_name' => '管理系统',
                'site_description' => '现代化PHP管理系统',
                'site_url' => 'http://localhost:8080',
                'admin_email' => '<EMAIL>',
                'timezone' => 'Asia/Shanghai',
                'language' => 'zh-CN'
            ],
            'email' => [
                'smtp_host' => '',
                'smtp_port' => '587',
                'smtp_username' => '',
                'smtp_password' => '',
                'smtp_encryption' => 'tls',
                'from_email' => '',
                'from_name' => '系统通知'
            ],
            'security' => [
                'session_timeout' => '3600',
                'max_login_attempts' => '5',
                'password_min_length' => '6',
                'password_require_uppercase' => '0',
                'password_require_lowercase' => '0',
                'password_require_numbers' => '0',
                'password_require_symbols' => '0',
                'login_attempt_lockout_time' => '900',
                'remember_me_duration' => '2592000',
                'require_email_verification' => '1',
                'enable_two_factor' => '0',
                'login_log_retention' => '30'
            ]
        ];
    }
    
    /**
     * 获取设置值
     */
    public static function get($category, $key = null, $default = null) {
        $instance = self::getInstance();
        
        if ($key === null) {
            // 返回整个分类的设置
            return self::$settings[$category] ?? [];
        }
        
        // 返回特定设置值
        return self::$settings[$category][$key] ?? $default;
    }
    
    /**
     * 获取网站标题
     */
    public static function getSiteName() {
        return self::get('website', 'site_name', '管理系统');
    }
    
    /**
     * 获取网站描述
     */
    public static function getSiteDescription() {
        return self::get('website', 'site_description', '现代化PHP管理系统');
    }
    
    /**
     * 获取网站URL
     */
    public static function getSiteUrl() {
        return self::get('website', 'site_url', 'http://localhost:8080');
    }
    
    /**
     * 获取管理员邮箱
     */
    public static function getAdminEmail() {
        return self::get('website', 'admin_email', '<EMAIL>');
    }
    
    /**
     * 获取时区
     */
    public static function getTimezone() {
        return self::get('website', 'timezone', 'Asia/Shanghai');
    }
    
    /**
     * 获取语言
     */
    public static function getLanguage() {
        return self::get('website', 'language', 'zh-CN');
    }
    
    /**
     * 获取完整的页面标题（网站名称 + 页面标题）
     */
    public static function getPageTitle($pageTitle = '') {
        $siteName = self::getSiteName();
        return empty($pageTitle) ? $siteName : $pageTitle . ' - ' . $siteName;
    }
    
    /**
     * 刷新设置缓存
     */
    public static function refresh() {
        self::$settings = null;
        if (self::$instance !== null) {
            self::$instance->loadSettings();
        }
    }
    
    /**
     * 获取所有设置
     */
    public static function getAll() {
        $instance = self::getInstance();
        return self::$settings;
    }

    /**
     * 获取会话超时时间（秒）
     */
    public static function getSessionTimeout() {
        return (int)self::get('security', 'session_timeout', '3600');
    }

    /**
     * 获取最大登录尝试次数
     */
    public static function getMaxLoginAttempts() {
        return (int)self::get('security', 'max_login_attempts', '5');
    }

    /**
     * 获取密码最小长度
     */
    public static function getPasswordMinLength() {
        return (int)self::get('security', 'password_min_length', '6');
    }

    /**
     * 获取登录失败锁定时间（秒）
     */
    public static function getLoginLockoutTime() {
        return (int)self::get('security', 'login_attempt_lockout_time', '900');
    }

    /**
     * 获取记住我功能持续时间（秒）
     */
    public static function getRememberMeDuration() {
        return (int)self::get('security', 'remember_me_duration', '2592000');
    }

    /**
     * 获取密码复杂度要求
     */
    public static function getPasswordComplexityRules() {
        return [
            'min_length' => self::getPasswordMinLength(),
            'require_uppercase' => self::get('security', 'password_require_uppercase', '0') === '1',
            'require_lowercase' => self::get('security', 'password_require_lowercase', '0') === '1',
            'require_numbers' => self::get('security', 'password_require_numbers', '0') === '1',
            'require_symbols' => self::get('security', 'password_require_symbols', '0') === '1'
        ];
    }

    /**
     * 验证密码是否符合复杂度要求
     */
    public static function validatePasswordComplexity($password) {
        $rules = self::getPasswordComplexityRules();
        $errors = [];

        // 检查最小长度
        if (strlen($password) < $rules['min_length']) {
            $errors[] = "密码长度至少需要 {$rules['min_length']} 个字符";
        }

        // 检查大写字母
        if ($rules['require_uppercase'] && !preg_match('/[A-Z]/', $password)) {
            $errors[] = '密码必须包含至少一个大写字母';
        }

        // 检查小写字母
        if ($rules['require_lowercase'] && !preg_match('/[a-z]/', $password)) {
            $errors[] = '密码必须包含至少一个小写字母';
        }

        // 检查数字
        if ($rules['require_numbers'] && !preg_match('/\d/', $password)) {
            $errors[] = '密码必须包含至少一个数字';
        }

        // 检查特殊字符
        if ($rules['require_symbols'] && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = '密码必须包含至少一个特殊字符';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
?>
