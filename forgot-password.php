<?php
/**
 * 🔑 现代化PHP管理系统 - 找回密码页面
 * 
 * 功能：用户密码重置界面
 * 设计：采用现代化UI设计语言，与登录和注册页面保持一致
 * 特色：响应式布局、主题切换、表单验证、邮箱验证
 */

session_start();

// 引入系统配置
require_once 'auth/SystemConfig.php';
require_once 'auth/auth.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 检查密码找回功能是否启用
Auth::requirePasswordResetAccess();

// 处理找回密码逻辑
$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    
    // 基础验证
    if (empty($email)) {
        $error_message = '请输入邮箱地址';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = '请输入有效的邮箱地址';
    } else {
        // 这里应该连接数据库检查邮箱是否存在
        // 示例：简单的演示验证
        if ($email === '<EMAIL>' || $email === '<EMAIL>') {
            $success_message = '密码重置链接已发送到您的邮箱，请查收邮件并按照指示重置密码';
            // 实际项目中应该：
            // 1. 检查邮箱是否在数据库中存在
            // 2. 生成重置令牌
            // 3. 发送重置邮件
            // 4. 记录重置请求日志

            // 演示用：显示重置链接
            $demo_reset_link = 'reset-password.php?token=demo_reset_token_123';
            $success_message .= '<br><br><strong>演示链接：</strong><br><a href="' . $demo_reset_link . '" class="text-primary">' . $demo_reset_link . '</a>';
        } else {
            $error_message = '该邮箱地址未注册，请检查后重试';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle('找回密码'); ?></title>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- SEO和社交媒体标签 -->
    <meta name="description" content="现代化PHP管理系统密码找回页面，安全便捷地重置您的账户密码">
    <meta name="keywords" content="PHP, 管理系统, 找回密码, 密码重置">
    <meta name="author" content="现代化PHP管理系统">
    
    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔑</text></svg>">

    <!-- 引入全局组件系统 -->
    <script src="assets/js/main.js"></script>
</head>
<body>
    <!-- 找回密码容器 -->
    <div class="login-container">
        <!-- 找回密码卡片 -->
        <div class="login-card">
            <!-- 找回密码头部 -->
            <div class="login-header">
                <h1 class="login-title"><?php echo SystemConfig::getSiteName(); ?></h1>
                <p class="login-subtitle"><?php echo SystemConfig::getSiteDescription(); ?></p>
            </div>
            
            <!-- 找回密码表单 -->
            <div class="login-body">
                <form id="forgotPasswordForm" method="POST" action="" data-validate>
                    <!-- 邮箱输入框 -->
                    <div class="form-group">
                        <label for="email" class="form-label">邮箱地址</label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-control" 
                            placeholder="请输入您注册时使用的邮箱地址"
                            value="<?php echo htmlspecialchars($email ?? ''); ?>"
                            autocomplete="email"
                            required
                        >
                    </div>
                    
                    <!-- 发送重置链接按钮 -->
                    <button type="submit" class="btn btn-primary w-full mb-4" data-tooltip="我们将向您的邮箱发送安全的密码重置链接">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                            <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                        发送重置链接
                    </button>
                    
                    <!-- 返回登录链接 -->
                    <div class="text-center">
                        <a href="login.php" class="text-sm text-muted hover:text-primary">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="inline mr-1">
                                <polyline points="15,18 9,12 15,6"></polyline>
                            </svg>
                            返回登录
                        </a>
                        <span class="text-muted mx-2">|</span>
                        <a href="register.php" class="text-sm text-muted hover:text-primary">注册账户</a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 页脚信息 -->
        <div class="page-footer">
            <p class="text-center text-sm text-muted">
                © <?php echo date('Y'); ?> <?php echo SystemConfig::getSiteName(); ?>. 保留所有权利.
            </p>
        </div>
    </div>
    

    
    <!-- 页面特定脚本 -->
    <script>
        /**
         * 页面特定的初始化逻辑
         */

        // 全局变量：跟踪验证状态
        let isEmailVerified = false;
        let verifiedEmail = '';

        function initializeForgotPasswordPage() {
            // 显示服务器端消息
            <?php if (!empty($error_message)): ?>
                showToast('<?php echo addslashes($error_message); ?>', 'error');
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                showToast('<?php echo addslashes($success_message); ?>', 'success');
                // 成功发送后延迟跳转到登录页面
                setTimeout(() => {
                    window.location.href = 'login.php?reset_sent=1';
                }, 3000);
            <?php endif; ?>

            // 找回密码表单提交处理
            initForgotPasswordFormSubmit();

            console.log('🔑 找回密码页面特定功能已初始化完成');
        }
        
        // 找回密码表单提交处理
        function initForgotPasswordFormSubmit() {
            const forgotPasswordForm = document.getElementById('forgotPasswordForm');
            const submitBtn = forgotPasswordForm.querySelector('button[type="submit"]');
            let captchaSessionKey = null; // 存储验证码会话密钥

            forgotPasswordForm.addEventListener('submit', async function(e) {
                e.preventDefault(); // 阻止默认提交

                // 获取表单数据
                const formData = new FormData(forgotPasswordForm);
                const email = formData.get('email');

                // 基础验证
                if (!email) {
                    showToast('请输入邮箱地址', 'error');
                    return;
                }

                try {
                    // 检查是否需要验证码（图形验证码）
                    const needsCaptcha = await checkCaptchaRequired('reset');

                    if (needsCaptcha) {
                        // 显示验证码模态弹窗
                        const captchaResult = await showCaptchaModal();
                        if (!captchaResult.success) {
                            return; // 用户取消或验证失败
                        }
                        captchaSessionKey = captchaResult.sessionKey;
                    }

                    // 强制使用邮箱验证码流程
                    // 发送邮箱验证码，然后显示验证码输入弹窗
                    await performPasswordResetWithVerification(email, captchaSessionKey);

                } catch (error) {
                    console.error('密码重置过程出错:', error);
                    showToast(error.message || '发送失败，请重试', 'error');
                    resetResetButton();
                }
            });

            // 设置重置按钮加载状态
            function setResetButtonLoading(loading) {
                if (loading) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = `
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="loading-spinner">
                            <path d="M21 12a9 9 0 11-6.219-8.56"/>
                        </svg>
                        发送中...
                    `;
                } else {
                    resetResetButton();
                }
            }

            // 重置重置按钮
            function resetResetButton() {
                submitBtn.disabled = false;
                submitBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                        <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                    发送重置链接
                `;
            }

            // 执行密码重置（带邮箱验证码的方式）
            async function performPasswordResetWithVerification(email, captchaSessionKey) {
                setResetButtonLoading(true);

                try {
                    const resetData = {
                        action: 'send_code',
                        email: email
                    };

                    if (captchaSessionKey) {
                        resetData.captcha_session_key = captchaSessionKey;
                    }

                    const response = await fetch('api/forgot-password.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(resetData)
                    });

                    const data = await response.json();

                    if (data.success) {
                        // 显示验证码发送成功提示
                        showToast('验证码已发送到您的邮箱，请查收', 'success');
                        // 显示验证码输入弹窗
                        showVerifyCodeModal(email);
                    } else {
                        showToast(data.message || '发送失败', 'error');
                        resetResetButton();
                    }

                } catch (error) {
                    console.error('密码重置请求失败:', error);
                    showToast('网络错误，请重试', 'error');
                    resetResetButton();
                }
            }



        }

        // 验证验证码（第二步：验证码验证）
        async function verifyResetCode(email, code) {
            try {
                console.log('开始验证验证码:', { email, code });

                const verifyData = {
                    action: 'verify_code',
                    email: email,
                    code: code
                };

                console.log('发送验证请求:', verifyData);

                const response = await fetch('api/forgot-password.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(verifyData)
                });

                console.log('响应状态:', response.status);
                const data = await response.json();
                console.log('响应数据:', data);

                if (data.success) {
                    console.log('验证成功，显示成功弹窗');
                    // 设置验证状态
                    isEmailVerified = true;
                    verifiedEmail = email;
                    // 验证成功，显示重置邮件发送成功提示
                    showPasswordResetSuccessModal(email);
                    return true;
                } else {
                    console.log('验证失败:', data.message);
                    showToast(data.message || '验证失败', 'error');
                    return false;
                }

            } catch (error) {
                console.error('验证码验证失败:', error);
                showToast('网络错误，请重试', 'error');
                return false;
            }
        }

        // 显示验证码输入弹窗
        function showVerifyCodeModal(email) {
            // 重置按钮状态
            const submitBtn = document.getElementById('resetBtn');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                        <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                    发送重置链接
                `;
            }

            const modal = window.showModal.custom({
                title: '输入验证码',
                content: `
                <div style="text-align: center; padding: 30px 20px; min-height: 280px;">
                    <p style="margin: 0 0 10px 0; font-size: 16px;">验证码已发送到</p>
                    <p style="margin: 0 0 20px 0; font-weight: bold; color: #3b82f6; font-size: 16px;">${email}</p>
                    <p style="color: #6b7280; font-size: 14px; margin: 0 0 30px 0;">请查收邮件并输入6位验证码</p>

                    <div style="margin: 30px 0;">
                        <input type="text" id="verifyCodeInput" placeholder="请输入6位验证码"
                               style="width: 220px; padding: 15px; border: 2px solid #e5e7eb; border-radius: 8px; text-align: center; font-size: 20px; letter-spacing: 3px; font-weight: bold;"
                               maxlength="6" pattern="[0-9]{6}"
                               oninput="this.value = this.value.replace(/[^0-9]/g, '').slice(0, 6)">
                    </div>

                    <div style="margin-top: 30px; margin-bottom: 20px;">
                        <button type="button" id="resendCodeBtn" style="background: none; border: none; color: #3b82f6; text-decoration: underline; cursor: pointer; font-size: 14px; padding: 5px 10px;">
                            重新发送验证码
                        </button>
                    </div>
                </div>
                `,
                buttons: [
                    { text: '取消', type: 'secondary', action: 'reject' },
                    {
                        text: '验证',
                        type: 'primary',
                        action: async () => {
                            console.log('点击了验证按钮');
                            const code = document.getElementById('verifyCodeInput').value.trim();
                            console.log('获取到的验证码:', code, '长度:', code.length);

                            if (!code) {
                                console.log('验证码为空');
                                showToast('请输入验证码', 'error');
                                return false; // 阻止关闭弹窗
                            }

                            if (code.length !== 6 || !/^\d{6}$/.test(code)) {
                                console.log('验证码格式不正确, 长度:', code.length, '正则测试:', /^\d{6}$/.test(code));
                                showToast('请输入6位数字验证码', 'error');
                                return false;
                            }

                            console.log('验证码格式正确，开始验证');
                            // 验证验证码
                            const success = await verifyResetCode(email, code);
                            console.log('验证结果:', success);

                            if (!success) {
                                // 验证失败时清空输入框并显示错误信息
                                const codeInput = document.getElementById('verifyCodeInput');
                                const errorMsg = document.getElementById('codeErrorMsg');

                                if (codeInput) {
                                    codeInput.value = '';
                                    codeInput.style.borderColor = '#ef4444'; // 红色边框
                                    codeInput.focus(); // 重新聚焦到输入框
                                }

                                if (errorMsg) {
                                    errorMsg.textContent = '验证码错误，请重新输入';
                                    // 3秒后清除错误信息和红色边框
                                    setTimeout(() => {
                                        errorMsg.textContent = '';
                                        if (codeInput) {
                                            codeInput.style.borderColor = '#e5e7eb';
                                        }
                                    }, 3000);
                                }
                            }

                            return success; // 只有验证成功才关闭弹窗
                        }
                    }
                ]
            });

            // 绑定重新发送验证码事件
            setTimeout(() => {
                const resendBtn = document.getElementById('resendCodeBtn');
                if (resendBtn) {
                    resendBtn.addEventListener('click', async () => {
                        resendBtn.disabled = true;
                        resendBtn.textContent = '发送中...';

                        try {
                            const resetData = {
                                action: 'send_code',
                                email: email
                            };

                            const response = await fetch('api/forgot-password.api.php', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify(resetData)
                            });

                            const data = await response.json();

                            if (data.success) {
                                showToast('验证码已重新发送', 'success');
                            } else {
                                showToast(data.message || '发送失败', 'error');
                            }
                        } catch (error) {
                            showToast('网络错误，请重试', 'error');
                        } finally {
                            resendBtn.disabled = false;
                            resendBtn.textContent = '重新发送验证码';
                        }
                    });
                }

                // 自动聚焦到验证码输入框
                const codeInput = document.getElementById('verifyCodeInput');
                if (codeInput) {
                    codeInput.focus();

                    // 只允许输入数字
                    codeInput.addEventListener('input', (e) => {
                        e.target.value = e.target.value.replace(/[^0-9]/g, '');
                    });
                }
            }, 100);
        }

        // 检查是否需要图形验证码
        async function checkCaptchaRequired(type = 'reset') {
            try {
                const response = await fetch(`auth/SystemConfig.php?action=get_setting&key=security.enable_${type}_captcha`);
                const data = await response.json();
                return data.success && data.value === '1';
            } catch (error) {
                console.error('检查验证码设置失败:', error);
                return false; // 默认不需要验证码
            }
        }



        // 刷新当前验证码
        async function refreshCurrentCaptcha() {
            // 查找当前显示的验证码元素
            const captchaContainer = document.querySelector('.captcha-container');
            if (captchaContainer) {
                // 重新生成验证码内容
                await regenerateCaptchaContent(captchaContainer);
            }
        }

        // 重新生成验证码内容
        async function regenerateCaptchaContent(container) {
            try {
                // 获取验证码设置
                const captchaType = await getCaptchaSetting('captcha_type', 'math');
                const captchaDifficulty = await getCaptchaSetting('captcha_difficulty', 'medium');

                // 生成新的验证码
                const captchaResponse = await fetch('api/captcha.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'generate',
                        type: captchaType,
                        difficulty: captchaDifficulty
                    })
                });

                const captchaData = await captchaResponse.json();
                if (captchaData.success) {
                    // 更新验证码内容
                    const newContent = createCaptchaContent(captchaData);
                    container.innerHTML = newContent;

                    // 更新全局的验证码数据
                    window.currentCaptchaData = captchaData;
                }
            } catch (error) {
                console.error('刷新验证码失败:', error);
            }
        }

        // 全局刷新验证码函数
        window.refreshCaptchaFunction = async function(btn) {
            if (btn.disabled) return;

            btn.disabled = true;
            const originalHTML = btn.innerHTML;
            btn.innerHTML = `
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 4px;">
                    <path d="M21 12a9 9 0 11-6.219-8.56"/>
                </svg>
                <span>刷新中...</span>
            `;

            try {
                const container = document.querySelector('.captcha-container');
                if (container) {
                    await regenerateCaptchaContent(container);

                    // 重新聚焦输入框
                    setTimeout(() => {
                        const newAnswerInput = document.querySelector('#captcha-answer');
                        if (newAnswerInput && newAnswerInput.type !== 'hidden') {
                            newAnswerInput.focus();
                        }
                    }, 100);
                }
            } catch (error) {
                console.error('刷新验证码失败:', error);
                btn.disabled = false;
                btn.innerHTML = originalHTML;
            }
        };

        // 获取验证码设置
        async function getCaptchaSetting(key, defaultValue) {
            try {
                const response = await fetch(`auth/SystemConfig.php?action=get_setting&key=security.${key}`);
                const data = await response.json();
                return data.success ? data.value : defaultValue;
            } catch (error) {
                return defaultValue;
            }
        }

        // 显示验证码模态弹窗（复用注册页面的函数）
        async function showCaptchaModal() {
            try {
                // 获取验证码设置
                const captchaType = await getCaptchaSetting('captcha_type', 'math');
                const captchaDifficulty = await getCaptchaSetting('captcha_difficulty', 'medium');

                // 生成验证码
                const captchaResponse = await fetch('api/captcha.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'generate',
                        type: captchaType,
                        difficulty: captchaDifficulty
                    })
                });

                const captchaData = await captchaResponse.json();
                if (!captchaData.success) {
                    throw new Error(captchaData.message || '生成验证码失败');
                }

                // 创建验证码内容
                const captchaContent = createCaptchaContent(captchaData);

                // 显示模态弹窗
                return new Promise((resolve) => {
                    showModal.custom({
                        title: '🔐 安全验证',
                        content: captchaContent,
                        buttons: [
                            { text: '取消', type: 'secondary', value: 'cancel' },
                            { text: '验证', type: 'primary', value: 'verify' }
                        ],
                        closeOnBackdrop: false,
                        closeOnEscape: false,
                        onShow: function() {
                            // 如果是滑块验证码，初始化滑块功能
                            if (captchaData.type === 'slider') {
                                initSliderCaptcha(captchaData.target_position || 75);
                            }

                            // 自动聚焦到输入框
                            const answerInput = document.querySelector('#captcha-answer');
                            if (answerInput && answerInput.type !== 'hidden') {
                                setTimeout(() => answerInput.focus(), 100);
                            }

                            // 添加回车键监听
                            if (answerInput && answerInput.type !== 'hidden') {
                                answerInput.addEventListener('keypress', function(e) {
                                    if (e.key === 'Enter') {
                                        e.preventDefault();
                                        document.querySelector('.modal-btn-primary').click();
                                    }
                                });
                            }
                        }
                    }).then(async (result) => {
                        if (result === 'cancel') {
                            resolve({ success: false });
                            return;
                        }

                        if (result === 'verify') {
                            // 获取用户输入的答案
                            const answerInput = document.querySelector('#captcha-answer');
                            const answer = answerInput ? answerInput.value.trim() : '';

                            if (!answer) {
                                showToast('请输入验证码', 'warning');
                                // 重新显示验证码弹窗
                                const retryResult = await showCaptchaModal();
                                resolve(retryResult);
                                return;
                            }

                            try {
                                // 验证答案
                                const verifyResponse = await fetch('api/captcha.api.php', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify({
                                        action: 'verify',
                                        answer: answer,
                                        session_key: captchaData.session_key
                                    })
                                });

                                const verifyData = await verifyResponse.json();
                                if (!verifyData.success) {
                                    showToast(verifyData.message || '验证码错误', 'error');
                                    // 重新显示验证码弹窗
                                    const retryResult = await showCaptchaModal();
                                    resolve(retryResult);
                                    return;
                                }

                                // 验证成功，直接继续密码重置流程，不显示成功提示
                                resolve({
                                    success: true,
                                    sessionKey: captchaData.session_key
                                });

                            } catch (error) {
                                console.error('验证码验证失败:', error);
                                showToast('验证失败，请重试', 'error');
                                // 重新显示验证码弹窗
                                const retryResult = await showCaptchaModal();
                                resolve(retryResult);
                            }
                        }
                    });
                });

            } catch (error) {
                console.error('验证码模态弹窗错误:', error);
                showToast(error.message || '验证码验证失败', 'error');
                return { success: false };
            }
        }

        // 创建验证码内容（复用登录页面的函数）
        function createCaptchaContent(captchaData) {
            let content = `
                <div class="captcha-container" style="text-align: center; padding: 1.5rem;">
                    <p style="margin-bottom: 1.5rem; color: var(--text-secondary); font-size: 14px;">为了您的账户安全，请完成以下验证：</p>
            `;

            if (captchaData.type === 'math') {
                content += `
                    <div class="math-captcha" style="margin-bottom: 1.5rem;">
                        <div style="font-size: 28px; font-weight: 600; color: var(--text-primary); margin-bottom: 1rem; font-family: 'Courier New', monospace;">
                            ${captchaData.question}
                        </div>
                        <input type="text" id="captcha-answer" placeholder="请输入答案"
                               style="width: 200px; padding: 12px 16px; border: 2px solid var(--border-color); border-radius: 8px; text-align: center; font-size: 16px; background: var(--bg-secondary); color: var(--text-primary); transition: all 0.2s ease;"
                               autocomplete="off">
                    </div>
                `;
            } else if (captchaData.type === 'alphanumeric') {
                content += `
                    <div class="alphanumeric-captcha" style="margin-bottom: 1.5rem;">
                        <div style="font-size: 24px; font-weight: 600; color: var(--text-primary); margin-bottom: 1rem; letter-spacing: 4px; font-family: 'Courier New', monospace; background: var(--bg-tertiary); padding: 12px 20px; border-radius: 8px; border: 2px solid var(--border-color);">
                            ${captchaData.question}
                        </div>
                        <input type="text" id="captcha-answer" placeholder="请输入验证码"
                               style="width: 200px; padding: 12px 16px; border: 2px solid var(--border-color); border-radius: 8px; text-align: center; font-size: 16px; letter-spacing: 2px; background: var(--bg-secondary); color: var(--text-primary); transition: all 0.2s ease;"
                               autocomplete="off">
                    </div>
                `;
            } else if (captchaData.type === 'slider') {
                content += `
                    <div class="slider-captcha" style="margin-bottom: 1.5rem;">
                        <p style="margin-bottom: 1rem; color: var(--text-secondary);">${captchaData.question}</p>
                        <div style="width: 300px; height: 44px; background: var(--bg-tertiary); border: 2px solid var(--border-color); border-radius: 22px; position: relative; margin: 0 auto; overflow: hidden;">
                            <div id="slider-track" style="width: 100%; height: 100%; border-radius: 20px; position: relative; cursor: pointer;">
                                <div id="slider-thumb" style="width: 40px; height: 40px; background: var(--primary-color); border-radius: 50%; position: absolute; top: 2px; left: 2px; cursor: grab; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; box-shadow: 0 2px 8px rgba(0,0,0,0.2); transition: all 0.2s ease;">
                                    →
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="captcha-answer" value="">
                    </div>
                `;
            }

            content += `
                    <div style="margin-top: 1rem; text-align: center;">
                        <button type="button" class="refresh-captcha-btn" id="refresh-captcha-btn"
                                style="background: #f8f9fa; border: 1px solid #dee2e6; color: #6c757d; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px; transition: all 0.2s ease; margin-bottom: 1rem; user-select: none;"
                                onmouseover="this.style.background='#e9ecef'"
                                onmouseout="this.style.background='#f8f9fa'"
                                onclick="window.refreshCaptchaFunction(this)">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 4px; pointer-events: none;">
                                <polyline points="1 4 1 10 7 10"/>
                                <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                            </svg>
                            <span style="pointer-events: none;">刷新验证码</span>
                        </button>
                    </div>
                    <p style="font-size: 12px; color: var(--text-tertiary); opacity: 0.8;">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 4px;">
                            <circle cx="12" cy="12" r="10"/>
                            <polyline points="12,6 12,12 16,14"/>
                        </svg>
                        验证码有效期5分钟，请及时完成验证
                    </p>
                </div>
                <style>
                    #captcha-answer:focus {
                        border-color: var(--primary-color) !important;
                        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1) !important;
                        outline: none !important;
                    }
                    #captcha-answer:hover {
                        border-color: var(--primary-color) !important;
                    }
                    #slider-thumb:hover {
                        transform: scale(1.05);
                        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    }
                    #slider-thumb:active {
                        transform: scale(0.95);
                    }
                </style>
            `;

            return content;
        }

        // 初始化滑块验证码（复用登录页面的函数）
        function initSliderCaptcha(targetPosition) {
            const slider = document.getElementById('slider-thumb');
            const track = document.getElementById('slider-track');
            const answerInput = document.getElementById('captcha-answer');

            if (!slider || !track || !answerInput) return;

            let isDragging = false;
            let startX = 0;
            let currentX = 0;

            // 鼠标事件
            slider.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);

            // 触摸事件（移动端支持）
            slider.addEventListener('touchstart', startDrag);
            document.addEventListener('touchmove', drag);
            document.addEventListener('touchend', endDrag);

            function startDrag(e) {
                isDragging = true;
                startX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
                slider.style.cursor = 'grabbing';
                e.preventDefault();
            }

            function drag(e) {
                if (!isDragging) return;

                currentX = (e.type === 'mousemove' ? e.clientX : e.touches[0].clientX) - startX;
                const trackWidth = track.offsetWidth - slider.offsetWidth;
                const newPosition = Math.max(0, Math.min(currentX, trackWidth));

                slider.style.left = newPosition + 'px';

                // 计算完成百分比
                const percentage = (newPosition / trackWidth) * 100;

                // 检查是否接近目标位置（允许5%的误差）
                if (Math.abs(percentage - targetPosition) <= 5) {
                    slider.style.background = 'var(--success-color)';
                    slider.innerHTML = '✓';
                    slider.style.boxShadow = '0 4px 12px rgba(var(--success-rgb), 0.4)';
                    track.style.background = 'linear-gradient(90deg, var(--success-color) 0%, var(--bg-tertiary) 100%)';
                    answerInput.value = 'success';
                } else {
                    slider.style.background = 'var(--primary-color)';
                    slider.innerHTML = '→';
                    slider.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                    track.style.background = 'var(--bg-tertiary)';
                    answerInput.value = '';
                }

                e.preventDefault();
            }

            function endDrag() {
                if (!isDragging) return;
                isDragging = false;
                slider.style.cursor = 'grab';
            }
        }

        // 显示密码重置邮件发送成功提示弹窗
        function showPasswordResetSuccessModal(email) {
            window.showModal.success(
                '重置链接已发送！',
                `
                <div style="text-align: center; padding: 1rem 0;">
                    <p style="margin-bottom: 1.5rem; line-height: 1.6;">
                        密码重置链接已发送到<br>
                        <strong style="color: var(--primary-color);">${email}</strong><br>
                        请查收邮件并点击其中的重置链接来设置新密码。
                    </p>
                    <div style="background: var(--bg-tertiary); border-radius: 8px; padding: 1rem; margin-bottom: 1.5rem; border-left: 4px solid var(--primary-color);">
                        <p style="margin: 0; font-size: 14px; color: var(--text-secondary); text-align: left;">
                            <strong>📌 重要提示：</strong><br>
                            • 重置链接有效期为 1 小时<br>
                            • 请检查垃圾邮件文件夹<br>
                            • 如果未收到邮件，可以重新发送
                        </p>
                    </div>
                    <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                        <button type="button" class="btn btn-primary" onclick="goToLoginFromReset()" style="padding: 8px 16px; border-radius: 6px; border: none; background: var(--primary-color); color: white; cursor: pointer;">
                            返回登录页面
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resendResetEmail('${email}')" style="padding: 8px 16px; border-radius: 6px; border: 1px solid var(--border-color); background: transparent; color: var(--text-primary); cursor: pointer;">
                            重新发送重置邮件
                        </button>
                    </div>
                    <p style="font-size: 12px; color: var(--text-tertiary); opacity: 0.8; margin-top: 1rem;">
                        如果长时间未收到邮件，请检查邮箱设置或联系管理员
                    </p>
                </div>
                `,
                {
                    closeOnBackdrop: false,
                    closeOnEscape: false
                }
            );
        }

        // 返回登录页面
        function goToLoginFromReset() {
            window.location.href = 'login.php?reset_sent=1';
        }

        // 重新发送重置邮件
        async function resendResetEmail(email) {
            try {
                // 检查验证状态，决定发送验证码还是直接发送重置链接
                if (isEmailVerified && verifiedEmail === email) {
                    // 已验证过，直接发送重置链接
                    showToast('正在重新发送重置链接...', 'info');

                    const response = await fetch('api/forgot-password.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'resend_reset_email',
                            email: email
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        showToast('重置链接已重新发送，请查收', 'success');
                    } else {
                        showToast(data.message || '发送失败，请稍后重试', 'error');
                    }
                } else {
                    // 未验证过，重新发送验证码
                    showToast('正在重新发送验证码...', 'info');

                    const response = await fetch('api/forgot-password.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'send_code',
                            email: email
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        showToast('验证码已重新发送，请查收', 'success');
                    } else {
                        showToast(data.message || '发送失败，请稍后重试', 'error');
                    }
                }

            } catch (error) {
                console.error('重发邮件错误:', error);
                showToast('网络错误，请稍后重试', 'error');
            }
        }

        // 当页面加载完成时初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeForgotPasswordPage);
        } else {
            initializeForgotPasswordPage();
        }
    </script>
</body>
</html>
