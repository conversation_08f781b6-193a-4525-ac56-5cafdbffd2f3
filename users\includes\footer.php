<?php
/**
 * 📄 仪表盘页脚组件
 * 
 * 功能：页面底部信息，包含版权、链接等
 */
?>

<footer class="dashboard-footer">
    <div class="footer-container">
        <div class="footer-content">
            <!-- 版权信息 -->
            <div class="footer-section">
                <p class="copyright">
                    © <?php echo date('Y'); ?> 现代化PHP管理系统. 保留所有权利.
                </p>
            </div>
            
            <!-- 快速链接 -->
            <div class="footer-section">
                <div class="footer-links">
                    <a href="#" class="footer-link">帮助文档</a>
                    <a href="#" class="footer-link">API文档</a>
                    <a href="#" class="footer-link">联系支持</a>
                    <a href="#" class="footer-link">隐私政策</a>
                </div>
            </div>
            
            <!-- 系统信息 -->
            <div class="footer-section">
                <div class="system-info">
                    <span class="info-item">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <polyline points="12,6 12,12 16,14"/>
                        </svg>
                        服务器时间: <?php echo date('Y-m-d H:i:s'); ?>
                    </span>
                    <span class="info-item">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                        </svg>
                        系统状态: 正常运行
                    </span>
                    <span class="info-item">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2v20m8-10H4"/>
                        </svg>
                        版本: v1.0.0
                    </span>
                </div>
            </div>
        </div>
    </div>
</footer>

<style>
/* ===== 仪表盘页脚样式 ===== */
.dashboard-footer {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
    padding: var(--spacing-6) 0;
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: var(--spacing-6);
    align-items: center;
}

.footer-section {
    display: flex;
    align-items: center;
}

.footer-section:first-child {
    justify-content: flex-start;
}

.footer-section:nth-child(2) {
    justify-content: center;
}

.footer-section:last-child {
    justify-content: flex-end;
}

/* 版权信息 */
.copyright {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

/* 快速链接 */
.footer-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.footer-link:hover {
    color: var(--primary-color);
}

.footer-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.footer-link:hover::after {
    width: 100%;
}

/* 系统信息 */
.system-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
}

.info-item svg {
    color: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
        text-align: center;
    }
    
    .footer-section {
        justify-content: center !important;
    }
    
    .system-info {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .footer-container {
        padding: 0 var(--spacing-4);
    }
    
    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-2);
    }
    
    .system-info {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .dashboard-footer {
        padding: var(--spacing-4) 0;
    }
}

@media (max-width: 480px) {
    .footer-links {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .footer-link {
        font-size: 0.8rem;
    }
    
    .info-item {
        font-size: 0.7rem;
    }
}

/* 暗色主题优化 */
[data-theme="dark"] .dashboard-footer {
    background: var(--bg-primary);
    border-top-color: var(--border-color);
}

[data-theme="dark"] .copyright,
[data-theme="dark"] .footer-link,
[data-theme="dark"] .info-item {
    color: var(--text-secondary);
}

[data-theme="dark"] .footer-link:hover {
    color: var(--primary-color);
}

/* 动画效果 */
.footer-content {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 悬停效果 */
.footer-link {
    position: relative;
    overflow: hidden;
}

.footer-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(var(--primary-rgb), 0.1), transparent);
    transition: left 0.5s ease;
}

.footer-link:hover::before {
    left: 100%;
}

/* 状态指示器 */
.info-item:nth-child(2) svg {
    color: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}
</style>

<!-- 引入主要JavaScript文件 -->
<script src="../assets/js/main.js"></script>
