<?php
/**
 * 📊 日志API接口
 * 
 * 功能：获取操作日志和登录日志数据
 * 方法：GET
 * 参数：type, page, per_page, user
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许GET请求'
    ]);
    exit;
}

// 引入认证类
require_once '../auth/Auth.php';

// 检查用户是否已登录
if (!Auth::isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
}

try {
    // 获取当前用户信息
    $current_user = Auth::getCurrentUser();
    $is_admin = Auth::hasRole(Auth::ROLE_ADMIN);
    
    // 引入数据库配置
    require_once '../config/database.php';
    $db = Database::getInstance();
    
    // 获取请求参数
    $log_type = $_GET['type'] ?? 'operation'; // operation 或 login
    $page = max(1, intval($_GET['page'] ?? 1));
    $per_page = min(100, max(10, intval($_GET['per_page'] ?? 20)));
    $user_filter = trim($_GET['user'] ?? '');
    
    $offset = ($page - 1) * $per_page;
    
    // 构建查询条件
    $where_conditions = [];
    $params = [];
    
    if (!$is_admin) {
        // 普通用户只能查看自己的日志
        $where_conditions[] = "user_id = ?";
        $params[] = $current_user['id'];
    } elseif (!empty($user_filter)) {
        // 管理员可以按用户筛选
        $where_conditions[] = "(username LIKE ? OR email LIKE ?)";
        $params[] = "%{$user_filter}%";
        $params[] = "%{$user_filter}%";
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // 获取日志数据
    if ($log_type === 'login') {
        // 登录日志
        $count_sql = "SELECT COUNT(*) as total FROM login_logs {$where_clause}";
        $data_sql = "SELECT * FROM login_logs {$where_clause} ORDER BY login_time DESC LIMIT {$per_page} OFFSET {$offset}";
    } else {
        // 操作日志
        $count_sql = "SELECT COUNT(*) as total FROM operation_logs {$where_clause}";
        $data_sql = "SELECT * FROM operation_logs {$where_clause} ORDER BY created_at DESC LIMIT {$per_page} OFFSET {$offset}";
    }
    
    $total_count = $db->fetch($count_sql, $params)['total'];
    $logs = $db->fetchAll($data_sql, $params);
    $total_pages = ceil($total_count / $per_page);
    
    // 格式化日志数据
    $formatted_logs = [];
    foreach ($logs as $log) {
        if ($log_type === 'login') {
            $formatted_logs[] = [
                'id' => $log['id'],
                'time' => $log['login_time'],
                'user' => [
                    'id' => $log['user_id'],
                    'username' => $log['username'],
                    'email' => $log['email']
                ],
                'status' => $log['login_status'],
                'ip_address' => $log['ip_address'],
                'device_info' => $log['device_info'],
                'location' => $log['location'],
                'failure_reason' => $log['failure_reason']
            ];
        } else {
            $formatted_logs[] = [
                'id' => $log['id'],
                'time' => $log['created_at'],
                'user' => [
                    'id' => $log['user_id'],
                    'username' => $log['username']
                ],
                'operation_type' => $log['operation_type'],
                'operation_desc' => $log['operation_desc'],
                'operation_data' => $log['operation_data'] ? json_decode($log['operation_data'], true) : null,
                'ip_address' => $log['ip_address'],
                'request_url' => $log['request_url'],
                'request_method' => $log['request_method'],
                'response_status' => $log['response_status'],
                'execution_time' => $log['execution_time']
            ];
        }
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'logs' => $formatted_logs,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $per_page,
                'total_count' => $total_count,
                'total_pages' => $total_pages,
                'has_prev' => $page > 1,
                'has_next' => $page < $total_pages
            ],
            'filters' => [
                'type' => $log_type,
                'user' => $user_filter,
                'is_admin' => $is_admin
            ]
        ],
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '获取日志数据失败: ' . $e->getMessage(),
        'timestamp' => time()
    ]);
}
?>
