/* ===== 加载动画组件样式 ===== */

/* 加载遮罩层 */
.loading-overlay {
    position: fixed;
    inset: 0;
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 加载容器 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-4, 1rem);
    padding: var(--spacing-6, 1.5rem);
    background: var(--bg-primary, #ffffff);
    border-radius: 20px;
    box-shadow: var(--shadow-2xl, 0 25px 50px -12px rgba(0, 0, 0, 0.25));
    transform: scale(0.9);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.loading-overlay.show .loading-container {
    transform: scale(1);
}

/* 加载文本 */
.loading-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary, #1f2937);
    margin: 0;
    text-align: center;
}

.loading-subtext {
    font-size: 0.75rem;
    color: var(--text-muted, #6b7280);
    margin: 0;
    text-align: center;
}

/* ===== 加载动画样式 ===== */

/* 1. 脉冲圆点 */
.loading-dots {
    display: flex;
    gap: 8px;
    align-items: center;
}

.loading-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--primary-color, #3b82f6);
    animation: pulse-dot 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes pulse-dot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 2. 旋转圆环 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-light, #e5e7eb);
    border-top: 3px solid var(--primary-color, #3b82f6);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 3. 波浪动画 */
.loading-wave {
    display: flex;
    gap: 4px;
    align-items: center;
}

.loading-wave-bar {
    width: 4px;
    height: 20px;
    background: var(--primary-color, #3b82f6);
    border-radius: 2px;
    animation: wave 1.2s ease-in-out infinite;
}

.loading-wave-bar:nth-child(1) { animation-delay: -1.1s; }
.loading-wave-bar:nth-child(2) { animation-delay: -1.0s; }
.loading-wave-bar:nth-child(3) { animation-delay: -0.9s; }
.loading-wave-bar:nth-child(4) { animation-delay: -0.8s; }
.loading-wave-bar:nth-child(5) { animation-delay: -0.7s; }

@keyframes wave {
    0%, 40%, 100% {
        transform: scaleY(0.4);
        opacity: 0.5;
    }
    20% {
        transform: scaleY(1);
        opacity: 1;
    }
}

/* 4. 弹跳球 */
.loading-bounce {
    display: flex;
    gap: 6px;
    align-items: center;
}

.loading-bounce-ball {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--primary-color, #3b82f6);
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-bounce-ball:nth-child(1) { animation-delay: -0.32s; }
.loading-bounce-ball:nth-child(2) { animation-delay: -0.16s; }
.loading-bounce-ball:nth-child(3) { animation-delay: 0s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0) translateY(0);
    }
    40% {
        transform: scale(1) translateY(-20px);
    }
}

/* 5. 渐变圆环 */
.loading-ring {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: conic-gradient(
        from 0deg,
        transparent,
        var(--primary-color, #3b82f6),
        transparent
    );
    animation: rotate 1s linear infinite;
    position: relative;
}

.loading-ring::before {
    content: '';
    position: absolute;
    inset: 3px;
    border-radius: 50%;
    background: var(--bg-primary, #ffffff);
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 6. 脉冲圆环 */
.loading-pulse {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color, #3b82f6);
    animation: pulse-ring 2s ease-in-out infinite;
    position: relative;
}

.loading-pulse::before,
.loading-pulse::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    border: 2px solid var(--primary-color, #3b82f6);
    animation: pulse-ring 2s ease-in-out infinite;
}

.loading-pulse::before {
    animation-delay: -1s;
}

@keyframes pulse-ring {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(2.5);
        opacity: 0;
    }
}

/* 7. 进度条 */
.loading-progress {
    width: 200px;
    height: 4px;
    background: var(--border-light, #e5e7eb);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.loading-progress-bar {
    height: 100%;
    background: linear-gradient(
        90deg,
        var(--primary-color, #3b82f6),
        var(--primary-light, #60a5fa),
        var(--primary-color, #3b82f6)
    );
    border-radius: 2px;
    animation: progress 2s ease-in-out infinite;
    transform: translateX(-100%);
}

@keyframes progress {
    0% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(0%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .loading-container {
        padding: var(--spacing-4, 1rem);
        margin: var(--spacing-4, 1rem);
    }
    
    .loading-progress {
        width: 150px;
    }
}

/* ===== 暗色主题适配 ===== */
[data-theme="dark"] .loading-overlay {
    background: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .loading-container {
    background: var(--bg-primary);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

/* ===== 无障碍支持 ===== */
@media (prefers-reduced-motion: reduce) {
    .loading-overlay,
    .loading-container {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    /* 保持加载动画正常运行，即使用户设置了减少动画 */
    .loading-dot,
    .loading-spinner,
    .loading-wave-bar,
    .loading-bounce-ball,
    .loading-ring,
    .loading-pulse,
    .loading-progress-bar {
        /* 注释掉这些规则，让动画正常运行 */
        /* animation-duration: 0.01ms !important; */
        /* animation-iteration-count: 1 !important; */
        /* transition-duration: 0.01ms !important; */
    }
}
