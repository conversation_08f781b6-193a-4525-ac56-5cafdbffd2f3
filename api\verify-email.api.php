<?php
/**
 * 📧 统一邮件服务 API 接口
 *
 * 功能：处理所有邮件发送和验证相关的操作
 * - 邮箱验证令牌处理
 * - 重新发送验证邮件
 * - 测试邮件发送
 * - 验证码邮件发送
 * - 注册验证邮件发送
 * - 密码重置邮件发送
 * - 邮件模板管理
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求'
    ]);
    exit;
}

// 开启错误报告（开发环境）
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入必要的类
require_once '../config/database.php';
require_once '../auth/Auth.php';
require_once '../auth/SystemConfig.php';

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

// 如果不是JSON请求，尝试从POST获取
if (!$input) {
    $input = $_POST;
}

$action = trim($input['action'] ?? '');

// 需要管理员权限的操作
$adminActions = ['test_email'];
if (in_array($action, $adminActions)) {
    if (!Auth::isLoggedIn() || !Auth::hasRole(Auth::ROLE_ADMIN)) {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'message' => '权限不足'
        ]);
        exit;
    }
}

try {
    // 记录请求日志
    error_log("Email API 请求 - Action: {$action}");

    switch ($action) {
        case 'verify_token':
            $result = handleTokenVerification($input);
            break;

        case 'resend_email':
            $result = handleResendEmail($input);
            break;

        case 'set_pending_email':
            $result = handleSetPendingEmail($input);
            break;

        case 'test_email':
            $result = handleTestEmail($input);
            break;

        case 'send_verify_code':
            $result = handleSendVerifyCode($input);
            break;

        case 'send_registration_email':
            $result = handleSendRegistrationEmail($input);
            break;

        case 'send_password_reset_email':
            $result = handleSendPasswordResetEmail($input);
            break;

        case 'resend_verification_email':
            $result = handleResendVerificationEmail($input);
            break;

        default:
            throw new Exception('无效的操作类型');
    }

    echo json_encode($result);

} catch (Exception $e) {
    error_log("Email API 错误: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => time()
    ]);
}

/**
 * 处理令牌验证
 */
function handleTokenVerification($input) {
    $token = trim($input['token'] ?? '');

    if (empty($token)) {
        throw new Exception('验证令牌不能为空');
    }

    $result = Auth::verifyEmailByToken($token);

    return [
        'success' => $result['success'],
        'message' => $result['message'],
        'status' => $result['status'] ?? 'unknown',
        'timestamp' => time()
    ];
}



/**
 * 处理重新发送邮件
 */
function handleResendEmail($input) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // 从session中获取待验证的邮箱
    $email = $_SESSION['pending_verification_email'] ?? '';
    if (empty($email)) {
        throw new Exception('验证会话已过期，请重新注册');
    }

    $result = Auth::resendVerificationEmail($email);

    return [
        'success' => $result['success'],
        'message' => $result['message'],
        'timestamp' => time()
    ];
}

/**
 * 设置待验证邮箱
 */
function handleSetPendingEmail($input) {
    session_start();

    $email = trim($input['email'] ?? '');

    if (empty($email)) {
        throw new Exception('邮箱地址不能为空');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('邮箱地址格式不正确');
    }

    // 设置session中的待验证邮箱
    $_SESSION['pending_verification_email'] = $email;

    return [
        'success' => true,
        'message' => '邮箱设置成功',
        'timestamp' => time()
    ];
}

/**
 * 处理测试邮件发送
 */
function handleTestEmail($input) {
    $emailSettings = $input['settings'] ?? [];
    $testEmail = trim($input['test_email'] ?? '');

    // 验证测试邮箱
    if (empty($testEmail)) {
        throw new Exception('请提供测试邮箱地址');
    }

    if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('测试邮箱地址格式不正确');
    }

    // 验证必要的邮件设置
    $required = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'from_email'];
    foreach ($required as $field) {
        if (empty($emailSettings[$field])) {
            throw new Exception("缺少必要的邮件设置：{$field}");
        }
    }

    // 验证发件人邮箱格式
    if (!filter_var($emailSettings['from_email'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception('发件人邮箱格式不正确');
    }

    if (!filter_var($emailSettings['smtp_username'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception('SMTP用户名邮箱格式不正确');
    }

    if (!is_numeric($emailSettings['smtp_port']) || $emailSettings['smtp_port'] < 1 || $emailSettings['smtp_port'] > 65535) {
        throw new Exception('SMTP端口号不正确');
    }

    // 尝试发送测试邮件
    try {
        $success = EmailService::sendTestEmail($emailSettings, $testEmail);
        if ($success) {
            return [
                'success' => true,
                'message' => "测试邮件已成功发送到 {$testEmail}，请检查邮箱（包括垃圾邮件文件夹）"
            ];
        } else {
            throw new Exception('邮件发送失败，请检查SMTP设置');
        }
    } catch (Exception $e) {
        // 记录详细错误信息
        error_log("测试邮件发送失败 - 邮箱: {$testEmail}, 错误: " . $e->getMessage());

        // 返回用户友好的错误信息
        $errorMessage = $e->getMessage();
        if (strpos($errorMessage, '无法连接') !== false) {
            $errorMessage = 'SMTP服务器连接失败，请检查服务器地址和端口';
        } elseif (strpos($errorMessage, '密码验证失败') !== false) {
            $errorMessage = 'SMTP用户名或密码错误，请检查邮箱账号设置';
        } elseif (strpos($errorMessage, 'EHLO') !== false) {
            $errorMessage = 'SMTP服务器不支持当前连接方式，请检查加密方式设置';
        }

        throw new Exception('邮件发送失败：' . $errorMessage);
    }
}

/**
 * 处理发送验证码邮件
 */
function handleSendVerifyCode($input) {
    $email = trim($input['email'] ?? '');
    $code = trim($input['code'] ?? '');
    $actionType = trim($input['action_type'] ?? '验证');
    $expireMinutes = intval($input['expire_minutes'] ?? 5);

    if (empty($email)) {
        throw new Exception('邮箱地址不能为空');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('邮箱地址格式不正确');
    }

    if (empty($code)) {
        throw new Exception('验证码不能为空');
    }

    try {
        $success = EmailService::sendVerifyCodeEmail($email, $code, $actionType, $expireMinutes);
        return [
            'success' => true,
            'message' => '验证码邮件发送成功'
        ];
    } catch (Exception $e) {
        throw new Exception('验证码邮件发送失败：' . $e->getMessage());
    }
}

/**
 * 处理发送注册验证邮件
 */
function handleSendRegistrationEmail($input) {
    $email = trim($input['email'] ?? '');
    $verifyToken = trim($input['verify_token'] ?? '');

    if (empty($email)) {
        throw new Exception('邮箱地址不能为空');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('邮箱地址格式不正确');
    }

    if (empty($verifyToken)) {
        throw new Exception('验证令牌不能为空');
    }

    try {
        $success = EmailService::sendRegistrationEmail($email, $verifyToken);
        return [
            'success' => true,
            'message' => '注册验证邮件发送成功'
        ];
    } catch (Exception $e) {
        throw new Exception('注册验证邮件发送失败：' . $e->getMessage());
    }
}

/**
 * 处理发送密码重置邮件
 */
function handleSendPasswordResetEmail($input) {
    $email = trim($input['email'] ?? '');
    $resetToken = trim($input['reset_token'] ?? '');

    if (empty($email)) {
        throw new Exception('邮箱地址不能为空');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('邮箱地址格式不正确');
    }

    if (empty($resetToken)) {
        throw new Exception('重置令牌不能为空');
    }

    try {
        $success = EmailService::sendPasswordResetEmail($email, $resetToken);
        return [
            'success' => true,
            'message' => '密码重置邮件发送成功'
        ];
    } catch (Exception $e) {
        throw new Exception('密码重置邮件发送失败：' . $e->getMessage());
    }
}

/**
 * 处理重新发送验证邮件
 */
function handleResendVerificationEmail($input) {
    $email = trim($input['email'] ?? '');

    if (empty($email)) {
        throw new Exception('邮箱地址不能为空');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('邮箱地址格式不正确');
    }

    try {
        $result = Auth::resendVerificationEmail($email);
        return $result;
    } catch (Exception $e) {
        throw new Exception('重新发送验证邮件失败：' . $e->getMessage());
    }
}

/**
 * 邮件服务类
 */
class EmailService {

    /**
     * 发送测试邮件
     */
    public static function sendTestEmail($emailSettings, $testEmail) {
        // 获取网站信息
        $siteName = SystemConfig::get('website', 'site_name', '管理系统');

        // 邮件内容
        $subject = "[{$siteName}] 邮件配置测试";
        $message = self::getTestEmailTemplate($siteName, $testEmail, $emailSettings);

        return self::sendSMTPEmail(
            $emailSettings['smtp_host'],
            $emailSettings['smtp_port'],
            $emailSettings['smtp_username'],
            $emailSettings['smtp_password'],
            $emailSettings['from_email'],
            $testEmail,
            $subject,
            $message,
            $emailSettings['smtp_encryption'] ?? 'ssl'
        );
    }

    /**
     * 发送验证码邮件
     */
    public static function sendVerifyCodeEmail($email, $code, $actionType = '验证', $expireMinutes = 5) {
        // 获取邮件设置
        $emailSettings = self::getEmailSettings();

        if (!$emailSettings) {
            throw new Exception('邮件服务未配置');
        }

        $siteName = SystemConfig::get('website', 'site_name', '管理系统');
        $subject = "[{$siteName}] 您的验证码";
        $message = self::getVerifyCodeEmailTemplate($siteName, $code, $actionType, $expireMinutes);

        return self::sendSMTPEmail(
            $emailSettings['smtp_host'],
            $emailSettings['smtp_port'],
            $emailSettings['smtp_username'],
            $emailSettings['smtp_password'],
            $emailSettings['from_email'],
            $email,
            $subject,
            $message,
            $emailSettings['smtp_encryption'] ?? 'ssl'
        );
    }

    /**
     * 发送注册验证邮件
     */
    public static function sendRegistrationEmail($email, $verifyToken) {
        // 获取邮件设置
        $emailSettings = self::getEmailSettings();

        if (!$emailSettings) {
            throw new Exception('邮件服务未配置');
        }

        $siteName = SystemConfig::get('website', 'site_name', '管理系统');
        $subject = "[{$siteName}] 请验证您的邮箱";
        $verifyUrl = self::getBaseUrl() . "/verify-email.php?token=" . urlencode($verifyToken);
        $message = self::getRegistrationEmailTemplate($siteName, $verifyUrl);

        return self::sendSMTPEmail(
            $emailSettings['smtp_host'],
            $emailSettings['smtp_port'],
            $emailSettings['smtp_username'],
            $emailSettings['smtp_password'],
            $emailSettings['from_email'],
            $email,
            $subject,
            $message,
            $emailSettings['smtp_encryption'] ?? 'ssl'
        );
    }

    /**
     * 发送密码重置邮件
     */
    public static function sendPasswordResetEmail($email, $resetToken) {
        // 获取邮件设置
        $emailSettings = self::getEmailSettings();

        if (!$emailSettings) {
            throw new Exception('邮件服务未配置');
        }

        $siteName = SystemConfig::get('website', 'site_name', '管理系统');
        $subject = "[{$siteName}] 密码重置";
        $resetUrl = self::getBaseUrl() . "/reset-password.php?token=" . urlencode($resetToken);
        $message = self::getPasswordResetEmailTemplate($siteName, $resetUrl);

        return self::sendSMTPEmail(
            $emailSettings['smtp_host'],
            $emailSettings['smtp_port'],
            $emailSettings['smtp_username'],
            $emailSettings['smtp_password'],
            $emailSettings['from_email'],
            $email,
            $subject,
            $message,
            $emailSettings['smtp_encryption'] ?? 'ssl'
        );
    }

    /**
     * 使用SMTP发送邮件
     */
    private static function sendSMTPEmail($host, $port, $username, $password, $from, $to, $subject, $body, $encryption = 'ssl') {
        // 创建socket连接
        $context = stream_context_create();

        if ($encryption === 'ssl') {
            $host = 'ssl://' . $host;
        }

        $socket = @stream_socket_client("$host:$port", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);

        if (!$socket) {
            throw new Exception("无法连接到SMTP服务器: $errstr ($errno)");
        }

        // 读取服务器响应
        $response = fgets($socket, 512);
        if (substr($response, 0, 3) !== '220') {
            fclose($socket);
            throw new Exception("SMTP服务器连接失败: $response");
        }

        // EHLO命令
        fwrite($socket, "EHLO localhost\r\n");

        // 读取所有EHLO响应行
        $ehlo_response = '';
        do {
            $response = fgets($socket, 512);
            $ehlo_response .= $response;
            // 检查是否是最后一行（第4个字符不是'-'）
        } while (isset($response[3]) && $response[3] === '-');

        if (substr($response, 0, 3) !== '250') {
            fclose($socket);
            throw new Exception("EHLO命令失败: $ehlo_response");
        }

        // 如果是TLS加密，发送STARTTLS命令
        if ($encryption === 'tls') {
            fwrite($socket, "STARTTLS\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) !== '220') {
                fclose($socket);
                throw new Exception("STARTTLS命令失败: $response");
            }

            // 启用TLS加密
            if (!stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                fclose($socket);
                throw new Exception("TLS加密启用失败");
            }

            // 重新发送EHLO
            fwrite($socket, "EHLO localhost\r\n");

            // 读取所有TLS EHLO响应行
            $tls_ehlo_response = '';
            do {
                $response = fgets($socket, 512);
                $tls_ehlo_response .= $response;
                // 检查是否是最后一行（第4个字符不是'-'）
            } while (isset($response[3]) && $response[3] === '-');

            if (substr($response, 0, 3) !== '250') {
                fclose($socket);
                throw new Exception("TLS EHLO命令失败: $tls_ehlo_response");
            }
        }

        // AUTH LOGIN命令
        fwrite($socket, "AUTH LOGIN\r\n");
        $response = fgets($socket, 512);
        if (substr($response, 0, 3) !== '334') {
            fclose($socket);
            throw new Exception("AUTH LOGIN命令失败: $response");
        }

        // 发送用户名
        fwrite($socket, base64_encode($username) . "\r\n");
        $response = fgets($socket, 512);
        if (substr($response, 0, 3) !== '334') {
            fclose($socket);
            throw new Exception("用户名验证失败: $response");
        }

        // 发送密码
        fwrite($socket, base64_encode($password) . "\r\n");
        $response = fgets($socket, 512);
        if (substr($response, 0, 3) !== '235') {
            fclose($socket);
            throw new Exception("密码验证失败，请检查SMTP用户名和密码: $response");
        }

        // MAIL FROM命令
        fwrite($socket, "MAIL FROM: <$from>\r\n");
        $response = fgets($socket, 512);
        if (substr($response, 0, 3) !== '250') {
            fclose($socket);
            throw new Exception("MAIL FROM命令失败: $response");
        }

        // RCPT TO命令
        fwrite($socket, "RCPT TO: <$to>\r\n");
        $response = fgets($socket, 512);
        if (substr($response, 0, 3) !== '250') {
            fclose($socket);
            throw new Exception("RCPT TO命令失败: $response");
        }

        // DATA命令
        fwrite($socket, "DATA\r\n");
        $response = fgets($socket, 512);
        if (substr($response, 0, 3) !== '354') {
            fclose($socket);
            throw new Exception("DATA命令失败: $response");
        }

        // 发送邮件头和内容
        $headers = "From: $from\r\n";
        $headers .= "To: $to\r\n";
        $headers .= "Subject: =?UTF-8?B?" . base64_encode($subject) . "?=\r\n";
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
        $headers .= "Content-Transfer-Encoding: base64\r\n";
        $headers .= "\r\n";

        fwrite($socket, $headers);
        fwrite($socket, chunk_split(base64_encode($body)));
        fwrite($socket, "\r\n.\r\n");

        $response = fgets($socket, 512);
        if (substr($response, 0, 3) !== '250') {
            fclose($socket);
            throw new Exception("邮件发送失败: $response");
        }

        // QUIT命令
        fwrite($socket, "QUIT\r\n");
        fclose($socket);

        return true;
    }

    /**
     * 获取邮件设置
     */
    private static function getEmailSettings() {
        try {
            $db = Database::getInstance();
            $stmt = $db->query("SELECT setting_key, setting_value FROM settings WHERE category = 'email'");
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // 检查必要的设置
            $required = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'from_email'];
            foreach ($required as $field) {
                if (empty($settings[$field])) {
                    return false;
                }
            }

            return $settings;
        } catch (Exception $e) {
            error_log("获取邮件设置失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取基础URL
     */
    private static function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host;
    }

    /**
     * 获取测试邮件模板
     */
    private static function getTestEmailTemplate($siteName, $testEmail, $emailSettings) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>邮件配置测试</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
                .content { padding: 30px; }
                .success { color: #28a745; font-weight: bold; padding: 15px; background: #d4edda; border-radius: 5px; margin: 20px 0; }
                .info { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 12px; }
                h2 { margin: 0; font-size: 24px; }
                p { line-height: 1.6; color: #333; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>📧 邮件配置测试</h2>
                </div>
                <div class='content'>
                    <p>您好！</p>
                    <p class='success'>✅ 恭喜！您的邮件配置测试成功！</p>
                    <div class='info'>
                        <strong>测试信息：</strong><br>
                        • 发送时间：" . date('Y-m-d H:i:s') . "<br>
                        • 接收邮箱：{$testEmail}<br>
                        • SMTP服务器：{$emailSettings['smtp_host']}:{$emailSettings['smtp_port']}<br>
                        • 发件人：{$emailSettings['from_email']}
                    </div>
                    <p>如果您收到这封邮件，说明您的SMTP邮件配置已经正确设置，系统可以正常发送邮件通知。</p>
                    <p>感谢您使用我们的系统！</p>
                </div>
                <div class='footer'>
                    <p>此邮件由 {$siteName} 自动发送，请勿回复。</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }

    /**
     * 获取验证码邮件模板
     */
    private static function getVerifyCodeEmailTemplate($siteName, $code, $actionType, $expireMinutes) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>验证码</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
                .content { padding: 30px; text-align: center; }
                .code { font-size: 32px; font-weight: bold; color: #667eea; background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; letter-spacing: 5px; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 12px; }
                h2 { margin: 0; font-size: 24px; }
                p { line-height: 1.6; color: #333; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>🔐 {$actionType}验证码</h2>
                </div>
                <div class='content'>
                    <p>您好！</p>
                    <p>您的{$actionType}验证码是：</p>
                    <div class='code'>{$code}</div>
                    <p>验证码有效期为 {$expireMinutes} 分钟，请及时使用。</p>
                    <p>如果这不是您的操作，请忽略此邮件。</p>
                </div>
                <div class='footer'>
                    <p>此邮件由 {$siteName} 自动发送，请勿回复。</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }

    /**
     * 获取注册验证邮件模板
     */
    private static function getRegistrationEmailTemplate($siteName, $verifyUrl) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>邮箱验证</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
                .content { padding: 30px; }
                .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 12px; }
                h2 { margin: 0; font-size: 24px; }
                p { line-height: 1.6; color: #333; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>📧 邮箱验证</h2>
                </div>
                <div class='content'>
                    <p>您好！</p>
                    <p>感谢您注册 {$siteName}！请点击下面的按钮验证您的邮箱地址：</p>
                    <p style='text-align: center;'>
                        <a href='{$verifyUrl}' class='button'>验证邮箱</a>
                    </p>
                    <p>如果您无法点击上面的按钮，请复制以下链接到浏览器地址栏：</p>
                    <p style='word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 5px;'>{$verifyUrl}</p>
                    <p>如果这不是您的操作，请忽略此邮件。</p>
                </div>
                <div class='footer'>
                    <p>此邮件由 {$siteName} 自动发送，请勿回复。</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }

    /**
     * 获取密码重置邮件模板
     */
    private static function getPasswordResetEmailTemplate($siteName, $resetUrl) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>密码重置</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px; text-align: center; }
                .content { padding: 30px; }
                .button { display: inline-block; background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 12px; }
                h2 { margin: 0; font-size: 24px; }
                p { line-height: 1.6; color: #333; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>🔒 密码重置</h2>
                </div>
                <div class='content'>
                    <p>您好！</p>
                    <p>您请求重置 {$siteName} 的密码。请点击下面的按钮重置您的密码：</p>
                    <p style='text-align: center;'>
                        <a href='{$resetUrl}' class='button'>重置密码</a>
                    </p>
                    <p>如果您无法点击上面的按钮，请复制以下链接到浏览器地址栏：</p>
                    <p style='word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 5px;'>{$resetUrl}</p>
                    <p>此链接将在1小时后过期。</p>
                    <p>如果您没有请求密码重置，请忽略此邮件。</p>
                </div>
                <div class='footer'>
                    <p>此邮件由 {$siteName} 自动发送，请勿回复。</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }
}
?>
