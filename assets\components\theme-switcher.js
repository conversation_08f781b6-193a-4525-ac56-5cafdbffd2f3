/**
 * 主题切换组件
 * 功能丰富的主题管理系统，支持手动切换、自动切换和时间设置
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class ThemeSwitcher {
    constructor(options = {}) {
        // 配置选项
        this.options = {
            position: 'bottom-right', // 位置：top-right, top-left, bottom-right, bottom-left
            showToast: true,          // 是否显示Toast提示
            autoInit: true,           // 是否自动初始化
            ...options
        };

        // 状态管理 - 优先从设置管理器获取
        this.currentTheme = this.getSettingValue('theme') || 'light';
        this.autoMode = this.getSettingValue('autoTheme') || false;
        this.lightTime = this.getSettingValue('lightTime') || '06:00';
        this.darkTime = this.getSettingValue('darkTime') || '18:00';
        this.autoTimer = null;

        // DOM元素
        this.switcher = null;
        this.toggleButton = null;
        this.dropdown = null;

        // 如果启用自动初始化，则立即初始化
        if (this.options.autoInit) {
            this.init();
        }
    }

    /**
     * 获取设置值（优先从设置管理器获取）
     */
    getSettingValue(key) {
        if (window.settingsManager) {
            return window.settingsManager.get(key);
        }
        // 回退到localStorage
        const legacyKeys = {
            'theme': 'theme',
            'autoTheme': 'autoMode',
            'lightTime': 'lightTime',
            'darkTime': 'darkTime'
        };
        const storageKey = legacyKeys[key] || key;
        const value = localStorage.getItem(storageKey);

        if (key === 'autoTheme') {
            return value === 'true';
        }
        return value;
    }

    /**
     * 设置值（同步到设置管理器）
     */
    setSettingValue(key, value) {
        if (window.settingsManager) {
            window.settingsManager.set(key, value);
        } else {
            // 回退到localStorage
            const legacyKeys = {
                'theme': 'theme',
                'autoTheme': 'autoMode',
                'lightTime': 'lightTime',
                'darkTime': 'darkTime'
            };
            const storageKey = legacyKeys[key] || key;
            localStorage.setItem(storageKey, value);
        }
    }

    /**
     * 初始化主题切换器
     */
    init() {
        // 设置初始主题
        document.documentElement.setAttribute('data-theme', this.currentTheme);

        // 创建主题切换器
        this.createThemeSwitcher();

        // 绑定事件
        this.bindEvents();

        // 启动自动模式（如果已启用）
        if (this.autoMode) {
            this.startAutoMode();
        }

        // 添加CSS样式（如果尚未添加）
        this.loadStyles();

        console.log(`🎨 主题切换器已初始化: ${this.currentTheme}主题, 自动模式: ${this.autoMode ? '开启' : '关闭'}`);
    }

    /**
     * 加载组件样式
     */
    loadStyles() {
        const existingLink = document.querySelector('link[href*="theme-switcher.css"]');
        if (!existingLink) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'assets/components/theme-switcher.css';
            document.head.appendChild(link);
        }
    }

    /**
     * 创建主题切换器DOM结构
     */
    createThemeSwitcher() {
        const switcher = document.createElement('div');
        switcher.className = `theme-switcher theme-switcher-${this.options.position}`;

        // 调试信息
        console.log(`🎨 创建主题切换器，位置: ${this.options.position}`);
        console.log(`🎨 CSS类名: ${switcher.className}`);

        switcher.innerHTML = `
            <button class="theme-toggle" aria-label="主题设置">
                ${this.getThemeIcon()}
            </button>
            <div class="theme-dropdown">
                <div class="theme-dropdown-header">
                    <h3 class="theme-dropdown-title">主题设置</h3>
                </div>
                <div class="theme-dropdown-body">
                    <button class="theme-option" data-action="toggle">
                        <div class="theme-option-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                ${this.currentTheme === 'light' ? 
                                    '<path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>' : 
                                    '<circle cx="12" cy="12" r="5"/><line x1="12" y1="1" x2="12" y2="3"/><line x1="12" y1="21" x2="12" y2="23"/><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/><line x1="1" y1="12" x2="3" y2="12"/><line x1="21" y1="12" x2="23" y2="12"/><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>'
                                }
                            </svg>
                        </div>
                        <div class="theme-option-content">
                            <div class="theme-option-title">切换到${this.currentTheme === 'light' ? '暗色' : '亮色'}主题</div>
                            <div class="theme-option-desc">手动切换主题模式</div>
                        </div>
                    </button>
                    
                    <div class="theme-option" data-action="auto">
                        <div class="theme-option-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                        </div>
                        <div class="theme-option-content">
                            <div class="theme-option-title">自动切换</div>
                            <div class="theme-option-desc">根据时间自动切换主题</div>
                        </div>
                        <button class="theme-option-toggle ${this.autoMode ? 'active' : ''}" data-toggle="auto"></button>
                    </div>
                    
                    <button class="theme-option" data-action="schedule">
                        <div class="theme-option-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                                <line x1="16" y1="2" x2="16" y2="6"/>
                                <line x1="8" y1="2" x2="8" y2="6"/>
                                <line x1="3" y1="10" x2="21" y2="10"/>
                            </svg>
                        </div>
                        <div class="theme-option-content">
                            <div class="theme-option-title">设置时间</div>
                            <div class="theme-option-desc">自定义切换时间 (${this.lightTime} - ${this.darkTime})</div>
                        </div>
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(switcher);
        this.switcher = switcher;
        this.toggleButton = switcher.querySelector('.theme-toggle');
        this.dropdown = switcher.querySelector('.theme-dropdown');

        // 调试信息 - 检查最终位置
        setTimeout(() => {
            const styles = window.getComputedStyle(switcher);
            console.log(`🎨 主题切换器最终位置:`);
            console.log(`   - bottom: ${styles.bottom}`);
            console.log(`   - right: ${styles.right}`);
            console.log(`   - position: ${styles.position}`);
            console.log(`   - z-index: ${styles.zIndex}`);
        }, 100);
    }

    /**
     * 获取主题图标HTML
     */
    getThemeIcon() {
        return `
            <div class="theme-icon">
                <svg class="sun-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="12" cy="12" r="5"/>
                    <line x1="12" y1="1" x2="12" y2="3"/>
                    <line x1="12" y1="21" x2="12" y2="23"/>
                    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
                    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
                    <line x1="1" y1="12" x2="3" y2="12"/>
                    <line x1="21" y1="12" x2="23" y2="12"/>
                    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
                    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
                </svg>
                <svg class="moon-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                </svg>
            </div>
        `;
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 主按钮点击
        this.toggleButton.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleDropdown();
        });

        // 下拉菜单选项点击
        this.dropdown.addEventListener('click', (e) => {
            e.stopPropagation();
            
            // 检查是否点击了开关按钮
            if (e.target.classList.contains('theme-option-toggle') || e.target.closest('.theme-option-toggle')) {
                this.toggleAutoMode();
                return;
            }
            
            const option = e.target.closest('.theme-option');
            if (!option) return;

            const action = option.dataset.action;
            switch (action) {
                case 'toggle':
                    this.toggleTheme();
                    this.hideDropdown();
                    break;
                case 'schedule':
                    this.showTimeModal();
                    this.hideDropdown();
                    break;
            }
        });

        // 点击外部关闭下拉菜单
        document.addEventListener('click', () => {
            this.hideDropdown();
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.toggleTheme();
            }
        });
    }

    /**
     * 切换下拉菜单显示状态
     */
    toggleDropdown() {
        const isVisible = this.dropdown.classList.contains('show');
        if (isVisible) {
            this.hideDropdown();
        } else {
            this.showDropdown();
        }
    }

    /**
     * 显示下拉菜单
     */
    showDropdown() {
        this.dropdown.classList.add('show');
        // 更新切换按钮文本
        const toggleOption = this.dropdown.querySelector('[data-action="toggle"] .theme-option-title');
        if (toggleOption) {
            toggleOption.textContent = `切换到${this.currentTheme === 'light' ? '暗色' : '亮色'}主题`;
        }
    }

    /**
     * 隐藏下拉菜单
     */
    hideDropdown() {
        this.dropdown.classList.remove('show');
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        this.setSettingValue('theme', this.currentTheme);

        // 触觉反馈
        if (navigator.vibrate) {
            navigator.vibrate(30);
        }

        // 触发自定义事件
        this.dispatchEvent('themeChanged', {
            theme: this.currentTheme,
            timestamp: Date.now()
        });

        console.log(`🎨 主题已切换为: ${this.currentTheme}`);
    }

    /**
     * 切换自动模式
     */
    toggleAutoMode() {
        this.autoMode = !this.autoMode;
        this.setSettingValue('autoTheme', this.autoMode);

        const toggle = this.dropdown.querySelector('[data-toggle="auto"]');
        toggle.classList.toggle('active', this.autoMode);

        if (this.autoMode) {
            this.startAutoMode();
            this.showToast('已启用自动切换主题', 'success');
        } else {
            this.stopAutoMode();
            this.showToast('已关闭自动切换主题', 'info');
        }

        // 触发自定义事件
        this.dispatchEvent('autoModeChanged', {
            autoMode: this.autoMode,
            lightTime: this.lightTime,
            darkTime: this.darkTime
        });

        console.log(`🕐 自动主题切换: ${this.autoMode ? '已启用' : '已关闭'}`);
    }

    /**
     * 启动自动模式
     */
    startAutoMode() {
        this.stopAutoMode(); // 清除现有定时器

        const checkTime = () => {
            const now = new Date();
            const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

            const shouldBeDark = this.isTimeInRange(currentTime, this.darkTime, this.lightTime);
            const targetTheme = shouldBeDark ? 'dark' : 'light';

            if (this.currentTheme !== targetTheme) {
                this.currentTheme = targetTheme;
                document.documentElement.setAttribute('data-theme', this.currentTheme);
                this.setSettingValue('theme', this.currentTheme);

                // 触发自动切换事件
                this.dispatchEvent('autoThemeChanged', {
                    theme: this.currentTheme,
                    time: currentTime,
                    auto: true
                });

                console.log(`🕐 自动切换主题为: ${this.currentTheme} (${currentTime})`);
            }
        };

        // 立即检查一次
        checkTime();

        // 每分钟检查一次
        this.autoTimer = setInterval(checkTime, 60000);
    }

    /**
     * 停止自动模式
     */
    stopAutoMode() {
        if (this.autoTimer) {
            clearInterval(this.autoTimer);
            this.autoTimer = null;
        }
    }

    /**
     * 检查时间是否在指定范围内
     */
    isTimeInRange(current, start, end) {
        // 处理跨天的情况（如18:00到次日06:00）
        if (start > end) {
            return current >= start || current < end;
        } else {
            return current >= start && current < end;
        }
    }

    /**
     * 显示时间设置弹窗
     */
    showTimeModal() {
        this.createTimeModal();
    }

    /**
     * 创建时间设置弹窗
     */
    createTimeModal() {
        // 移除现有弹窗
        const existingModal = document.querySelector('.time-modal-overlay');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.className = 'time-modal-overlay';
        modal.innerHTML = `
            <div class="time-modal">
                <div class="time-modal-header">
                    <h3 class="time-modal-title">设置切换时间</h3>
                    <button class="time-modal-close" aria-label="关闭">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"/>
                            <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                    </button>
                </div>
                <div class="time-modal-body">
                    <div class="time-setting-group">
                        <label class="time-setting-label">亮色主题开始时间</label>
                        <div class="time-input-group">
                            <div class="time-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="5"/>
                                    <line x1="12" y1="1" x2="12" y2="3"/>
                                    <line x1="12" y1="21" x2="12" y2="23"/>
                                    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
                                    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
                                    <line x1="1" y1="12" x2="3" y2="12"/>
                                    <line x1="21" y1="12" x2="23" y2="12"/>
                                    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
                                    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
                                </svg>
                            </div>
                            <input type="number" class="time-input" id="lightHour" min="0" max="23" value="${this.lightTime.split(':')[0]}">
                            <span class="time-separator">:</span>
                            <input type="number" class="time-input" id="lightMinute" min="0" max="59" value="${this.lightTime.split(':')[1]}">
                        </div>
                    </div>

                    <div class="time-setting-group">
                        <label class="time-setting-label">暗色主题开始时间</label>
                        <div class="time-input-group">
                            <div class="time-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                                </svg>
                            </div>
                            <input type="number" class="time-input" id="darkHour" min="0" max="23" value="${this.darkTime.split(':')[0]}">
                            <span class="time-separator">:</span>
                            <input type="number" class="time-input" id="darkMinute" min="0" max="59" value="${this.darkTime.split(':')[1]}">
                        </div>
                    </div>
                </div>
                <div class="time-modal-footer">
                    <button class="btn btn-secondary" data-action="cancel">取消</button>
                    <button class="btn btn-primary" data-action="save">保存</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        modal.querySelector('.time-modal-close').addEventListener('click', () => {
            this.hideTimeModal();
        });

        modal.querySelector('[data-action="cancel"]').addEventListener('click', () => {
            this.hideTimeModal();
        });

        modal.querySelector('[data-action="save"]').addEventListener('click', () => {
            this.saveTimeSettings();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideTimeModal();
            }
        });

        // 显示弹窗
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }

    /**
     * 隐藏时间设置弹窗
     */
    hideTimeModal() {
        const modal = document.querySelector('.time-modal-overlay');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    /**
     * 保存时间设置
     */
    saveTimeSettings() {
        const lightHour = document.getElementById('lightHour').value.padStart(2, '0');
        const lightMinute = document.getElementById('lightMinute').value.padStart(2, '0');
        const darkHour = document.getElementById('darkHour').value.padStart(2, '0');
        const darkMinute = document.getElementById('darkMinute').value.padStart(2, '0');

        this.lightTime = `${lightHour}:${lightMinute}`;
        this.darkTime = `${darkHour}:${darkMinute}`;

        this.setSettingValue('lightTime', this.lightTime);
        this.setSettingValue('darkTime', this.darkTime);

        // 更新下拉菜单中的时间显示
        const scheduleDesc = this.dropdown.querySelector('[data-action="schedule"] .theme-option-desc');
        if (scheduleDesc) {
            scheduleDesc.textContent = `自定义切换时间 (${this.lightTime} - ${this.darkTime})`;
        }

        // 立即检查并应用当前时间对应的主题
        this.checkAndApplyCurrentTheme();

        // 如果自动模式已启用，重新启动
        if (this.autoMode) {
            this.startAutoMode();
        }

        this.hideTimeModal();
        this.showToast('时间设置已保存', 'success');

        // 触发自定义事件
        this.dispatchEvent('timeSettingsChanged', {
            lightTime: this.lightTime,
            darkTime: this.darkTime
        });
    }

    /**
     * 检查并应用当前时间对应的主题
     */
    checkAndApplyCurrentTheme() {
        const now = new Date();
        const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

        const shouldBeDark = this.isTimeInRange(currentTime, this.darkTime, this.lightTime);
        const targetTheme = shouldBeDark ? 'dark' : 'light';

        if (this.currentTheme !== targetTheme) {
            const oldTheme = this.currentTheme;
            this.currentTheme = targetTheme;
            document.documentElement.setAttribute('data-theme', this.currentTheme);
            this.setSettingValue('theme', this.currentTheme);

            // 显示主题切换提示
            this.showToast(`根据时间设置自动切换到${targetTheme === 'dark' ? '暗色' : '亮色'}主题`, 'info');

            // 触发主题切换事件
            this.dispatchEvent('themeChanged', {
                theme: this.currentTheme,
                previousTheme: oldTheme,
                time: currentTime,
                auto: true,
                reason: 'timeSettingsChanged'
            });

            console.log(`🕐 根据时间设置切换主题: ${oldTheme} → ${this.currentTheme} (${currentTime})`);
        } else {
            console.log(`🕐 当前时间 ${currentTime} 对应主题 ${targetTheme}，无需切换`);
        }
    }

    /**
     * 设置自动模式（供外部调用）
     */
    setAutoMode(enabled, lightTime, darkTime) {
        this.autoMode = enabled;
        if (lightTime) this.lightTime = lightTime;
        if (darkTime) this.darkTime = darkTime;

        // 保存设置
        this.setSettingValue('autoTheme', this.autoMode);
        if (lightTime) this.setSettingValue('lightTime', this.lightTime);
        if (darkTime) this.setSettingValue('darkTime', this.darkTime);

        // 更新UI状态
        const toggle = this.dropdown?.querySelector('[data-toggle="auto"]');
        if (toggle) {
            toggle.classList.toggle('active', this.autoMode);
        }

        // 更新时间显示
        const scheduleDesc = this.dropdown?.querySelector('[data-action="schedule"] .theme-option-desc');
        if (scheduleDesc) {
            scheduleDesc.textContent = `自定义切换时间 (${this.lightTime} - ${this.darkTime})`;
        }

        if (this.autoMode) {
            // 立即检查并应用当前时间对应的主题
            this.checkAndApplyCurrentTheme();
            // 启动自动模式
            this.startAutoMode();
        } else {
            this.stopAutoMode();
        }

        console.log(`🕐 自动主题模式已${enabled ? '启用' : '禁用'}: ${this.lightTime} - ${this.darkTime}`);
    }

    /**
     * 显示Toast提示
     */
    showToast(message, type = 'info', duration = 3000) {
        if (!this.options.showToast) return;

        // 如果存在全局Toast管理器，使用它
        if (window.toastManager && typeof window.toastManager.show === 'function') {
            window.toastManager.show(message, type, duration);
            return;
        }

        // 否则创建简单的Toast
        const toast = document.createElement('div');
        toast.className = `theme-toast theme-toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
        `;

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateY(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    /**
     * 触发自定义事件
     */
    dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(`themeSwitcher:${eventName}`, {
            detail: {
                instance: this,
                ...detail
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 停止自动模式
        this.stopAutoMode();

        // 移除DOM元素
        if (this.switcher && this.switcher.parentNode) {
            this.switcher.parentNode.removeChild(this.switcher);
        }

        // 移除时间弹窗
        const modal = document.querySelector('.time-modal-overlay');
        if (modal && modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }

        // 清空引用
        this.switcher = null;
        this.toggleButton = null;
        this.dropdown = null;
    }

    /**
     * 获取当前状态
     */
    getState() {
        return {
            theme: this.currentTheme,
            autoMode: this.autoMode,
            lightTime: this.lightTime,
            darkTime: this.darkTime,
            isAutoRunning: this.autoTimer !== null
        };
    }

    /**
     * 设置主题（编程方式）
     */
    setTheme(theme) {
        if (theme !== 'light' && theme !== 'dark') {
            console.warn('Invalid theme. Use "light" or "dark".');
            return;
        }

        this.currentTheme = theme;
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        localStorage.setItem('theme', this.currentTheme);

        // 触发事件
        this.dispatchEvent('themeChanged', {
            theme: this.currentTheme,
            programmatic: true
        });
    }


}

// 导出组件类
window.ThemeSwitcher = ThemeSwitcher;
