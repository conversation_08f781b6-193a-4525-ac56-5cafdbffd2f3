/**
 * 🎯 会员管理页面脚本
 * 
 * 功能：
 * - 用户升级
 * - 会员续费
 * - 历史记录查看
 * - 分组管理
 */

class MembershipManager {
    constructor() {
        this.groups = window.membershipData?.groups || [];
        this.currentUser = window.membershipData?.currentUser || {};
        
        this.init();
    }
    
    /**
     * 初始化
     */
    init() {
        this.bindEvents();
        console.log('🎯 会员管理已初始化');
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 表单提交事件
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.filters-form')) {
                // 可以在这里添加表单验证
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }
    
    /**
     * 升级用户
     */
    upgradeUser(userId, username) {
        const modal = this.createModal('升级用户', `
            <form id="upgradeForm" class="modal-form">
                <input type="hidden" name="user_id" value="${userId}">
                
                <div class="form-group">
                    <label>用户</label>
                    <div class="user-display">
                        <div class="user-avatar">${username.charAt(0).toUpperCase()}</div>
                        <span>${username}</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="target_group">目标分组</label>
                    <select id="target_group" name="target_group" required>
                        <option value="">请选择分组</option>
                        ${this.groups.map(group => 
                            `<option value="${group.group_key}">${group.group_name} (${group.group_type})</option>`
                        ).join('')}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="duration">有效期（天）</label>
                    <select id="duration" name="duration">
                        <option value="">永久</option>
                        <option value="30">30天</option>
                        <option value="90">90天</option>
                        <option value="180">180天</option>
                        <option value="365">365天</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="reason">升级原因</label>
                    <textarea id="reason" name="reason" placeholder="请输入升级原因..." rows="3"></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-outline" onclick="membershipManager.closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">确认升级</button>
                </div>
            </form>
        `);
        
        // 绑定表单提交
        const form = modal.querySelector('#upgradeForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitUpgrade(form);
        });
    }
    
    /**
     * 续费用户
     */
    renewUser(userId, username) {
        const modal = this.createModal('续费用户', `
            <form id="renewForm" class="modal-form">
                <input type="hidden" name="user_id" value="${userId}">
                
                <div class="form-group">
                    <label>用户</label>
                    <div class="user-display">
                        <div class="user-avatar">${username.charAt(0).toUpperCase()}</div>
                        <span>${username}</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="renew_duration">续费时长（天）</label>
                    <select id="renew_duration" name="duration" required>
                        <option value="">请选择时长</option>
                        <option value="30">30天</option>
                        <option value="90">90天</option>
                        <option value="180">180天</option>
                        <option value="365">365天</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="renew_reason">续费原因</label>
                    <textarea id="renew_reason" name="reason" placeholder="请输入续费原因..." rows="3"></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-outline" onclick="membershipManager.closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">确认续费</button>
                </div>
            </form>
        `);
        
        // 绑定表单提交
        const form = modal.querySelector('#renewForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitRenewal(form);
        });
    }
    
    /**
     * 查看用户历史
     */
    viewHistory(userId, username) {
        // 显示加载状态
        const modal = this.createModal('会员历史', `
            <div class="loading-state">
                <div class="spinner"></div>
                <p>正在加载历史记录...</p>
            </div>
        `);
        
        // 获取历史记录
        this.fetchUserHistory(userId).then(history => {
            modal.querySelector('.modal-body').innerHTML = `
                <div class="history-header">
                    <div class="user-display">
                        <div class="user-avatar">${username.charAt(0).toUpperCase()}</div>
                        <span>${username} 的会员历史</span>
                    </div>
                </div>
                
                <div class="history-list">
                    ${history.length > 0 ? history.map(record => `
                        <div class="history-item">
                            <div class="history-icon ${record.action_type}">
                                ${this.getActionIcon(record.action_type)}
                            </div>
                            <div class="history-content">
                                <div class="history-action">${this.getActionText(record.action_type)}</div>
                                <div class="history-details">
                                    ${record.from_group_name ? `从 ${record.from_group_name}` : ''}
                                    ${record.to_group_name ? ` 到 ${record.to_group_name}` : ''}
                                    ${record.reason ? ` - ${record.reason}` : ''}
                                </div>
                                <div class="history-meta">
                                    <span class="history-time">${this.formatDate(record.created_at)}</span>
                                    ${record.operator_name ? `<span class="history-operator">操作者: ${record.operator_name}</span>` : ''}
                                </div>
                            </div>
                        </div>
                    `).join('') : '<div class="empty-history">暂无历史记录</div>'}
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-outline" onclick="membershipManager.closeModal()">关闭</button>
                </div>
            `;
        }).catch(error => {
            modal.querySelector('.modal-body').innerHTML = `
                <div class="error-state">
                    <p>加载历史记录失败: ${error.message}</p>
                    <button type="button" class="btn btn-outline" onclick="membershipManager.closeModal()">关闭</button>
                </div>
            `;
        });
    }
    
    /**
     * 提交升级
     */
    async submitUpgrade(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        try {
            submitBtn.disabled = true;
            submitBtn.textContent = '处理中...';
            
            const response = await fetch('../api/membership.api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'upgrade',
                    user_id: formData.get('user_id'),
                    target_group: formData.get('target_group'),
                    duration: formData.get('duration'),
                    reason: formData.get('reason')
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showToast('升级成功', 'success');
                this.closeModal();
                setTimeout(() => location.reload(), 1000);
            } else {
                throw new Error(result.message || '升级失败');
            }
            
        } catch (error) {
            this.showToast(error.message, 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = '确认升级';
        }
    }
    
    /**
     * 提交续费
     */
    async submitRenewal(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        try {
            submitBtn.disabled = true;
            submitBtn.textContent = '处理中...';
            
            const response = await fetch('../api/membership.api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'renew',
                    user_id: formData.get('user_id'),
                    duration: formData.get('duration'),
                    reason: formData.get('reason')
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showToast('续费成功', 'success');
                this.closeModal();
                setTimeout(() => location.reload(), 1000);
            } else {
                throw new Error(result.message || '续费失败');
            }
            
        } catch (error) {
            this.showToast(error.message, 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = '确认续费';
        }
    }
    
    /**
     * 获取用户历史记录
     */
    async fetchUserHistory(userId) {
        const response = await fetch(`../api/membership.api.php?action=history&user_id=${userId}`);
        const result = await response.json();
        
        if (result.success) {
            return result.history;
        } else {
            throw new Error(result.message || '获取历史记录失败');
        }
    }
    
    /**
     * 创建模态框
     */
    createModal(title, content) {
        const modalHtml = `
            <div class="modal-overlay" onclick="membershipManager.closeModal()">
                <div class="modal-dialog" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3 class="modal-title">${title}</h3>
                        <button class="modal-close" onclick="membershipManager.closeModal()">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        `;
        
        const container = document.getElementById('modalContainer');
        container.innerHTML = modalHtml;
        
        return container.querySelector('.modal-dialog');
    }
    
    /**
     * 关闭模态框
     */
    closeModal() {
        const container = document.getElementById('modalContainer');
        container.innerHTML = '';
    }
    
    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
        // 使用全局的toast系统
        if (window.appManager && window.appManager.showToast) {
            window.appManager.showToast(message, type);
        } else {
            alert(message);
        }
    }
    
    /**
     * 获取操作图标
     */
    getActionIcon(actionType) {
        const icons = {
            upgrade: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="12" y1="19" x2="12" y2="5"/><polyline points="5,12 12,5 19,12"/></svg>',
            downgrade: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="12" y1="5" x2="12" y2="19"/><polyline points="19,12 12,19 5,12"/></svg>',
            renew: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="23,4 23,10 17,10"/><path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"/></svg>',
            suspend: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"/><line x1="15" y1="9" x2="9" y2="15"/><line x1="9" y1="9" x2="15" y2="15"/></svg>',
            activate: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="20,6 9,17 4,12"/></svg>',
            expire: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"/><line x1="12" y1="8" x2="12" y2="12"/><line x1="12" y1="16" x2="12.01" y2="16"/></svg>'
        };
        return icons[actionType] || icons.upgrade;
    }
    
    /**
     * 获取操作文本
     */
    getActionText(actionType) {
        const texts = {
            upgrade: '升级',
            downgrade: '降级',
            renew: '续费',
            suspend: '暂停',
            activate: '激活',
            expire: '过期'
        };
        return texts[actionType] || actionType;
    }
    
    /**
     * 格式化日期
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }
}

// 全局函数
function upgradeUser(userId, username) {
    window.membershipManager.upgradeUser(userId, username);
}

function renewUser(userId, username) {
    window.membershipManager.renewUser(userId, username);
}

function viewHistory(userId, username) {
    window.membershipManager.viewHistory(userId, username);
}

function showGroupModal() {
    // 分组管理功能
    window.membershipManager.showToast('分组管理功能开发中...', 'info');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.membershipManager = new MembershipManager();
    console.log('🎯 会员管理页面已完全初始化');
});
