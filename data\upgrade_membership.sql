-- =============================================
-- 会员管理系统升级脚本
-- 版本：v1.0
-- 说明：为现有用户管理系统添加会员管理功能
-- =============================================

USE `modern_php_admin`;

-- =============================================
-- 1. 用户分组表 (user_groups)
-- =============================================
CREATE TABLE IF NOT EXISTS `user_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_key` varchar(50) NOT NULL COMMENT '分组标识',
  `group_name` varchar(100) NOT NULL COMMENT '分组名称',
  `group_type` enum('free','premium','vip') NOT NULL DEFAULT 'free' COMMENT '分组类型',
  `description` text COMMENT '分组描述',
  `permissions` json DEFAULT NULL COMMENT '权限配置',
  `features` json DEFAULT NULL COMMENT '功能特性',
  `max_storage` bigint(20) DEFAULT 0 COMMENT '最大存储空间(字节)',
  `max_files` int(11) DEFAULT 0 COMMENT '最大文件数量',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级(数字越大优先级越高)',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认分组',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_key` (`group_key`),
  KEY `idx_group_type` (`group_type`),
  KEY `idx_priority` (`priority`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户分组表';

-- =============================================
-- 2. 用户会员信息表 (user_memberships)
-- =============================================
CREATE TABLE IF NOT EXISTS `user_memberships` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `group_id` int(11) NOT NULL COMMENT '分组ID',
  `membership_type` enum('free','trial','paid','gift') NOT NULL DEFAULT 'free' COMMENT '会员类型',
  `start_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_date` datetime DEFAULT NULL COMMENT '结束时间(NULL表示永久)',
  `auto_renew` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动续费',
  `status` enum('active','expired','suspended','cancelled') NOT NULL DEFAULT 'active' COMMENT '状态',
  `upgrade_from` int(11) DEFAULT NULL COMMENT '从哪个分组升级',
  `upgrade_reason` varchar(255) DEFAULT NULL COMMENT '升级原因',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_membership_type` (`membership_type`),
  KEY `idx_status` (`status`),
  KEY `idx_end_date` (`end_date`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`group_id`) REFERENCES `user_groups` (`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`upgrade_from`) REFERENCES `user_groups` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会员信息表';

-- =============================================
-- 3. 会员操作日志表 (membership_logs)
-- =============================================
CREATE TABLE IF NOT EXISTS `membership_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `action_type` enum('upgrade','downgrade','renew','suspend','activate','expire') NOT NULL COMMENT '操作类型',
  `from_group_id` int(11) DEFAULT NULL COMMENT '原分组ID',
  `to_group_id` int(11) DEFAULT NULL COMMENT '目标分组ID',
  `from_end_date` datetime DEFAULT NULL COMMENT '原结束时间',
  `to_end_date` datetime DEFAULT NULL COMMENT '新结束时间',
  `reason` varchar(255) DEFAULT NULL COMMENT '操作原因',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作者ID',
  `operator_type` enum('system','admin','user') NOT NULL DEFAULT 'system' COMMENT '操作者类型',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_from_group_id` (`from_group_id`),
  KEY `idx_to_group_id` (`to_group_id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`from_group_id`) REFERENCES `user_groups` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`to_group_id`) REFERENCES `user_groups` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`operator_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员操作日志表';

-- =============================================
-- 4. 插入默认用户分组
-- =============================================

-- 普通用户分组
INSERT IGNORE INTO `user_groups` (
  `group_key`, `group_name`, `group_type`, `description`, 
  `permissions`, `features`, `max_storage`, `max_files`, 
  `priority`, `is_default`, `is_active`
) VALUES (
  'free_user', '普通用户', 'free', '系统默认的普通用户分组',
  JSON_OBJECT(
    'dashboard', true,
    'profile', true,
    'basic_features', true,
    'advanced_features', false,
    'admin_features', false
  ),
  JSON_OBJECT(
    'storage_limit', '100MB',
    'file_upload', true,
    'export_data', false,
    'priority_support', false,
    'custom_themes', false
  ),
  104857600, 100, 10, 1, 1
);

-- 会员用户分组
INSERT IGNORE INTO `user_groups` (
  `group_key`, `group_name`, `group_type`, `description`, 
  `permissions`, `features`, `max_storage`, `max_files`, 
  `priority`, `is_default`, `is_active`
) VALUES (
  'premium_user', '会员用户', 'premium', '付费会员用户，享受更多功能',
  JSON_OBJECT(
    'dashboard', true,
    'profile', true,
    'basic_features', true,
    'advanced_features', true,
    'admin_features', false
  ),
  JSON_OBJECT(
    'storage_limit', '1GB',
    'file_upload', true,
    'export_data', true,
    'priority_support', true,
    'custom_themes', true
  ),
  1073741824, 1000, 20, 0, 1
);

-- VIP用户分组
INSERT IGNORE INTO `user_groups` (
  `group_key`, `group_name`, `group_type`, `description`, 
  `permissions`, `features`, `max_storage`, `max_files`, 
  `priority`, `is_default`, `is_active`
) VALUES (
  'vip_user', 'VIP用户', 'vip', '高级VIP用户，享受所有功能',
  JSON_OBJECT(
    'dashboard', true,
    'profile', true,
    'basic_features', true,
    'advanced_features', true,
    'admin_features', false
  ),
  JSON_OBJECT(
    'storage_limit', '10GB',
    'file_upload', true,
    'export_data', true,
    'priority_support', true,
    'custom_themes', true,
    'api_access', true
  ),
  10737418240, 10000, 30, 0, 1
);

-- 管理员分组
INSERT IGNORE INTO `user_groups` (
  `group_key`, `group_name`, `group_type`, `description`, 
  `permissions`, `features`, `max_storage`, `max_files`, 
  `priority`, `is_default`, `is_active`
) VALUES (
  'admin', '管理员', 'premium', '系统管理员，拥有所有权限',
  JSON_OBJECT(
    'dashboard', true,
    'profile', true,
    'basic_features', true,
    'advanced_features', true,
    'admin_features', true,
    'user_management', true,
    'system_settings', true
  ),
  JSON_OBJECT(
    'storage_limit', 'unlimited',
    'file_upload', true,
    'export_data', true,
    'priority_support', true,
    'custom_themes', true,
    'api_access', true,
    'admin_panel', true
  ),
  -1, -1, 100, 0, 1
);

-- =============================================
-- 5. 为现有用户创建会员记录
-- =============================================

-- 为所有现有用户创建会员记录
INSERT IGNORE INTO `user_memberships` (
  `user_id`, `group_id`, `membership_type`, `status`
)
SELECT 
  u.id,
  CASE 
    WHEN u.role = 'admin' THEN (SELECT id FROM user_groups WHERE group_key = 'admin')
    WHEN u.user_group = 'premium' THEN (SELECT id FROM user_groups WHERE group_key = 'premium_user')
    WHEN u.user_group = 'vip' THEN (SELECT id FROM user_groups WHERE group_key = 'vip_user')
    ELSE (SELECT id FROM user_groups WHERE group_key = 'free_user')
  END,
  CASE 
    WHEN u.role = 'admin' THEN 'paid'
    WHEN u.user_group IN ('premium', 'vip') THEN 'paid'
    ELSE 'free'
  END,
  'active'
FROM users u
WHERE NOT EXISTS (
  SELECT 1 FROM user_memberships um WHERE um.user_id = u.id
);

-- =============================================
-- 6. 更新用户表的user_group字段
-- =============================================

-- 根据会员信息更新用户分组
UPDATE users u 
JOIN user_memberships um ON u.id = um.user_id 
JOIN user_groups ug ON um.group_id = ug.id 
SET u.user_group = ug.group_key 
WHERE um.status = 'active';

-- =============================================
-- 7. 创建视图和索引优化
-- =============================================

-- 创建用户会员信息视图
CREATE OR REPLACE VIEW `user_membership_view` AS
SELECT 
  u.id as user_id,
  u.username,
  u.email,
  u.nickname,
  u.role,
  u.status as user_status,
  ug.id as group_id,
  ug.group_key,
  ug.group_name,
  ug.group_type,
  ug.priority,
  um.membership_type,
  um.start_date,
  um.end_date,
  um.auto_renew,
  um.status as membership_status,
  CASE 
    WHEN um.end_date IS NULL THEN 'permanent'
    WHEN um.end_date > NOW() THEN 'active'
    ELSE 'expired'
  END as membership_validity,
  DATEDIFF(um.end_date, NOW()) as days_remaining,
  u.created_at as user_created_at,
  um.created_at as membership_created_at
FROM users u
LEFT JOIN user_memberships um ON u.id = um.user_id AND um.status = 'active'
LEFT JOIN user_groups ug ON um.group_id = ug.id;

-- =============================================
-- 升级完成
-- =============================================

-- 记录升级日志
INSERT INTO operation_logs (
  operation_type, operation_desc, operation_data, 
  ip_address, created_at
) VALUES (
  'system_upgrade', '会员管理系统升级完成', 
  JSON_OBJECT(
    'version', 'v1.0',
    'tables_created', JSON_ARRAY('user_groups', 'user_memberships', 'membership_logs'),
    'views_created', JSON_ARRAY('user_membership_view'),
    'default_groups', 4
  ),
  '127.0.0.1', NOW()
);

-- 显示升级结果
SELECT 
  '会员管理系统升级完成' as message,
  (SELECT COUNT(*) FROM user_groups) as total_groups,
  (SELECT COUNT(*) FROM user_memberships) as total_memberships,
  (SELECT COUNT(*) FROM users WHERE id IN (SELECT user_id FROM user_memberships)) as users_with_membership;
