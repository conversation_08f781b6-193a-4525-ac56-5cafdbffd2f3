<?php
/**
 * 🎯 功能中心页面
 * 
 * 功能：展示系统所有功能模块
 * 特色：支持列表/卡片视图切换，响应式设计
 */

session_start();

// 引入认证中间件
require_once '../auth/auth.php';
require_once '../auth/SystemConfig.php';

// 检查用户是否已登录
Auth::requireLogin();

// 获取当前用户信息
$current_user = Auth::getCurrentUser();
$page_title = '功能中心';

// 功能模块数据
$features = [
    [
        'id' => 'dashboard',
        'name' => '仪表盘',
        'description' => '系统概览和数据统计',
        'icon' => 'dashboard',
        'url' => 'index.php',
        'category' => 'core',
        'status' => 'active',
        'color' => '#3b82f6'
    ],
    [
        'id' => 'users',
        'name' => '用户管理',
        'description' => '管理系统用户和权限',
        'icon' => 'users',
        'url' => 'users.php',
        'category' => 'management',
        'status' => 'active',
        'color' => '#10b981'
    ],
    [
        'id' => 'profile',
        'name' => '个人资料',
        'description' => '查看和编辑个人信息',
        'icon' => 'user',
        'url' => 'profile.php',
        'category' => 'personal',
        'status' => 'active',
        'color' => '#8b5cf6'
    ],
    [
        'id' => 'settings',
        'name' => '系统设置',
        'description' => '配置系统参数和选项',
        'icon' => 'settings',
        'url' => 'settings.php',
        'category' => 'system',
        'status' => 'active',
        'color' => '#f59e0b'
    ],
    [
        'id' => 'security',
        'name' => '安全中心',
        'description' => '安全设置和日志查看',
        'icon' => 'shield',
        'url' => '#',
        'category' => 'security',
        'status' => 'coming_soon',
        'color' => '#ef4444'
    ],
    [
        'id' => 'reports',
        'name' => '报表中心',
        'description' => '数据报表和分析',
        'icon' => 'chart',
        'url' => '#',
        'category' => 'analytics',
        'status' => 'coming_soon',
        'color' => '#06b6d4'
    ],
    [
        'id' => 'notifications',
        'name' => '消息通知',
        'description' => '系统消息和通知管理',
        'icon' => 'bell',
        'url' => '#',
        'category' => 'communication',
        'status' => 'development',
        'color' => '#84cc16'
    ],
    [
        'id' => 'files',
        'name' => '文件管理',
        'description' => '文件上传和管理',
        'icon' => 'folder',
        'url' => '#',
        'category' => 'tools',
        'status' => 'development',
        'color' => '#f97316'
    ],
    [
        'id' => 'backup',
        'name' => '数据备份',
        'description' => '数据备份和恢复',
        'icon' => 'database',
        'url' => '#',
        'category' => 'system',
        'status' => 'planning',
        'color' => '#6366f1'
    ],
    [
        'id' => 'logs',
        'name' => '系统日志',
        'description' => '查看系统操作日志',
        'icon' => 'list',
        'url' => '#',
        'category' => 'system',
        'status' => 'planning',
        'color' => '#64748b'
    ]
];

// 功能分类
$categories = [
    'all' => '全部',
    'core' => '核心功能',
    'management' => '管理功能',
    'personal' => '个人功能',
    'system' => '系统功能',
    'security' => '安全功能',
    'analytics' => '分析功能',
    'communication' => '通信功能',
    'tools' => '工具功能'
];

// 状态标签
$status_labels = [
    'active' => '可用',
    'coming_soon' => '即将推出',
    'development' => '开发中',
    'planning' => '规划中'
];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle($page_title); ?></title>
    
    <!-- 引入功能中心样式 -->
    <link rel="stylesheet" href="assets/css/features.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- 头部 -->
        <?php include 'includes/header.php'; ?>
        
        <!-- 主要内容 -->
        <main class="main-content">
            <div class="features-container">
                <!-- 页面标题和控制栏 -->
                <div class="features-header">
                    <div class="header-left">
                        <h1 class="page-title">
                            <svg class="title-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="7" height="7"/>
                                <rect x="14" y="3" width="7" height="7"/>
                                <rect x="14" y="14" width="7" height="7"/>
                                <rect x="3" y="14" width="7" height="7"/>
                            </svg>
                            功能中心
                        </h1>
                        <p class="page-subtitle">探索和使用系统的各项功能</p>
                    </div>
                    
                    <div class="header-controls">
                        <!-- 搜索框 -->
                        <div class="search-box">
                            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="m21 21-4.35-4.35"/>
                            </svg>
                            <input type="text" id="searchInput" placeholder="搜索功能..." autocomplete="off">
                        </div>
                        
                        <!-- 视图切换 -->
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="grid" title="卡片视图">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="3" width="7" height="7"/>
                                    <rect x="14" y="3" width="7" height="7"/>
                                    <rect x="14" y="14" width="7" height="7"/>
                                    <rect x="3" y="14" width="7" height="7"/>
                                </svg>
                            </button>
                            <button class="view-btn" data-view="list" title="列表视图">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="8" y1="6" x2="21" y2="6"/>
                                    <line x1="8" y1="12" x2="21" y2="12"/>
                                    <line x1="8" y1="18" x2="21" y2="18"/>
                                    <line x1="3" y1="6" x2="3.01" y2="6"/>
                                    <line x1="3" y1="12" x2="3.01" y2="12"/>
                                    <line x1="3" y1="18" x2="3.01" y2="18"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 分类过滤器 -->
                <div class="category-filters">
                    <?php foreach ($categories as $key => $label): ?>
                    <button class="category-btn <?php echo $key === 'all' ? 'active' : ''; ?>" 
                            data-category="<?php echo $key; ?>">
                        <?php echo $label; ?>
                    </button>
                    <?php endforeach; ?>
                </div>
                
                <!-- 功能列表/卡片容器 -->
                <div class="features-content">
                    <div id="featuresGrid" class="features-grid view-grid">
                        <?php foreach ($features as $feature): ?>
                        <div class="feature-item" 
                             data-category="<?php echo $feature['category']; ?>"
                             data-status="<?php echo $feature['status']; ?>"
                             data-name="<?php echo strtolower($feature['name']); ?>"
                             data-description="<?php echo strtolower($feature['description']); ?>">
                            
                            <!-- 卡片视图内容 -->
                            <div class="feature-card">
                                <div class="feature-icon" style="background-color: <?php echo $feature['color']; ?>20;">
                                    <svg class="icon-<?php echo $feature['icon']; ?>" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="<?php echo $feature['color']; ?>" stroke-width="2">
                                        <!-- SVG图标将通过CSS/JS动态加载 -->
                                    </svg>
                                </div>
                                
                                <div class="feature-content">
                                    <h3 class="feature-name"><?php echo $feature['name']; ?></h3>
                                    <p class="feature-description"><?php echo $feature['description']; ?></p>
                                    
                                    <div class="feature-meta">
                                        <span class="feature-status status-<?php echo $feature['status']; ?>">
                                            <?php echo $status_labels[$feature['status']]; ?>
                                        </span>
                                        <span class="feature-category"><?php echo $categories[$feature['category']]; ?></span>
                                    </div>
                                </div>
                                
                                <div class="feature-actions">
                                    <?php if ($feature['status'] === 'active'): ?>
                                    <a href="<?php echo $feature['url']; ?>" class="action-btn primary">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                        进入
                                    </a>
                                    <?php else: ?>
                                    <button class="action-btn disabled" disabled>
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="12" cy="12" r="10"/>
                                            <line x1="15" y1="9" x2="9" y2="15"/>
                                            <line x1="9" y1="9" x2="15" y2="15"/>
                                        </svg>
                                        暂不可用
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <!-- 列表视图内容 -->
                            <div class="feature-list-item">
                                <div class="list-icon" style="background-color: <?php echo $feature['color']; ?>20;">
                                    <svg class="icon-<?php echo $feature['icon']; ?>" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="<?php echo $feature['color']; ?>" stroke-width="2">
                                        <!-- SVG图标将通过CSS/JS动态加载 -->
                                    </svg>
                                </div>
                                
                                <div class="list-content">
                                    <div class="list-main">
                                        <h4 class="list-name"><?php echo $feature['name']; ?></h4>
                                        <p class="list-description"><?php echo $feature['description']; ?></p>
                                    </div>
                                    
                                    <div class="list-meta">
                                        <span class="list-category"><?php echo $categories[$feature['category']]; ?></span>
                                        <span class="list-status status-<?php echo $feature['status']; ?>">
                                            <?php echo $status_labels[$feature['status']]; ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="list-actions">
                                    <?php if ($feature['status'] === 'active'): ?>
                                    <a href="<?php echo $feature['url']; ?>" class="list-action-btn primary">
                                        进入
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </a>
                                    <?php else: ?>
                                    <button class="list-action-btn disabled" disabled>
                                        暂不可用
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- 空状态 -->
                    <div id="emptyState" class="empty-state" style="display: none;">
                        <svg class="empty-icon" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="m21 21-4.35-4.35"/>
                        </svg>
                        <h3>未找到相关功能</h3>
                        <p>尝试调整搜索条件或选择其他分类</p>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- 页脚 -->
        <?php include 'includes/footer.php'; ?>
    </div>
    
    <!-- 引入功能中心脚本 -->
    <script src="assets/js/features.js"></script>
</body>
</html>
