/**
 * 🎭 现代化PHP管理系统 - 全局模态弹窗组件
 *
 * 功能特性：
 * - 多种弹窗类型（信息/确认/警告/错误/自定义内容）
 * - 持久化存储用户偏好设置
 * - 自定义样式和主题适配
 * - 响应式设计和移动端优化
 * - ESC键和背景点击关闭
 * - Promise-based API设计
 * - 完整的CSS样式组件
 * - 动画效果和过渡
 *
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复声明
if (typeof ModalComponent === 'undefined') {
class ModalComponent {
    constructor(options = {}) {
        this.options = {
            // 基础配置
            autoInit: true,
            storageKey: 'modal_preferences',
            
            // 默认设置
            defaults: {
                animation: 'fade',          // fade, slide, zoom, bounce
                position: 'center',         // center, top, bottom
                backdrop: 'blur',           // blur, dark, light, transparent
                closeOnEscape: true,
                closeOnBackdrop: true,
                showCloseButton: true,
                autoFocus: true,
                
                // 样式设置
                borderRadius: 'medium',     // small, medium, large, rounded
                shadow: 'large',            // small, medium, large, xl
                maxWidth: 'medium',         // small, medium, large, xl, full
                
                // 动画时长
                animationDuration: 300
            },
            
            ...options
        };
        
        this.modals = new Map();
        this.activeModal = null;
        this.preferences = {};
        this.zIndexCounter = 2000;
        
        if (this.options.autoInit) {
            this.init();
        }
    }
    
    /**
     * 初始化组件
     */
    init() {
        this.loadPreferences();
        this.createStylesheet();
        this.bindGlobalEvents();

        // 延迟加载设置面板的配置
        setTimeout(() => {
            this.syncWithSettingsPanel();
        }, 100);

        console.log('🎭 模态弹窗组件已初始化');
    }
    
    /**
     * 加载用户偏好设置
     */
    loadPreferences() {
        try {
            // 优先从统一存储管理器加载
            if (window.storageManager) {
                const stored = window.storageManager.get(this.options.storageKey, null);
                if (stored) {
                    this.preferences = { ...this.options.defaults, ...stored };
                    console.log('🎭 从统一存储管理器加载模态弹窗偏好设置:', this.preferences);
                    return;
                }
            }

            // 回退到本地存储
            const stored = localStorage.getItem(this.options.storageKey);
            this.preferences = stored ? { ...this.options.defaults, ...JSON.parse(stored) } : { ...this.options.defaults };
            console.log('🎭 从本地存储加载模态弹窗偏好设置:', this.preferences);
        } catch (error) {
            console.warn('加载模态弹窗偏好设置失败:', error);
            this.preferences = { ...this.options.defaults };
        }
    }
    
    /**
     * 保存用户偏好设置
     */
    savePreferences() {
        try {
            // 优先保存到统一存储管理器
            if (window.storageManager) {
                window.storageManager.set(this.options.storageKey, this.preferences);
                console.log('🎭 模态弹窗偏好设置已保存到统一存储管理器:', this.preferences);
            } else {
                // 回退到本地存储
                localStorage.setItem(this.options.storageKey, JSON.stringify(this.preferences));
                console.log('🎭 模态弹窗偏好设置已保存到本地存储:', this.preferences);
            }
        } catch (error) {
            console.warn('保存模态弹窗偏好设置失败:', error);
        }
    }
    
    /**
     * 更新偏好设置
     */
    updatePreferences(newPreferences) {
        this.preferences = { ...this.preferences, ...newPreferences };
        this.savePreferences();
        console.log('🎨 模态弹窗偏好设置已更新:', newPreferences);
    }

    /**
     * 与设置面板同步配置
     */
    syncWithSettingsPanel() {
        if (window.storageManager) {
            try {
                const modalPrefs = window.storageManager.get(this.options.storageKey, {});
                if (modalPrefs && Object.keys(modalPrefs).length > 0) {
                    // 合并设置面板的配置
                    const updatedPrefs = { ...this.preferences, ...modalPrefs };
                    this.preferences = updatedPrefs;
                    console.log('🔄 已同步设置面板的模态弹窗配置:', modalPrefs);
                }
            } catch (error) {
                console.warn('同步设置面板配置失败:', error);
            }
        }
    }
    
    /**
     * 创建样式表
     */
    createStylesheet() {
        if (document.getElementById('modal-component-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'modal-component-styles';
        style.textContent = this.getStylesheet();
        document.head.appendChild(style);
    }
    
    /**
     * 获取样式表内容
     */
    getStylesheet() {
        return `
            /* 模态弹窗基础样式 */
            .modal-overlay {
                position: fixed;
                inset: 0;
                z-index: 2000;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                visibility: hidden;
                transition: all var(--modal-duration, 300ms) ease;
            }
            
            .modal-overlay.show {
                opacity: 1;
                visibility: visible;
            }
            
            /* 背景样式 */
            .modal-backdrop-blur {
                background: rgba(15, 23, 42, 0.8);
                backdrop-filter: blur(8px);
            }
            
            .modal-backdrop-dark {
                background: rgba(0, 0, 0, 0.75);
            }
            
            .modal-backdrop-light {
                background: rgba(255, 255, 255, 0.9);
            }
            
            .modal-backdrop-transparent {
                background: transparent;
            }
            
            /* 模态框容器 */
            .modal-container {
                position: relative;
                background: var(--bg-primary);
                border: 1px solid var(--border-color);
                color: var(--text-primary);
                transform: scale(0.9) translateY(20px);
                transition: all var(--modal-duration, 300ms) ease;
                max-height: 90vh;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                margin: 0 1rem;
                width: fit-content;
                max-width: calc(100vw - 2rem);
            }
            
            .modal-overlay.show .modal-container {
                transform: scale(1) translateY(0);
            }
            
            /* 尺寸变体 */
            .modal-size-small { max-width: 400px; width: auto; min-width: 300px; }
            .modal-size-medium { max-width: 600px; width: auto; min-width: 400px; }
            .modal-size-large { max-width: 800px; width: auto; min-width: 600px; }
            .modal-size-xl { max-width: 1200px; width: auto; min-width: 600px; }
            .modal-size-full { width: 95%; height: 90%; }
            
            /* 圆角变体 */
            .modal-radius-small { border-radius: 6px; }
            .modal-radius-medium { border-radius: 12px; }
            .modal-radius-large { border-radius: 20px; }
            .modal-radius-rounded { border-radius: 50px; }
            
            /* 阴影变体 */
            .modal-shadow-small { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
            .modal-shadow-medium { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
            .modal-shadow-large { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1); }
            .modal-shadow-xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
            
            /* 动画变体 */
            .modal-animation-slide .modal-container {
                transform: translateY(-100px);
            }
            
            .modal-animation-zoom .modal-container {
                transform: scale(0.5);
            }
            
            .modal-animation-bounce .modal-container {
                transform: scale(0.3);
                transition: all var(--modal-duration, 300ms) cubic-bezier(0.68, -0.55, 0.265, 1.55);
            }
            
            /* 位置变体 */
            .modal-position-top {
                align-items: flex-start;
                padding-top: 5vh;
            }
            
            .modal-position-bottom {
                align-items: flex-end;
                padding-bottom: 5vh;
            }
            
            /* 模态框头部 */
            .modal-header {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 1.5rem;
                border-bottom: 1px solid var(--border-color);
                flex-shrink: 0;
                min-width: 0;
                position: relative;
            }

            .modal-title {
                margin: 0;
                font-size: 1.25rem;
                font-weight: 600;
                color: var(--text-primary);
                display: flex;
                align-items: center;
                gap: 0.75rem;
                line-height: 1;
                text-align: center;
                white-space: nowrap;
            }
            
            .modal-close {
                position: absolute;
                top: 1.5rem;
                right: 1.5rem;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border: none;
                background: transparent;
                color: var(--text-secondary);
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.2s ease;
                z-index: 1;
            }
            
            .modal-close:hover {
                background: var(--bg-secondary);
                color: var(--text-primary);
            }
            


            .modal-content {
                line-height: 1.6;
                width: 100%;
                word-wrap: break-word;
                background: transparent;
            }
            
            /* 模态框底部 */
            .modal-footer {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 0.75rem;
                padding: 1.5rem;
                border-top: 1px solid var(--border-color);
                flex-shrink: 0;
                min-width: 0;
                flex-wrap: wrap;
            }
            
            /* 按钮样式 */
            .modal-btn {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 1rem;
                border: 1px solid var(--border-color);
                border-radius: 6px;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                text-decoration: none;
            }
            
            .modal-btn-primary {
                background: var(--primary-color);
                color: white;
                border-color: var(--primary-color);
            }
            
            .modal-btn-primary:hover {
                background: var(--primary-hover);
                border-color: var(--primary-hover);
            }
            
            .modal-btn-secondary {
                background: var(--bg-secondary);
                color: var(--text-primary);
            }
            
            .modal-btn-secondary:hover {
                background: var(--bg-tertiary);
            }
            
            .modal-btn-danger {
                background: var(--error-color);
                color: white;
                border-color: var(--error-color);
            }
            
            .modal-btn-danger:hover {
                background: var(--error-hover);
                border-color: var(--error-hover);
            }
            
            /* 类型图标 */
            .modal-icon {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                margin: 0;
            }
            
            .modal-icon-info {
                background: rgba(var(--primary-rgb), 0.1);
                color: var(--primary-color);
            }
            
            .modal-icon-success {
                background: rgba(var(--success-rgb), 0.1);
                color: var(--success-color);
            }
            
            .modal-icon-warning {
                background: rgba(var(--warning-rgb), 0.1);
                color: var(--warning-color);
            }
            
            .modal-icon-error {
                background: rgba(var(--error-rgb), 0.1);
                color: var(--error-color);
            }
            
            /* 响应式设计 */
            @media (max-width: 768px) {
                .modal-container {
                    margin: 0;
                    max-height: 95vh;
                    width: calc(100vw - 1rem) !important;
                    max-width: calc(100vw - 1rem) !important;
                    min-width: auto !important;
                }

                .modal-size-small,
                .modal-size-medium,
                .modal-size-large,
                .modal-size-xl {
                    width: calc(100vw - 1rem) !important;
                    max-width: calc(100vw - 1rem) !important;
                    min-width: auto !important;
                }
                
                .modal-header,
                .modal-footer {
                    padding: 1rem;
                }
                
                .modal-footer {
                    flex-direction: column-reverse;
                }
                
                .modal-btn {
                    width: 100%;
                    justify-content: center;
                }
            }
        `;
    }
    
    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal && this.preferences.closeOnEscape) {
                this.close(this.activeModal);
            }
        });
    }

    /**
     * 显示信息弹窗
     */
    info(title, content, options = {}) {
        return this.show({
            type: 'info',
            title,
            content,
            icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="12" y1="16" x2="12" y2="12"/>
                <line x1="12" y1="8" x2="12.01" y2="8"/>
            </svg>`,
            buttons: [
                { text: '确定', type: 'primary', action: 'resolve' }
            ],
            ...options
        });
    }

    /**
     * 显示成功弹窗
     */
    success(title, content, options = {}) {
        return this.show({
            type: 'success',
            title,
            content,
            icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="20,6 9,17 4,12"/>
            </svg>`,
            buttons: [
                { text: '确定', type: 'primary', action: 'resolve' }
            ],
            ...options
        });
    }

    /**
     * 显示警告弹窗
     */
    warning(title, content, options = {}) {
        return this.show({
            type: 'warning',
            title,
            content,
            icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                <line x1="12" y1="9" x2="12" y2="13"/>
                <line x1="12" y1="17" x2="12.01" y2="17"/>
            </svg>`,
            buttons: [
                { text: '确定', type: 'primary', action: 'resolve' }
            ],
            ...options
        });
    }

    /**
     * 显示错误弹窗
     */
    error(title, content, options = {}) {
        return this.show({
            type: 'error',
            title,
            content,
            icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="15" y1="9" x2="9" y2="15"/>
                <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>`,
            buttons: [
                { text: '确定', type: 'primary', action: 'resolve' }
            ],
            ...options
        });
    }

    /**
     * 显示确认弹窗
     */
    confirm(title, content, options = {}) {
        return this.show({
            type: 'warning',
            title,
            content,
            icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="9" y1="9" x2="15" y2="15"/>
                <line x1="15" y1="9" x2="9" y2="15"/>
            </svg>`,
            buttons: [
                { text: '取消', type: 'secondary', action: 'reject' },
                { text: '确定', type: 'primary', action: 'resolve' }
            ],
            ...options
        });
    }

    /**
     * 显示自定义弹窗
     */
    custom(options = {}) {
        return this.show({
            type: 'custom',
            ...options
        });
    }

    /**
     * 显示弹窗的核心方法
     */
    show(config = {}) {
        return new Promise((resolve, reject) => {
            const modalId = this.generateId();
            const modalConfig = {
                id: modalId,
                resolve,
                reject,
                ...this.preferences,
                ...config
            };

            const modalElement = this.createModal(modalConfig);
            this.modals.set(modalId, { element: modalElement, config: modalConfig });

            // 添加到DOM
            document.body.appendChild(modalElement);

            // 设置z-index
            modalElement.style.zIndex = this.zIndexCounter++;

            // 显示动画
            requestAnimationFrame(() => {
                modalElement.classList.add('show');
                this.activeModal = modalId;

                // 自动聚焦
                if (modalConfig.autoFocus) {
                    const firstButton = modalElement.querySelector('.modal-btn');
                    if (firstButton) {
                        firstButton.focus();
                    }
                }
            });
        });
    }

    /**
     * 创建模态弹窗元素
     */
    createModal(config) {
        const overlay = document.createElement('div');
        overlay.className = this.getOverlayClasses(config);
        overlay.style.setProperty('--modal-duration', `${config.animationDuration}ms`);

        // 背景点击关闭
        if (config.closeOnBackdrop) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.close(config.id);
                }
            });
        }

        const container = document.createElement('div');
        container.className = this.getContainerClasses(config);

        // 创建头部
        if (config.title || config.showCloseButton) {
            const header = this.createHeader(config);
            container.appendChild(header);
        }

        // 创建内容
        const content = this.createContent(config);
        container.appendChild(content);

        // 创建底部
        if (config.buttons && config.buttons.length > 0) {
            const footer = this.createFooter(config);
            container.appendChild(footer);
        }

        overlay.appendChild(container);
        return overlay;
    }

    /**
     * 创建头部
     */
    createHeader(config) {
        const header = document.createElement('div');
        header.className = 'modal-header';

        if (config.title) {
            const title = document.createElement('h3');
            title.className = 'modal-title';

            if (config.icon && config.type !== 'custom') {
                const iconSpan = document.createElement('span');
                iconSpan.className = `modal-icon modal-icon-${config.type}`;
                iconSpan.innerHTML = config.icon;
                title.appendChild(iconSpan);
            }

            const titleText = document.createElement('span');
            titleText.textContent = config.title;
            title.appendChild(titleText);

            header.appendChild(title);
        }

        if (config.showCloseButton) {
            const closeBtn = document.createElement('button');
            closeBtn.className = 'modal-close';
            closeBtn.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"/>
                    <line x1="6" y1="6" x2="18" y2="18"/>
                </svg>
            `;
            closeBtn.addEventListener('click', () => this.close(config.id));
            header.appendChild(closeBtn);
        }

        return header;
    }

    /**
     * 创建内容
     */
    createContent(config) {
        const content = document.createElement('div');
        content.className = 'modal-content';
        content.style.cssText = 'padding: 1.5rem; overflow-y: auto; flex: 1; min-width: 0;';

        if (config.content) {
            if (typeof config.content === 'string') {
                content.innerHTML = config.content;
            } else if (config.content instanceof HTMLElement) {
                content.appendChild(config.content);
            }
        }

        return content;
    }

    /**
     * 创建底部
     */
    createFooter(config) {
        const footer = document.createElement('div');
        footer.className = 'modal-footer';

        config.buttons.forEach(buttonConfig => {
            const button = document.createElement('button');
            button.className = `modal-btn modal-btn-${buttonConfig.type || 'secondary'}`;
            button.textContent = buttonConfig.text;

            if (buttonConfig.icon) {
                button.innerHTML = `${buttonConfig.icon} ${buttonConfig.text}`;
            }

            button.addEventListener('click', async () => {
                if (buttonConfig.action === 'resolve') {
                    config.resolve(true);
                    this.close(config.id);
                } else if (buttonConfig.action === 'reject') {
                    config.reject(false);
                    this.close(config.id);
                } else if (typeof buttonConfig.action === 'function') {
                    // 执行自定义函数，检查返回值
                    try {
                        const result = await buttonConfig.action();
                        // 只有当函数返回 true 或 undefined 时才关闭弹窗
                        // 返回 false 时不关闭弹窗，允许用户重试
                        if (result !== false) {
                            config.resolve(result);
                            this.close(config.id);
                        }
                    } catch (error) {
                        console.error('按钮动作执行失败:', error);
                        // 发生错误时不关闭弹窗
                    }
                } else if (buttonConfig.value !== undefined) {
                    // 支持返回按钮的值
                    config.resolve(buttonConfig.value);
                    this.close(config.id);
                } else {
                    // 默认返回按钮文本
                    config.resolve(buttonConfig.text);
                    this.close(config.id);
                }
            });

            footer.appendChild(button);
        });

        return footer;
    }

    /**
     * 获取覆盖层CSS类
     */
    getOverlayClasses(config) {
        return [
            'modal-overlay',
            `modal-backdrop-${config.backdrop}`,
            `modal-position-${config.position}`,
            `modal-animation-${config.animation}`
        ].join(' ');
    }

    /**
     * 获取容器CSS类
     */
    getContainerClasses(config) {
        return [
            'modal-container',
            `modal-size-${config.maxWidth}`,
            `modal-radius-${config.borderRadius}`,
            `modal-shadow-${config.shadow}`
        ].join(' ');
    }

    /**
     * 关闭弹窗
     */
    close(modalId) {
        const modal = this.modals.get(modalId);
        if (!modal) return;

        const { element, config } = modal;

        // 移除显示类
        element.classList.remove('show');

        // 等待动画完成后移除元素
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            this.modals.delete(modalId);

            if (this.activeModal === modalId) {
                this.activeModal = null;
            }
        }, config.animationDuration);
    }

    /**
     * 关闭所有弹窗
     */
    closeAll() {
        this.modals.forEach((modal, modalId) => {
            this.close(modalId);
        });
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return `modal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取偏好设置
     */
    getPreferences() {
        return { ...this.preferences };
    }

    /**
     * 重置偏好设置为默认值
     */
    resetPreferences() {
        this.preferences = { ...this.options.defaults };
        this.savePreferences();
        console.log('🔄 模态弹窗偏好设置已重置为默认值');
    }
}

// 全局实例
window.ModalComponent = ModalComponent;

// 创建默认实例
if (typeof window !== 'undefined') {
    window.modalManager = new ModalComponent();

    // 便捷的全局函数
    window.showModal = {
        info: (title, content, options) => window.modalManager.info(title, content, options),
        success: (title, content, options) => window.modalManager.success(title, content, options),
        warning: (title, content, options) => window.modalManager.warning(title, content, options),
        error: (title, content, options) => window.modalManager.error(title, content, options),
        confirm: (title, content, options) => window.modalManager.confirm(title, content, options),
        custom: (options) => window.modalManager.custom(options)
    };
}
} // 结束 ModalComponent 类定义检查
