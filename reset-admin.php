<?php
/**
 * 管理员密码重置工具
 * 
 * 功能：重置管理员账户密码
 * 安全：仅限本地环境使用
 */

// 安全提醒（已移除环境限制）
// 注意：使用完毕后请删除此文件

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_password'])) {
    try {
        // 引入数据库配置
        require_once 'config/database.php';
        
        $username = trim($_POST['username'] ?? '');
        $new_password = trim($_POST['new_password'] ?? '');
        $confirm_password = trim($_POST['confirm_password'] ?? '');
        
        // 验证输入
        if (empty($username)) {
            throw new Exception('请输入用户名');
        }
        
        if (empty($new_password)) {
            throw new Exception('请输入新密码');
        }
        
        if (strlen($new_password) < 6) {
            throw new Exception('密码至少需要6个字符');
        }
        
        if ($new_password !== $confirm_password) {
            throw new Exception('两次输入的密码不一致');
        }
        
        // 连接数据库
        $db = Database::getInstance();
        
        // 检查用户是否存在
        $user = $db->fetch("SELECT id, username, email FROM users WHERE username = ?", [$username]);
        
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        // 生成新密码哈希
        $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        
        // 更新密码
        $affected = $db->update(
            "UPDATE users SET password = ?, updated_at = NOW() WHERE username = ?",
            [$password_hash, $username]
        );
        
        if ($affected > 0) {
            $message = "✅ 密码重置成功！用户 '{$username}' 的密码已更新。";
            $message_type = 'success';
        } else {
            throw new Exception('密码更新失败');
        }
        
    } catch (Exception $e) {
        $message = "❌ 重置失败: " . $e->getMessage();
        $message_type = 'error';
    }
}

// 获取所有管理员用户
$admin_users = [];
try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        $db = Database::getInstance();
        $admin_users = $db->fetchAll("SELECT username, email, nickname FROM users WHERE role = 'admin' ORDER BY username");
    }
} catch (Exception $e) {
    // 忽略数据库连接错误
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员密码重置 - 现代化PHP管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
            color: #856404;
        }
        
        .warning-box h3 {
            margin-bottom: 8px;
            color: #856404;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #ff6b6b;
        }
        
        .btn {
            width: 100%;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            padding: 14px 20px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .message {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .admin-list {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        
        .admin-list h3 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 1rem;
        }
        
        .admin-item {
            background: white;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
            border-left: 4px solid #ff6b6b;
        }
        
        .admin-item:last-child {
            margin-bottom: 0;
        }
        
        .admin-username {
            font-weight: 600;
            color: #333;
        }
        
        .admin-email {
            font-size: 0.9rem;
            color: #666;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
            color: #666;
            font-size: 14px;
        }
        
        .login-link {
            display: inline-block;
            margin-top: 15px;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background 0.3s;
        }
        
        .login-link:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔑 管理员密码重置</h1>
            <p>重置管理员账户密码</p>
        </div>
        
        <div class="warning-box">
            <h3>⚠️ 安全提醒</h3>
            <p>重置密码后请立即删除此文件。请勿在生产环境中使用。</p>
        </div>
        
        <?php if ($message): ?>
        <div class="message <?php echo $message_type; ?>">
            <?php echo htmlspecialchars($message); ?>
            <?php if ($message_type === 'success'): ?>
                <a href="login.php" class="login-link">前往登录</a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($admin_users)): ?>
        <div class="admin-list">
            <h3>📋 现有管理员账户</h3>
            <?php foreach ($admin_users as $admin): ?>
            <div class="admin-item">
                <div class="admin-username"><?php echo htmlspecialchars($admin['username']); ?></div>
                <div class="admin-email"><?php echo htmlspecialchars($admin['email']); ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <form method="post">
            <div class="form-group">
                <label for="username">管理员用户名</label>
                <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($_POST['username'] ?? 'admin'); ?>" required>
            </div>
            
            <div class="form-group">
                <label for="new_password">新密码</label>
                <input type="password" id="new_password" name="new_password" required minlength="6">
            </div>
            
            <div class="form-group">
                <label for="confirm_password">确认新密码</label>
                <input type="password" id="confirm_password" name="confirm_password" required minlength="6">
            </div>
            
            <button type="submit" name="reset_password" class="btn">
                🔄 重置密码
            </button>
        </form>
        
        <div class="footer">
            <p>⚠️ 重置完成后请删除此文件</p>
        </div>
    </div>
    
    <script>
        // 密码确认验证
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('new_password').value;
            const confirm = this.value;
            
            if (password !== confirm) {
                this.setCustomValidity('密码不一致');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
