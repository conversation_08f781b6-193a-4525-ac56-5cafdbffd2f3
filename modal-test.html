<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态弹窗样式测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .test-section h3 {
            font-size: 18px;
            margin-bottom: 15px;
            color: #555;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .test-btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .test-btn-primary:hover {
            background: #2563eb;
        }
        
        .test-btn-success {
            background: #10b981;
            color: white;
        }
        
        .test-btn-success:hover {
            background: #059669;
        }
        
        .test-btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .test-btn-warning:hover {
            background: #d97706;
        }
        
        .test-btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .test-btn-danger:hover {
            background: #dc2626;
        }
        
        .test-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0c4a6e;
        }
        
        .storage-info {
            background: #f9fafb;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🎭 模态弹窗样式持久化测试</h1>
        
        <div class="test-info">
            <strong>测试说明：</strong><br>
            1. 点击右下角的设置按钮，切换模态弹窗样式<br>
            2. 点击下方按钮测试不同类型的模态弹窗<br>
            3. 刷新页面，检查样式设置是否保持<br>
            4. 查看下方的存储信息，确认设置已正确保存
        </div>
        
        <div class="test-section">
            <h3>基础模态弹窗测试</h3>
            <div class="test-buttons">
                <button class="test-btn test-btn-primary" onclick="testInfo()">信息弹窗</button>
                <button class="test-btn test-btn-success" onclick="testSuccess()">成功弹窗</button>
                <button class="test-btn test-btn-warning" onclick="testWarning()">警告弹窗</button>
                <button class="test-btn test-btn-danger" onclick="testError()">错误弹窗</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>交互模态弹窗测试</h3>
            <div class="test-buttons">
                <button class="test-btn test-btn-primary" onclick="testConfirm()">确认弹窗</button>
                <button class="test-btn test-btn-primary" onclick="testCustom()">自定义弹窗</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>存储信息检查</h3>
            <div class="test-buttons">
                <button class="test-btn test-btn-primary" onclick="checkStorage()">检查存储状态</button>
                <button class="test-btn test-btn-warning" onclick="clearStorage()">清除存储</button>
            </div>
        </div>
        
        <div id="storageInfo" class="storage-info"></div>
    </div>

    <!-- 加载主要组件 -->
    <script src="assets/js/main.js"></script>

    <script>
        // 测试函数
        function testInfo() {
            window.showModal.info('信息提示', '这是一个信息提示弹窗，用于显示一般性信息。');
        }
        
        function testSuccess() {
            window.showModal.success('操作成功', '您的操作已成功完成！数据已保存。');
        }
        
        function testWarning() {
            window.showModal.warning('注意警告', '请注意：此操作可能会影响系统性能，确认继续吗？');
        }
        
        function testError() {
            window.showModal.error('错误提示', '抱歉，操作失败！请检查网络连接或联系管理员。');
        }
        
        function testConfirm() {
            window.showModal.confirm('确认操作', '您确定要删除这个项目吗？此操作不可撤销。')
                .then(() => {
                    window.showModal.success('已确认', '您选择了确认操作。');
                })
                .catch(() => {
                    window.showModal.info('已取消', '您取消了操作。');
                });
        }
        
        function testCustom() {
            window.showModal.custom({
                title: '自定义弹窗',
                content: `
                    <div style="text-align: center; padding: 20px;">
                        <h3 style="color: #3b82f6; margin-bottom: 15px;">🎨 自定义内容</h3>
                        <p>这是一个自定义的模态弹窗，可以包含任意HTML内容。</p>
                        <div style="margin: 20px 0;">
                            <input type="text" placeholder="输入一些内容..." style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px;">
                        </div>
                    </div>
                `,
                buttons: [
                    { text: '取消', type: 'secondary', action: 'reject' },
                    { text: '保存', type: 'primary', action: 'resolve' }
                ]
            });
        }
        
        function checkStorage() {
            const storageInfo = document.getElementById('storageInfo');
            const modalPrefs = localStorage.getItem('app_modal_preferences');
            const pageSettings = localStorage.getItem('pageSettings');
            
            let info = '=== 本地存储状态 ===\n\n';
            info += '模态弹窗偏好设置 (app_modal_preferences):\n';
            info += modalPrefs ? JSON.stringify(JSON.parse(modalPrefs), null, 2) : '未设置';
            info += '\n\n页面设置 (pageSettings):\n';
            info += pageSettings ? JSON.stringify(JSON.parse(pageSettings), null, 2) : '未设置';
            
            if (window.modalManager) {
                info += '\n\n当前模态弹窗管理器偏好:\n';
                info += JSON.stringify(window.modalManager.getPreferences(), null, 2);
            }
            
            storageInfo.textContent = info;
        }
        
        function clearStorage() {
            if (confirm('确定要清除所有存储的设置吗？')) {
                localStorage.removeItem('app_modal_preferences');
                localStorage.removeItem('pageSettings');
                window.showModal.success('清除完成', '所有设置已清除，请刷新页面查看效果。');
                checkStorage();
            }
        }
        
        // 页面加载完成后自动检查存储状态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkStorage();
            }, 1000);
        });
    </script>
</body>
</html>
