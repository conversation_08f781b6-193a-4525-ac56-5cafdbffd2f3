/* ===== 会员管理页面样式 ===== */

/* 基础容器 */
.membership-container {
    padding: var(--spacing-6);
    max-width: 1400px;
    margin: 0 auto;
}

/* ===== 页面头部 ===== */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-8);
    gap: var(--spacing-6);
}

.header-left {
    flex: 1;
}

.page-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-2) 0;
}

.title-icon {
    color: var(--primary-color);
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
    line-height: 1.5;
}

.header-actions {
    display: flex;
    gap: var(--spacing-3);
}

/* ===== 统计卡片 ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-5);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-card.warning {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fef3c7 0%, #fef9e7 100%);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon.free {
    background: #f3f4f6;
    color: #6b7280;
}

.stat-icon.premium {
    background: #dbeafe;
    color: #2563eb;
}

.stat-icon.vip {
    background: #fce7f3;
    color: #be185d;
}

.stat-icon svg {
    width: 24px;
    height: 24px;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 500;
}

/* ===== 搜索和过滤 ===== */
.filters-section {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-5);
    margin-bottom: var(--spacing-6);
}

.filters-form {
    display: flex;
    gap: var(--spacing-4);
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-icon {
    position: absolute;
    left: var(--spacing-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    pointer-events: none;
}

.search-box input {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-3) 2.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.filter-select {
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    min-width: 150px;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* ===== 按钮样式 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid transparent;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
}

.btn-outline {
    background: transparent;
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.btn-outline:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* ===== 用户列表 ===== */
.users-section {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-5);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.user-count {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.users-table-container {
    overflow-x: auto;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th {
    background: var(--bg-secondary);
    padding: var(--spacing-4);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap;
}

.users-table td {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.users-table tr:hover {
    background: var(--bg-secondary);
}

/* ===== 用户信息 ===== */
.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.user-details {
    min-width: 0;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    margin-bottom: 2px;
}

.user-email {
    font-size: 0.75rem;
    color: var(--text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* ===== 标签样式 ===== */
.group-badge, .membership-type, .status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.group-badge.free, .membership-type.free {
    background: #f3f4f6;
    color: #374151;
}

.group-badge.premium, .membership-type.paid {
    background: #dbeafe;
    color: #1e40af;
}

.group-badge.vip, .membership-type.gift {
    background: #fce7f3;
    color: #be185d;
}

.membership-type.trial {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.active, .status-badge.permanent {
    background: #dcfce7;
    color: #166534;
}

.status-badge.expired {
    background: #fee2e2;
    color: #991b1b;
}

.status-badge.unknown {
    background: #f3f4f6;
    color: #6b7280;
}

/* ===== 到期信息 ===== */
.expire-info {
    text-align: center;
}

.expire-date {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 2px;
}

.days-remaining {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.days-remaining.warning {
    color: #dc2626;
    font-weight: 600;
}

.permanent {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* ===== 操作按钮 ===== */
.action-buttons {
    display: flex;
    gap: var(--spacing-2);
}

.btn-action {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
}

.btn-action:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* ===== 分页 ===== */
.pagination {
    display: flex;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-5);
    border-top: 1px solid var(--border-color);
}

.page-btn {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: var(--transition);
}

.page-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.page-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
    .membership-container {
        padding: var(--spacing-4);
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: var(--spacing-3);
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-4);
    }
    
    .filters-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .users-table-container {
        font-size: 0.875rem;
    }
    
    .users-table th,
    .users-table td {
        padding: var(--spacing-3);
    }
    
    .user-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .membership-container {
        padding: var(--spacing-3);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: var(--spacing-4);
    }
    
    .page-title {
        font-size: 1.5rem;
    }
}

/* ===== 模态框样式 ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--spacing-4);
}

.modal-dialog {
    background: var(--bg-primary);
    border-radius: 12px;
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-5);
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-5);
    overflow-y: auto;
    flex: 1;
}

/* ===== 表单样式 ===== */
.modal-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.user-display {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    background: var(--bg-secondary);
    border-radius: 8px;
}

.form-actions {
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;
    margin-top: var(--spacing-4);
}

/* ===== 历史记录样式 ===== */
.history-header {
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-3);
    border-bottom: 1px solid var(--border-color);
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    background: var(--bg-secondary);
    border-radius: 8px;
}

.history-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.history-icon.upgrade {
    background: #dcfce7;
    color: #166534;
}

.history-icon.downgrade {
    background: #fee2e2;
    color: #991b1b;
}

.history-icon.renew {
    background: #dbeafe;
    color: #1e40af;
}

.history-icon.suspend {
    background: #fef3c7;
    color: #92400e;
}

.history-icon.activate {
    background: #dcfce7;
    color: #166534;
}

.history-icon.expire {
    background: #f3f4f6;
    color: #6b7280;
}

.history-content {
    flex: 1;
    min-width: 0;
}

.history-action {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    margin-bottom: 2px;
}

.history-details {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.history-meta {
    display: flex;
    gap: var(--spacing-3);
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

.empty-history {
    text-align: center;
    padding: var(--spacing-8);
    color: var(--text-secondary);
    font-style: italic;
}

/* ===== 加载和错误状态 ===== */
.loading-state,
.error-state {
    text-align: center;
    padding: var(--spacing-8);
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-3);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
