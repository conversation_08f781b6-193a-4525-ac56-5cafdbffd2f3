<?php
/**
 * 👤 现代化PHP管理系统 - 个人资料页面
 * 
 * 功能：用户个人资料管理
 * 设计：三段式布局（头部导航栏、主内容区域、页脚）
 * 特色：响应式设计、主题切换、实时编辑
 */

// 引入认证中间件
require_once '../auth/Auth.php';
require_once '../auth/SystemConfig.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 要求用户登录
Auth::requireLogin('../login.php');

// 获取当前用户信息
$current_user = Auth::getCurrentUser();

// 处理表单提交
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    try {
        require_once '../config/database.php';
        require_once '../auth/SystemConfig.php';

        $nickname = trim($_POST['nickname'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $verify_code = trim($_POST['verify_code'] ?? '');
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';

        $db = Database::getInstance();

        // 检查是否需要修改密码
        if (!empty($new_password)) {
            // 检查安全策略是否要求验证码
            $require_verify_code = SystemConfig::get('security', 'profile_require_verify_code', '1') === '1';

            if ($require_verify_code) {
                if (empty($verify_code)) {
                    throw new Exception('修改密码需要邮箱验证码');
                }

                // 验证验证码
                $verify_result = Auth::verifyProfileCode($current_user['id'], $current_user['email'], $verify_code, 'profile_update');
                if (!$verify_result['success']) {
                    throw new Exception($verify_result['message']);
                }
            }

            // 检查密码复杂度策略
            $min_length = intval(SystemConfig::get('security', 'password_min_length', '6'));
            $require_uppercase = SystemConfig::get('security', 'password_require_uppercase', '0') === '1';
            $require_lowercase = SystemConfig::get('security', 'password_require_lowercase', '0') === '1';
            $require_numbers = SystemConfig::get('security', 'password_require_numbers', '0') === '1';
            $require_symbols = SystemConfig::get('security', 'password_require_symbols', '0') === '1';

            if (strlen($new_password) < $min_length) {
                throw new Exception("新密码至少需要{$min_length}个字符");
            }

            if ($require_uppercase && !preg_match('/[A-Z]/', $new_password)) {
                throw new Exception('新密码必须包含大写字母');
            }

            if ($require_lowercase && !preg_match('/[a-z]/', $new_password)) {
                throw new Exception('新密码必须包含小写字母');
            }

            if ($require_numbers && !preg_match('/[0-9]/', $new_password)) {
                throw new Exception('新密码必须包含数字');
            }

            if ($require_symbols && !preg_match('/[^A-Za-z0-9]/', $new_password)) {
                throw new Exception('新密码必须包含特殊字符');
            }

            if ($new_password !== $confirm_password) {
                throw new Exception('两次输入的新密码不一致');
            }
        }
        
        // 检查邮箱是否被其他用户使用
        if ($email !== $current_user['email']) {
            $existing = $db->fetch("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $current_user['id']]);
            if ($existing) {
                throw new Exception('该邮箱已被其他用户使用');
            }
        }
        
        // 更新用户信息
        if (!empty($new_password)) {
            $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
            $db->update(
                "UPDATE users SET nickname = ?, email = ?, password = ?, updated_at = NOW() WHERE id = ?",
                [$nickname, $email, $password_hash, $current_user['id']]
            );
        } else {
            $db->update(
                "UPDATE users SET nickname = ?, email = ?, updated_at = NOW() WHERE id = ?",
                [$nickname, $email, $current_user['id']]
            );
        }
        
        // 记录操作日志
        Auth::logOperation('profile_update', '更新个人资料', [
            'nickname' => $nickname,
            'email' => $email,
            'password_changed' => !empty($new_password)
        ]);
        
        $message = '个人资料更新成功！';
        $message_type = 'success';
        
        // 更新会话中的用户信息
        $_SESSION['user_email'] = $email;
        $_SESSION['user_name'] = $nickname ?: $current_user['username'];
        
        // 重新获取用户信息
        $current_user = Auth::getCurrentUser();
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $message_type = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle('个人资料'); ?></title>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- SEO和社交媒体标签 -->
    <meta name="description" content="个人资料管理页面">
    <meta name="keywords" content="个人资料, 用户设置, 账户管理">
    <meta name="author" content="现代化PHP管理系统">
    
    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>👤</text></svg>">

    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- 仪表盘容器 -->
    <div class="dashboard-container">
        <!-- 头部导航栏 -->
        <?php include 'includes/header.php'; ?>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                        个人资料
                    </h1>
                    <p class="page-subtitle">管理您的个人信息和账户设置</p>
                </div>
                
                <!-- 消息提示 -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type; ?>">
                    <div class="alert-content">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <?php if ($message_type === 'success'): ?>
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                            <polyline points="22,4 12,14.01 9,11.01"/>
                            <?php else: ?>
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="15" y1="9" x2="9" y2="15"/>
                            <line x1="9" y1="9" x2="15" y2="15"/>
                            <?php endif; ?>
                        </svg>
                        <span><?php echo htmlspecialchars($message); ?></span>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- 个人资料内容 -->
                <div class="profile-content">
                    <div class="profile-grid">
                        <!-- 用户信息卡片 -->
                        <div class="profile-card user-info-card">
                            <div class="card-header">
                                <h2 class="card-title">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                        <circle cx="12" cy="7" r="4"/>
                                    </svg>
                                    用户信息
                                </h2>
                            </div>
                            <div class="card-content">
                                <div class="user-avatar-section">
                                    <div class="user-avatar-large">
                                        <?php
                                        // 生成用户头像首字母
                                        $initials = '';
                                        $name_parts = explode(' ', $current_user['name']);
                                        foreach ($name_parts as $part) {
                                            if (!empty($part)) {
                                                $initials .= mb_substr($part, 0, 1);
                                                if (mb_strlen($initials) >= 2) break;
                                            }
                                        }
                                        if (empty($initials)) {
                                            $initials = mb_substr($current_user['username'], 0, 2);
                                        }
                                        ?>
                                        <span class="avatar-text-xl"><?php echo strtoupper($initials); ?></span>
                                    </div>
                                    <div class="user-basic-info">
                                        <h3><?php echo htmlspecialchars($current_user['name']); ?></h3>
                                        <p class="user-username">@<?php echo htmlspecialchars($current_user['username']); ?></p>
                                        <span class="user-role-badge">
                                            <?php
                                            $role_names = [
                                                Auth::ROLE_ADMIN => '管理员',
                                                Auth::ROLE_USER => '用户',
                                                Auth::ROLE_GUEST => '访客'
                                            ];
                                            echo $role_names[$current_user['role']] ?? '未知';
                                            ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="user-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">注册时间</span>
                                        <span class="stat-value"><?php echo date('Y年m月d日', $current_user['login_time']); ?></span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">最后登录</span>
                                        <span class="stat-value"><?php echo date('Y-m-d H:i', $current_user['last_activity']); ?></span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">账户状态</span>
                                        <span class="stat-value status-active">
                                            <span class="status-dot"></span>
                                            活跃
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 编辑资料表单 -->
                        <div class="profile-card edit-form-card">
                            <div class="card-header">
                                <h2 class="card-title">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                    </svg>
                                    编辑资料
                                </h2>
                            </div>
                            <div class="card-content">
                                <form method="post" class="profile-form">
                                    <div class="form-section">
                                        <h4 class="section-title">基本信息</h4>
                                        
                                        <div class="form-group">
                                            <label for="nickname">昵称</label>
                                            <input type="text" id="nickname" name="nickname" 
                                                   value="<?php echo htmlspecialchars($current_user['name']); ?>" 
                                                   placeholder="请输入昵称">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="email">邮箱地址</label>
                                            <input type="email" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($current_user['email']); ?>" 
                                                   placeholder="请输入邮箱地址" required>
                                        </div>
                                    </div>
                                    
                                    <div class="form-section">
                                        <h4 class="section-title">修改密码</h4>
                                        <p class="section-description">修改密码需要邮箱验证码验证</p>

                                        <div class="form-group">
                                            <label for="verify_code">邮箱验证码</label>
                                            <div class="verify-code-input">
                                                <input type="text" id="verify_code" name="verify_code"
                                                       placeholder="请输入邮箱验证码" maxlength="6">
                                                <button type="button" id="send_verify_code" class="btn btn-secondary">
                                                    发送验证码
                                                </button>
                                            </div>
                                            <small class="form-help">验证码将发送到您当前绑定的邮箱</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="new_password">新密码</label>
                                            <input type="password" id="new_password" name="new_password"
                                                   placeholder="请输入新密码（至少6个字符）" minlength="6">
                                        </div>

                                        <div class="form-group">
                                            <label for="confirm_password">确认新密码</label>
                                            <input type="password" id="confirm_password" name="confirm_password"
                                                   placeholder="请再次输入新密码">
                                        </div>
                                    </div>
                                    
                                    <div class="form-actions">
                                        <button type="submit" name="update_profile" class="btn btn-primary">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                                                <polyline points="17,21 17,13 7,13 7,21"/>
                                                <polyline points="7,3 7,8 15,8"/>
                                            </svg>
                                            保存更改
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="1,4 1,10 7,10"/>
                                                <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                                            </svg>
                                            重置
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- 页脚 -->
        <?php include 'includes/footer.php'; ?>
    </div>

    <!-- 个人资料页面脚本 -->
    <script>
        /**
         * 个人资料页面初始化
         */
        function initializeProfile() {
            console.log('👤 个人资料页面已加载');

            // 初始化表单验证
            initializeFormValidation();

            // 初始化密码强度检查
            initializePasswordStrength();

            // 初始化验证码功能
            initializeVerifyCode();
        }
        
        // 表单验证
        function initializeFormValidation() {
            const form = document.querySelector('.profile-form');
            const newPassword = document.getElementById('new_password');
            const confirmPassword = document.getElementById('confirm_password');
            const currentPassword = document.getElementById('current_password');
            
            // 密码确认验证
            function validatePasswordMatch() {
                if (newPassword.value && confirmPassword.value) {
                    if (newPassword.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('两次输入的密码不一致');
                    } else {
                        confirmPassword.setCustomValidity('');
                    }
                }
            }
            
            // 检查是否需要当前密码
            function checkCurrentPasswordRequired() {
                if (newPassword.value && !currentPassword.value) {
                    currentPassword.setCustomValidity('修改密码时必须输入当前密码');
                } else {
                    currentPassword.setCustomValidity('');
                }
            }
            
            newPassword.addEventListener('input', () => {
                validatePasswordMatch();
                checkCurrentPasswordRequired();
            });
            
            confirmPassword.addEventListener('input', validatePasswordMatch);
            currentPassword.addEventListener('input', checkCurrentPasswordRequired);
            
            // 表单提交验证
            form.addEventListener('submit', function(e) {
                validatePasswordMatch();
                checkCurrentPasswordRequired();
                
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        }
        
        // 密码强度检查
        function initializePasswordStrength() {
            const newPassword = document.getElementById('new_password');
            
            newPassword.addEventListener('input', function() {
                const password = this.value;
                // 这里可以添加密码强度指示器
                console.log('密码强度检查:', password.length);
            });
        }
        
        // 重置表单
        function resetForm() {
            const form = document.querySelector('.profile-form');
            const passwordFields = form.querySelectorAll('input[type="password"]');

            // 只重置密码字段
            passwordFields.forEach(field => {
                field.value = '';
                field.setCustomValidity('');
            });
        }

        // 初始化验证码功能
        function initializeVerifyCode() {
            const sendCodeBtn = document.getElementById('send_verify_code');
            const verifyCodeInput = document.getElementById('verify_code');
            const newPasswordInput = document.getElementById('new_password');

            if (!sendCodeBtn || !verifyCodeInput) return;

            let countdown = 0;
            let countdownTimer = null;

            // 发送验证码按钮点击事件
            sendCodeBtn.addEventListener('click', async function() {
                if (countdown > 0) return;

                try {
                    // 设置按钮为加载状态
                    sendCodeBtn.disabled = true;
                    sendCodeBtn.classList.add('loading');
                    sendCodeBtn.textContent = '发送中...';

                    const response = await fetch('../api/profile-verify.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'send_code',
                            action_type: 'profile_update'
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        showToast('验证码已发送到您的邮箱', 'success');
                        startCountdown(data.expire_minutes * 60 || 1800); // 默认30分钟
                    } else {
                        showToast(data.message || '发送验证码失败', 'error');
                        resetSendButton();
                    }

                } catch (error) {
                    console.error('发送验证码失败:', error);
                    showToast('网络错误，请重试', 'error');
                    resetSendButton();
                }
            });

            // 开始倒计时
            function startCountdown(seconds) {
                countdown = seconds;
                updateCountdownDisplay();

                countdownTimer = setInterval(() => {
                    countdown--;
                    updateCountdownDisplay();

                    if (countdown <= 0) {
                        clearInterval(countdownTimer);
                        resetSendButton();
                    }
                }, 1000);
            }

            // 更新倒计时显示
            function updateCountdownDisplay() {
                if (countdown > 0) {
                    const minutes = Math.floor(countdown / 60);
                    const seconds = countdown % 60;
                    sendCodeBtn.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                    sendCodeBtn.disabled = true;
                    sendCodeBtn.classList.remove('loading');
                }
            }

            // 重置发送按钮
            function resetSendButton() {
                sendCodeBtn.disabled = false;
                sendCodeBtn.classList.remove('loading');
                sendCodeBtn.textContent = '发送验证码';
                countdown = 0;
                if (countdownTimer) {
                    clearInterval(countdownTimer);
                    countdownTimer = null;
                }
            }

            // 监听密码输入，如果有密码输入则要求验证码
            newPasswordInput.addEventListener('input', function() {
                if (this.value.trim()) {
                    verifyCodeInput.required = true;
                } else {
                    verifyCodeInput.required = false;
                }
            });
        }
        
        // 页面加载完成时初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeProfile);
        } else {
            initializeProfile();
        }
    </script>
</body>
</html>
