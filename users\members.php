<?php
/**
 * 👑 会员管理页面
 * 
 * 功能：会员管理、分组管理、权限控制
 */

session_start();

// 引入认证中间件
require_once '../auth/auth.php';
require_once '../auth/SystemConfig.php';
require_once 'classes/MemberManager.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 要求用户登录
Auth::requireLogin('../login.php');

// 检查管理员权限
if (!Auth::hasRole(Auth::ROLE_ADMIN)) {
    header('Location: index.php?error=permission_denied');
    exit;
}

// 获取当前用户信息
$current_user = Auth::getCurrentUser();
$page_title = '会员管理';

// 初始化会员管理器
$memberManager = new MemberManager();

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_SERVER['HTTP_X_REQUESTED_WITH'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_user_group':
                $userId = intval($_POST['user_id']);
                $groupKey = $_POST['group_key'];
                $result = $memberManager->updateUserGroup_User($userId, $groupKey, $current_user['id']);
                echo json_encode(['success' => true, 'message' => '分组更新成功']);
                break;
                
            case 'set_member_expiry':
                $userId = intval($_POST['user_id']);
                $expireDate = $_POST['expire_date'] ?: null;
                $result = $memberManager->setMemberExpiry($userId, $expireDate, $current_user['id']);
                echo json_encode(['success' => true, 'message' => '会员期限设置成功']);
                break;
                
            case 'adjust_points':
                $userId = intval($_POST['user_id']);
                $pointsChange = intval($_POST['points_change']);
                $reason = $_POST['reason'];
                $result = $memberManager->adjustUserPoints($userId, $pointsChange, $reason, $current_user['id']);
                echo json_encode(['success' => true, 'message' => '积分调整成功']);
                break;
                
            default:
                throw new Exception('无效的操作');
        }
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// 获取筛选参数
$filters = [
    'search' => trim($_GET['search'] ?? ''),
    'user_group' => $_GET['user_group'] ?? '',
    'member_type' => $_GET['member_type'] ?? '',
    'status' => $_GET['status'] ?? '',
    'member_status' => $_GET['member_status'] ?? ''
];

$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 15;

// 获取用户列表
$userData = $memberManager->getUsersWithGroups($filters, $page, $perPage);
$users = $userData['users'];
$totalPages = $userData['pages'];

// 获取用户分组列表
$userGroups = $memberManager->getUserGroups();

// 获取统计信息
$stats = $memberManager->getMemberStats();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle($page_title); ?></title>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- SEO和社交媒体标签 -->
    <meta name="description" content="会员管理页面，管理用户分组、会员权限和积分系统">
    <meta name="keywords" content="会员管理, 用户分组, 权限管理, 积分系统">
    <meta name="author" content="现代化PHP管理系统">

    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>👑</text></svg>">

    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="members-page">
    <!-- 仪表盘容器 -->
    <div class="dashboard-container">
        <!-- 头部导航栏 -->
        <?php include 'includes/header.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                            <circle cx="9" cy="7" r="4"/>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                        </svg>
                        会员管理
                    </h1>
                    <p class="page-subtitle">管理用户分组、会员权限和积分系统</p>
                </div>

                <!-- 操作工具栏 -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <!-- 搜索框 -->
                        <div class="search-box">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="M21 21l-4.35-4.35"/>
                            </svg>
                            <input type="text" id="searchInput" placeholder="搜索用户名、邮箱或昵称..."
                                   value="<?php echo htmlspecialchars($filters['search']); ?>">
                        </div>

                        <!-- 筛选器 -->
                        <div class="filter-group">
                            <select id="userGroupFilter" class="filter-select">
                                <option value="">所有分组</option>
                                <?php foreach ($userGroups as $group): ?>
                                <option value="<?php echo $group['group_key']; ?>"
                                        <?php echo $filters['user_group'] === $group['group_key'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($group['group_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>

                            <select id="memberTypeFilter" class="filter-select">
                                <option value="">所有类型</option>
                                <option value="regular" <?php echo $filters['member_type'] === 'regular' ? 'selected' : ''; ?>>普通用户</option>
                                <option value="vip" <?php echo $filters['member_type'] === 'vip' ? 'selected' : ''; ?>>VIP会员</option>
                                <option value="premium" <?php echo $filters['member_type'] === 'premium' ? 'selected' : ''; ?>>高级会员</option>
                            </select>

                            <select id="memberStatusFilter" class="filter-select">
                                <option value="">会员状态</option>
                                <option value="active" <?php echo $filters['member_status'] === 'active' ? 'selected' : ''; ?>>有效</option>
                                <option value="expired" <?php echo $filters['member_status'] === 'expired' ? 'selected' : ''; ?>>已过期</option>
                            </select>

                            <select id="statusFilter" class="filter-select">
                                <option value="">账户状态</option>
                                <option value="active" <?php echo $filters['status'] === 'active' ? 'selected' : ''; ?>>正常</option>
                                <option value="inactive" <?php echo $filters['status'] === 'inactive' ? 'selected' : ''; ?>>未激活</option>
                                <option value="banned" <?php echo $filters['status'] === 'banned' ? 'selected' : ''; ?>>已封禁</option>
                            </select>
                        </div>
                    </div>

                    <div class="toolbar-right">
                        <!-- 分组管理按钮 -->
                        <a href="member-groups.php" class="btn btn-secondary" title="分组管理">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            </svg>
                            分组管理
                        </a>
                    </div>
                </div>
                <!-- 统计信息 - 会员管理专用设计 -->
                <div class="stats-grid">
                    <div class="card-google card-blue">
                        <div class="card-content">
                            <div class="card-left">
                                <div class="card-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="card-title">总会员数</div>
                            </div>
                            <div class="card-right">
                                <div class="card-value" id="total-users"><?php echo number_format($stats['total_users']); ?></div>
                                <div class="card-description">系统注册会员总数</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-google card-green">
                        <div class="card-content">
                            <div class="card-left">
                                <div class="card-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <div class="card-title">VIP会员</div>
                            </div>
                            <div class="card-right">
                                <div class="card-value" id="vip-members">
                                    <?php
                                    $vipCount = 0;
                                    foreach ($users as $user) {
                                        if ($user['member_type'] === 'vip') $vipCount++;
                                    }
                                    echo number_format($vipCount);
                                    ?>
                                </div>
                                <div class="card-description">VIP会员用户数</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-google card-orange">
                        <div class="card-content">
                            <div class="card-left">
                                <div class="card-icon">
                                    <i class="fas fa-gem"></i>
                                </div>
                                <div class="card-title">高级会员</div>
                            </div>
                            <div class="card-right">
                                <div class="card-value" id="premium-members">
                                    <?php
                                    $premiumCount = 0;
                                    foreach ($users as $user) {
                                        if ($user['member_type'] === 'premium') $premiumCount++;
                                    }
                                    echo number_format($premiumCount);
                                    ?>
                                </div>
                                <div class="card-description">高级会员用户数</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-google card-red">
                        <div class="card-content">
                            <div class="card-left">
                                <div class="card-icon">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div class="card-title">积分总量</div>
                            </div>
                            <div class="card-right">
                                <div class="card-value" id="total-points">
                                    <?php
                                    $totalPoints = 0;
                                    foreach ($users as $user) {
                                        $totalPoints += $user['total_points'];
                                    }
                                    echo number_format($totalPoints);
                                    ?>
                                </div>
                                <div class="card-description">系统积分总量</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 会员列表 -->
                <div class="users-table-container">
                    <div class="table-header">
                        <h2 class="table-title">会员列表</h2>
                        <div class="table-info">
                            共 <?php echo number_format(count($users)); ?> 个会员
                        </div>
                    </div>

                    <?php if (empty($users)): ?>
                    <div class="empty-state">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                            <circle cx="9" cy="7" r="4"/>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                        </svg>
                        <h3>暂无会员</h3>
                        <p>没有找到符合条件的会员</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="users-table">
                            <thead>
                                <tr>
                                    <th>会员信息</th>
                                    <th>分组</th>
                                    <th>会员等级</th>
                                    <th>积分</th>
                                    <th>状态</th>
                                    <th>注册时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="user-profile-card">
                                            <div class="user-avatar-wrapper">
                                                <div class="user-avatar">
                                                    <?php if (!empty($user['avatar'])): ?>
                                                    <img src="<?php echo htmlspecialchars($user['avatar']); ?>" alt="头像">
                                                    <?php else: ?>
                                                    <div class="avatar-placeholder">
                                                        <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="user-info-wrapper">
                                                <div class="user-name-row">
                                                    <h4 class="user-display-name">
                                                        <?php echo htmlspecialchars($user['nickname'] ?: $user['username']); ?>
                                                    </h4>
                                                    <span class="user-handle">@<?php echo htmlspecialchars($user['username']); ?></span>
                                                    <?php if ($user['email_verified']): ?>
                                                    <span class="verified-icon" title="邮箱已验证">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                            <polyline points="20,6 9,17 4,12"/>
                                                        </svg>
                                                    </span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="user-details-row">
                                                    <span class="user-contact"><?php echo htmlspecialchars($user['email']); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="role-badge role-<?php echo $user['group_key'] ?? 'default'; ?>" style="background: <?php echo $user['group_color'] ?? '#6b7280'; ?>20; color: <?php echo $user['group_color'] ?? '#6b7280'; ?>;">
                                            <?php echo htmlspecialchars($user['group_name'] ?? '默认分组'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="member-level-info">
                                            <span class="member-type-badge member-type-<?php echo $user['member_type']; ?>">
                                                <?php
                                                $memberTypes = ['regular' => '普通用户', 'vip' => 'VIP会员', 'premium' => '高级会员'];
                                                echo $memberTypes[$user['member_type']];
                                                ?>
                                            </span>
                                            <?php if ($user['member_expire_date']): ?>
                                            <div class="member-expiry-info">到期：<?php echo date('Y-m-d', strtotime($user['member_expire_date'])); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="points-display">
                                            <div class="points-available"><?php echo number_format($user['available_points']); ?></div>
                                            <div class="points-total">总计：<?php echo number_format($user['total_points']); ?></div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $user['status']; ?>">
                                            <?php
                                            $statusLabels = ['active' => '正常', 'inactive' => '未激活', 'banned' => '已封禁'];
                                            echo $statusLabels[$user['status']] ?? $user['status'];
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="date-info">
                                            <?php echo date('Y-m-d', strtotime($user['created_at'])); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon" onclick="editUser(<?php echo $user['id']; ?>)" title="编辑用户">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                                </svg>
                                            </button>

                                            <button class="btn-icon warning" onclick="manageMember(<?php echo $user['id']; ?>)" title="会员管理">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                                </svg>
                                            </button>

                                            <button class="btn-icon success" onclick="manageMember(<?php echo $user['id']; ?>)" title="积分管理">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <circle cx="12" cy="12" r="3"/>
                                                    <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- 分页导航 -->
                <?php if ($totalPages > 1): ?>
                <div class="pagination-wrapper">
                    <nav class="pagination">
                        <?php
                        $currentUrl = $_SERVER['REQUEST_URI'];
                        $urlParts = parse_url($currentUrl);
                        parse_str($urlParts['query'] ?? '', $queryParams);

                        // 上一页
                        if ($page > 1):
                            $queryParams['page'] = $page - 1;
                            $prevUrl = '?' . http_build_query($queryParams);
                        ?>
                        <a href="<?php echo $prevUrl; ?>" class="pagination-btn prev-btn">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                            上一页
                        </a>
                        <?php endif; ?>

                        <!-- 页码按钮 -->
                        <div class="pagination-numbers">
                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $page + 2);

                            if ($startPage > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($queryParams, ['page' => 1])); ?>" class="pagination-btn">1</a>
                                <?php if ($startPage > 2): ?>
                                    <span class="pagination-ellipsis">...</span>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php for ($i = $startPage; $i <= $endPage; $i++):
                                $queryParams['page'] = $i;
                                $pageUrl = '?' . http_build_query($queryParams);
                            ?>
                            <a href="<?php echo $pageUrl; ?>" class="pagination-btn <?php echo ($i === $page) ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                            <?php endfor; ?>

                            <?php if ($endPage < $totalPages): ?>
                                <?php if ($endPage < $totalPages - 1): ?>
                                    <span class="pagination-ellipsis">...</span>
                                <?php endif; ?>
                                <a href="?<?php echo http_build_query(array_merge($queryParams, ['page' => $totalPages])); ?>" class="pagination-btn"><?php echo $totalPages; ?></a>
                            <?php endif; ?>
                        </div>

                        <!-- 下一页 -->
                        <?php if ($page < $totalPages):
                            $queryParams['page'] = $page + 1;
                            $nextUrl = '?' . http_build_query($queryParams);
                        ?>
                        <a href="<?php echo $nextUrl; ?>" class="pagination-btn next-btn">
                            下一页
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"/>
                            </svg>
                        </a>
                        <?php endif; ?>
                    </nav>

                    <div class="pagination-info">
                        显示第 <?php echo (($page - 1) * $perPage + 1); ?> - <?php echo min($page * $perPage, count($users)); ?> 条，共 <?php echo count($users); ?> 条记录
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </main>
        
        <!-- 页脚 -->
        <?php include 'includes/footer.php'; ?>
    </div>
    
    <!-- 会员管理模态框 -->
    <div id="memberModal" class="modal-overlay" style="display: none;">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    会员管理
                </h3>
                <button class="modal-close" onclick="closeModal()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <form id="memberForm">
                    <input type="hidden" id="modalUserId" name="user_id">

                    <div class="form-group">
                        <label for="userGroup">用户分组</label>
                        <select id="userGroup" name="group_key" class="form-control">
                            <?php foreach ($userGroups as $group): ?>
                            <option value="<?php echo $group['group_key']; ?>">
                                <?php echo htmlspecialchars($group['group_name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="memberExpiry">会员到期时间</label>
                        <input type="date" id="memberExpiry" name="expire_date" class="form-control">
                        <small class="form-text">留空表示永久会员</small>
                    </div>

                    <div class="form-group">
                        <label for="pointsChange">积分调整</label>
                        <input type="number" id="pointsChange" name="points_change" class="form-control" placeholder="正数增加，负数减少">
                    </div>

                    <div class="form-group">
                        <label for="pointsReason">调整原因</label>
                        <input type="text" id="pointsReason" name="reason" class="form-control" placeholder="积分调整原因">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveMemberChanges()">保存更改</button>
            </div>
        </div>
    </div>
    
    <!-- 引入主要JavaScript文件 -->
    <script src="assets/js/main.js"></script>

    <!-- 会员管理JavaScript -->
    <script>
    // 会员管理功能
    let currentUserId = null;

    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('tbody tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    // 筛选功能
    function setupFilters() {
        const filters = ['userGroupFilter', 'memberTypeFilter', 'memberStatusFilter', 'statusFilter'];

        filters.forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', applyFilters);
            }
        });
    }

    function applyFilters() {
        const searchParams = new URLSearchParams();

        const search = document.getElementById('searchInput').value;
        if (search) searchParams.set('search', search);

        const userGroup = document.getElementById('userGroupFilter').value;
        if (userGroup) searchParams.set('user_group', userGroup);

        const memberType = document.getElementById('memberTypeFilter').value;
        if (memberType) searchParams.set('member_type', memberType);

        const memberStatus = document.getElementById('memberStatusFilter').value;
        if (memberStatus) searchParams.set('member_status', memberStatus);

        const status = document.getElementById('statusFilter').value;
        if (status) searchParams.set('status', status);

        window.location.href = 'members.php?' + searchParams.toString();
    }

    // 编辑用户
    function editUser(userId) {
        showToast('编辑用户功能开发中...', 'info');
    }

    // 管理会员
    function manageMember(userId) {
        currentUserId = userId;
        document.getElementById('modalUserId').value = userId;
        document.getElementById('memberModal').style.display = 'flex';
    }

    // 关闭会员管理模态框
    function closeModal() {
        document.getElementById('memberModal').style.display = 'none';
        currentUserId = null;
    }

    // 保存会员更改
    async function saveMemberChanges() {
        const form = document.getElementById('memberForm');
        const formData = new FormData(form);

        try {
            const actions = [];

            // 更新用户分组
            const groupKey = formData.get('group_key');
            if (groupKey) {
                actions.push(fetch('members.php', {
                    method: 'POST',
                    headers: { 'X-Requested-With': 'XMLHttpRequest', 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=update_user_group&user_id=${currentUserId}&group_key=${groupKey}`
                }));
            }

            // 设置会员期限
            const expireDate = formData.get('expire_date');
            if (expireDate) {
                actions.push(fetch('members.php', {
                    method: 'POST',
                    headers: { 'X-Requested-With': 'XMLHttpRequest', 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=set_member_expiry&user_id=${currentUserId}&expire_date=${expireDate}`
                }));
            }

            // 调整积分
            const pointsChange = formData.get('points_change');
            const reason = formData.get('reason');
            if (pointsChange && reason) {
                actions.push(fetch('members.php', {
                    method: 'POST',
                    headers: { 'X-Requested-With': 'XMLHttpRequest', 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=adjust_points&user_id=${currentUserId}&points_change=${pointsChange}&reason=${encodeURIComponent(reason)}`
                }));
            }

            if (actions.length > 0) {
                await Promise.all(actions);
                showToast('会员信息更新成功', 'success');
                closeModal();
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('请至少填写一项要修改的信息', 'warning');
            }

        } catch (error) {
            showToast('操作失败：' + error.message, 'error');
        }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        setupFilters();

        // 模态框外部点击关闭
        document.getElementById('memberModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    });
    </script>
</body>
</html>
