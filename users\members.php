<?php
/**
 * 👑 会员管理页面
 * 
 * 功能：会员管理、分组管理、权限控制
 */

session_start();

// 引入认证中间件
require_once '../auth/auth.php';
require_once '../auth/SystemConfig.php';
require_once 'classes/MemberManager.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 要求用户登录
Auth::requireLogin('../login.php');

// 检查管理员权限
if (!Auth::hasRole(Auth::ROLE_ADMIN)) {
    header('Location: index.php?error=permission_denied');
    exit;
}

// 获取当前用户信息
$current_user = Auth::getCurrentUser();
$page_title = '会员管理';

// 初始化会员管理器
$memberManager = new MemberManager();

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_SERVER['HTTP_X_REQUESTED_WITH'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_user_group':
                $userId = intval($_POST['user_id']);
                $groupKey = $_POST['group_key'];
                $result = $memberManager->updateUserGroup_User($userId, $groupKey, $current_user['id']);
                echo json_encode(['success' => true, 'message' => '分组更新成功']);
                break;
                
            case 'set_member_expiry':
                $userId = intval($_POST['user_id']);
                $expireDate = $_POST['expire_date'] ?: null;
                $result = $memberManager->setMemberExpiry($userId, $expireDate, $current_user['id']);
                echo json_encode(['success' => true, 'message' => '会员期限设置成功']);
                break;
                
            case 'adjust_points':
                $userId = intval($_POST['user_id']);
                $pointsChange = intval($_POST['points_change']);
                $reason = $_POST['reason'];
                $result = $memberManager->adjustUserPoints($userId, $pointsChange, $reason, $current_user['id']);
                echo json_encode(['success' => true, 'message' => '积分调整成功']);
                break;
                
            default:
                throw new Exception('无效的操作');
        }
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// 获取筛选参数
$filters = [
    'search' => trim($_GET['search'] ?? ''),
    'user_group' => $_GET['user_group'] ?? '',
    'member_type' => $_GET['member_type'] ?? '',
    'status' => $_GET['status'] ?? '',
    'member_status' => $_GET['member_status'] ?? ''
];

$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 15;

// 获取用户列表
$userData = $memberManager->getUsersWithGroups($filters, $page, $perPage);
$users = $userData['users'];
$totalPages = $userData['pages'];

// 获取用户分组列表
$userGroups = $memberManager->getUserGroups();

// 获取统计信息
$stats = $memberManager->getMemberStats();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle($page_title); ?></title>
    
    <!-- 引入会员管理样式 -->
    <link rel="stylesheet" href="assets/css/members.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- 头部 -->
        <?php include 'includes/header.php'; ?>
        
        <!-- 主要内容 -->
        <main class="main-content">
            <div class="members-container">
                <!-- 页面标题和统计 -->
                <div class="members-header">
                    <div class="header-left">
                        <h1 class="page-title">
                            <svg class="title-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                            会员管理
                        </h1>
                        <p class="page-subtitle">管理用户分组、会员权限和积分系统</p>
                    </div>
                    
                    <div class="header-actions">
                        <a href="member-groups.php" class="action-btn secondary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            </svg>
                            分组管理
                        </a>
                    </div>
                </div>
                
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: #3b82f620;">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#3b82f6" stroke-width="2">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo number_format($stats['total_users']); ?></div>
                            <div class="stat-label">总用户数</div>
                        </div>
                    </div>
                    
                    <?php foreach ($stats['group_stats'] as $groupStat): ?>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: <?php echo $groupStat['group_color']; ?>20;">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="<?php echo $groupStat['group_color']; ?>" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <path d="M12 6v6l4 2"/>
                            </svg>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo number_format($groupStat['user_count']); ?></div>
                            <div class="stat-label"><?php echo htmlspecialchars($groupStat['group_name']); ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- 搜索和筛选 -->
                <div class="filters-section">
                    <form method="GET" class="filters-form">
                        <div class="search-box">
                            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="m21 21-4.35-4.35"/>
                            </svg>
                            <input type="text" name="search" placeholder="搜索用户名、邮箱或昵称..." 
                                   value="<?php echo htmlspecialchars($filters['search']); ?>" autocomplete="off">
                        </div>
                        
                        <div class="filter-selects">
                            <select name="user_group">
                                <option value="">所有分组</option>
                                <?php foreach ($userGroups as $group): ?>
                                <option value="<?php echo $group['group_key']; ?>" 
                                        <?php echo $filters['user_group'] === $group['group_key'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($group['group_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                            
                            <select name="member_type">
                                <option value="">所有类型</option>
                                <option value="regular" <?php echo $filters['member_type'] === 'regular' ? 'selected' : ''; ?>>普通用户</option>
                                <option value="vip" <?php echo $filters['member_type'] === 'vip' ? 'selected' : ''; ?>>VIP会员</option>
                                <option value="premium" <?php echo $filters['member_type'] === 'premium' ? 'selected' : ''; ?>>高级会员</option>
                            </select>
                            
                            <select name="member_status">
                                <option value="">会员状态</option>
                                <option value="active" <?php echo $filters['member_status'] === 'active' ? 'selected' : ''; ?>>有效</option>
                                <option value="expired" <?php echo $filters['member_status'] === 'expired' ? 'selected' : ''; ?>>已过期</option>
                            </select>
                            
                            <select name="status">
                                <option value="">账户状态</option>
                                <option value="active" <?php echo $filters['status'] === 'active' ? 'selected' : ''; ?>>正常</option>
                                <option value="inactive" <?php echo $filters['status'] === 'inactive' ? 'selected' : ''; ?>>未激活</option>
                                <option value="banned" <?php echo $filters['status'] === 'banned' ? 'selected' : ''; ?>>已封禁</option>
                            </select>
                        </div>
                        
                        <div class="filter-actions">
                            <button type="submit" class="btn primary">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"/>
                                    <path d="m21 21-4.35-4.35"/>
                                </svg>
                                搜索
                            </button>
                            <a href="members.php" class="btn secondary">重置</a>
                        </div>
                    </form>
                </div>
                
                <!-- 用户列表 -->
                <div class="members-table-container">
                    <div class="table-wrapper">
                        <table class="members-table">
                            <thead>
                                <tr>
                                    <th>用户信息</th>
                                    <th>分组/等级</th>
                                    <th>会员信息</th>
                                    <th>积分</th>
                                    <th>状态</th>
                                    <th>注册时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($users)): ?>
                                <tr>
                                    <td colspan="7" class="empty-state">
                                        <div class="empty-content">
                                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                                                <circle cx="11" cy="11" r="8"/>
                                                <path d="m21 21-4.35-4.35"/>
                                            </svg>
                                            <p>未找到符合条件的用户</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($users as $user): ?>
                                <tr class="user-row" data-user-id="<?php echo $user['id']; ?>">
                                    <td class="user-info">
                                        <div class="user-avatar">
                                            <?php if ($user['avatar']): ?>
                                            <img src="<?php echo htmlspecialchars($user['avatar']); ?>" alt="头像">
                                            <?php else: ?>
                                            <div class="avatar-placeholder">
                                                <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="user-details">
                                            <div class="user-name"><?php echo htmlspecialchars($user['nickname'] ?: $user['username']); ?></div>
                                            <div class="user-email"><?php echo htmlspecialchars($user['email']); ?></div>
                                            <div class="user-username">@<?php echo htmlspecialchars($user['username']); ?></div>
                                        </div>
                                    </td>
                                    
                                    <td class="group-info">
                                        <div class="group-badge" style="background: <?php echo $user['group_color']; ?>20; color: <?php echo $user['group_color']; ?>;">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <circle cx="12" cy="12" r="10"/>
                                            </svg>
                                            <?php echo htmlspecialchars($user['group_name']); ?>
                                        </div>
                                        <div class="member-level">等级 <?php echo $user['member_level']; ?></div>
                                    </td>
                                    
                                    <td class="member-info">
                                        <div class="member-type member-type-<?php echo $user['member_type']; ?>">
                                            <?php 
                                            $memberTypes = ['regular' => '普通用户', 'vip' => 'VIP会员', 'premium' => '高级会员'];
                                            echo $memberTypes[$user['member_type']];
                                            ?>
                                        </div>
                                        <?php if ($user['member_expire_date']): ?>
                                        <div class="member-expiry">
                                            到期：<?php echo date('Y-m-d', strtotime($user['member_expire_date'])); ?>
                                        </div>
                                        <?php endif; ?>
                                        <div class="member-status member-status-<?php echo strtolower($user['member_status']); ?>">
                                            <?php echo $user['member_status']; ?>
                                        </div>
                                    </td>
                                    
                                    <td class="points-info">
                                        <div class="points-available"><?php echo number_format($user['available_points']); ?></div>
                                        <div class="points-total">总计：<?php echo number_format($user['total_points']); ?></div>
                                    </td>
                                    
                                    <td class="status-info">
                                        <span class="status-badge status-<?php echo $user['status']; ?>">
                                            <?php 
                                            $statusLabels = ['active' => '正常', 'inactive' => '未激活', 'banned' => '已封禁'];
                                            echo $statusLabels[$user['status']];
                                            ?>
                                        </span>
                                        <?php if ($user['email_verified']): ?>
                                        <span class="verified-badge">已验证</span>
                                        <?php endif; ?>
                                    </td>
                                    
                                    <td class="date-info">
                                        <?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?>
                                    </td>
                                    
                                    <td class="actions">
                                        <div class="action-buttons">
                                            <button class="action-btn edit-user" data-user-id="<?php echo $user['id']; ?>" title="编辑用户">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                                </svg>
                                            </button>
                                            <button class="action-btn manage-member" data-user-id="<?php echo $user['id']; ?>" title="会员管理">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 分页 -->
                <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php
                    $queryParams = http_build_query(array_filter($filters));
                    $baseUrl = 'members.php' . ($queryParams ? '?' . $queryParams . '&' : '?');
                    ?>
                    
                    <?php if ($page > 1): ?>
                    <a href="<?php echo $baseUrl; ?>page=<?php echo $page - 1; ?>" class="page-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M15 18l-6-6 6-6"/>
                        </svg>
                        上一页
                    </a>
                    <?php endif; ?>
                    
                    <?php
                    $startPage = max(1, $page - 2);
                    $endPage = min($totalPages, $page + 2);
                    
                    for ($i = $startPage; $i <= $endPage; $i++):
                    ?>
                    <a href="<?php echo $baseUrl; ?>page=<?php echo $i; ?>" 
                       class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                    <a href="<?php echo $baseUrl; ?>page=<?php echo $page + 1; ?>" class="page-btn">
                        下一页
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 18l6-6-6-6"/>
                        </svg>
                    </a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </main>
        
        <!-- 页脚 -->
        <?php include 'includes/footer.php'; ?>
    </div>
    
    <!-- 会员管理模态框 -->
    <div id="memberModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>会员管理</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="memberForm">
                    <input type="hidden" id="modalUserId" name="user_id">
                    
                    <div class="form-group">
                        <label for="userGroup">用户分组</label>
                        <select id="userGroup" name="group_key">
                            <?php foreach ($userGroups as $group): ?>
                            <option value="<?php echo $group['group_key']; ?>">
                                <?php echo htmlspecialchars($group['group_name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="memberExpiry">会员到期时间</label>
                        <input type="datetime-local" id="memberExpiry" name="expire_date">
                        <small class="form-help">留空表示永久有效</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="pointsChange">积分调整</label>
                        <div class="points-input">
                            <input type="number" id="pointsChange" name="points_change" placeholder="输入正数增加，负数减少">
                            <input type="text" id="pointsReason" name="reason" placeholder="调整原因">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn secondary" onclick="closeModal()">取消</button>
                <button type="button" class="btn primary" onclick="saveMemberChanges()">保存</button>
            </div>
        </div>
    </div>
    
    <!-- 引入会员管理脚本 -->
    <script src="assets/js/members.js"></script>
</body>
</html>
