/**
 * 加载动画组件
 * 提供多种加载动画效果和灵活的配置选项
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class LoadingSpinner {
    constructor(options = {}) {
        // 获取保存的默认设置
        const savedSettings = this.getSavedSettings();

        // 配置选项
        this.options = {
            type: savedSettings.loadingAnimation || LoadingSpinner.defaultType || 'spinner', // 动画类型
            text: '加载中...',         // 加载文本
            subtext: '',              // 副文本
            overlay: true,            // 是否显示遮罩层
            backdrop: true,           // 是否显示背景模糊
            autoHide: false,          // 是否自动隐藏
            duration: 0,              // 自动隐藏时间（毫秒）
            zIndex: 9999,             // z-index层级
            className: '',            // 自定义CSS类名
            onShow: null,             // 显示回调
            onHide: null,             // 隐藏回调
            ...options
        };

        // 状态管理
        this.isVisible = false;
        this.element = null;
        this.autoHideTimer = null;

        // 动画类型映射
        this.animationTypes = {
            spinner: this.createSpinner,
            dots: this.createDots,
            wave: this.createWave,
            bounce: this.createBounce,
            ring: this.createRing,
            pulse: this.createPulse,
            progress: this.createProgress
        };

        // 自动加载样式
        this.loadStyles();
    }

    /**
     * 获取保存的设置
     */
    getSavedSettings() {
        try {
            const saved = localStorage.getItem('pageSettings');
            return saved ? JSON.parse(saved) : {};
        } catch (error) {
            console.warn('Failed to load settings:', error);
            return {};
        }
    }

    /**
     * 加载组件样式
     */
    loadStyles() {
        const existingLink = document.querySelector('link[href*="loading-spinner.css"]');
        if (!existingLink) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'assets/components/loading-spinner.css';
            document.head.appendChild(link);
        }
    }

    /**
     * 显示加载动画
     */
    show(options = {}) {
        // 合并选项
        const config = { ...this.options, ...options };
        
        // 如果已经显示，先隐藏
        if (this.isVisible) {
            this.hide();
        }

        // 创建加载元素
        this.element = this.createElement(config);
        document.body.appendChild(this.element);

        // 显示动画
        setTimeout(() => {
            this.element.classList.add('show');
            this.isVisible = true;

            // 触发显示回调
            if (config.onShow && typeof config.onShow === 'function') {
                config.onShow();
            }

            // 触发自定义事件
            this.dispatchEvent('loadingShow', { config });
        }, 10);

        // 自动隐藏
        if (config.autoHide && config.duration > 0) {
            this.autoHideTimer = setTimeout(() => {
                this.hide();
            }, config.duration);
        }

        return this;
    }

    /**
     * 隐藏加载动画
     */
    hide() {
        if (!this.isVisible || !this.element) {
            return this;
        }

        // 清除自动隐藏定时器
        if (this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
            this.autoHideTimer = null;
        }

        // 隐藏动画
        this.element.classList.remove('show');
        
        setTimeout(() => {
            if (this.element && this.element.parentNode) {
                this.element.parentNode.removeChild(this.element);
            }
            this.element = null;
            this.isVisible = false;

            // 触发隐藏回调
            if (this.options.onHide && typeof this.options.onHide === 'function') {
                this.options.onHide();
            }

            // 触发自定义事件
            this.dispatchEvent('loadingHide');
        }, 300);

        return this;
    }

    /**
     * 切换显示状态
     */
    toggle(options = {}) {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show(options);
        }
        return this;
    }

    /**
     * 更新加载文本
     */
    updateText(text, subtext = '') {
        if (!this.element) return this;

        const textElement = this.element.querySelector('.loading-text');
        const subtextElement = this.element.querySelector('.loading-subtext');

        if (textElement) {
            textElement.textContent = text;
        }

        if (subtextElement) {
            subtextElement.textContent = subtext;
            subtextElement.style.display = subtext ? 'block' : 'none';
        }

        return this;
    }

    /**
     * 创建加载元素
     */
    createElement(config) {
        const overlay = document.createElement('div');
        overlay.className = `loading-overlay ${config.className}`;
        overlay.style.zIndex = config.zIndex;

        if (!config.backdrop) {
            overlay.style.backdropFilter = 'none';
        }

        const container = document.createElement('div');
        container.className = 'loading-container';

        // 创建动画元素
        const animationElement = this.createAnimation(config.type);
        container.appendChild(animationElement);

        // 创建文本元素
        if (config.text) {
            const textElement = document.createElement('p');
            textElement.className = 'loading-text';
            textElement.textContent = config.text;
            container.appendChild(textElement);
        }

        if (config.subtext) {
            const subtextElement = document.createElement('p');
            subtextElement.className = 'loading-subtext';
            subtextElement.textContent = config.subtext;
            container.appendChild(subtextElement);
        }

        overlay.appendChild(container);

        // 点击遮罩层关闭（可选）
        if (config.clickToClose) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.hide();
                }
            });
        }

        return overlay;
    }

    /**
     * 创建动画元素
     */
    createAnimation(type) {
        const animationCreator = this.animationTypes[type];
        if (animationCreator) {
            return animationCreator.call(this);
        } else {
            console.warn(`Unknown animation type: ${type}. Using default spinner.`);
            return this.createSpinner();
        }
    }

    /**
     * 创建旋转圆环动画
     */
    createSpinner() {
        const spinner = document.createElement('div');
        spinner.className = 'loading-spinner';
        return spinner;
    }

    /**
     * 创建脉冲圆点动画
     */
    createDots() {
        const container = document.createElement('div');
        container.className = 'loading-dots';
        
        for (let i = 0; i < 3; i++) {
            const dot = document.createElement('div');
            dot.className = 'loading-dot';
            container.appendChild(dot);
        }
        
        return container;
    }

    /**
     * 创建波浪动画
     */
    createWave() {
        const container = document.createElement('div');
        container.className = 'loading-wave';
        
        for (let i = 0; i < 5; i++) {
            const bar = document.createElement('div');
            bar.className = 'loading-wave-bar';
            container.appendChild(bar);
        }
        
        return container;
    }

    /**
     * 创建弹跳球动画
     */
    createBounce() {
        const container = document.createElement('div');
        container.className = 'loading-bounce';
        
        for (let i = 0; i < 3; i++) {
            const ball = document.createElement('div');
            ball.className = 'loading-bounce-ball';
            container.appendChild(ball);
        }
        
        return container;
    }

    /**
     * 创建渐变圆环动画
     */
    createRing() {
        const ring = document.createElement('div');
        ring.className = 'loading-ring';
        return ring;
    }

    /**
     * 创建脉冲圆环动画
     */
    createPulse() {
        const pulse = document.createElement('div');
        pulse.className = 'loading-pulse';
        return pulse;
    }

    /**
     * 创建进度条动画
     */
    createProgress() {
        const container = document.createElement('div');
        container.className = 'loading-progress';
        
        const bar = document.createElement('div');
        bar.className = 'loading-progress-bar';
        container.appendChild(bar);
        
        return container;
    }

    /**
     * 触发自定义事件
     */
    dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(`loadingSpinner:${eventName}`, {
            detail: {
                instance: this,
                ...detail
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 获取当前状态
     */
    getState() {
        return {
            isVisible: this.isVisible,
            options: this.options
        };
    }

    /**
     * 销毁组件
     */
    destroy() {
        this.hide();
        
        // 清除定时器
        if (this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
            this.autoHideTimer = null;
        }
    }
}

// 默认动画类型
LoadingSpinner.defaultType = 'spinner';

// 静态方法：获取保存的设置
LoadingSpinner.getSavedSettings = function() {
    try {
        const saved = localStorage.getItem('pageSettings');
        return saved ? JSON.parse(saved) : {};
    } catch (error) {
        console.warn('Failed to load settings:', error);
        return {};
    }
};

// 静态方法：快速显示加载动画
LoadingSpinner.show = function(options = {}) {
    // 如果没有指定类型，使用保存的默认类型
    if (!options.type) {
        const savedSettings = LoadingSpinner.getSavedSettings();
        options.type = savedSettings.loadingAnimation || LoadingSpinner.defaultType;
    }

    const instance = new LoadingSpinner(options);
    instance.show();
    return instance;
};

// 静态方法：快速隐藏所有加载动画
LoadingSpinner.hideAll = function() {
    const overlays = document.querySelectorAll('.loading-overlay');
    overlays.forEach(overlay => {
        overlay.classList.remove('show');
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    });
};

// 静态方法：更新默认类型
LoadingSpinner.setDefaultType = function(type) {
    LoadingSpinner.defaultType = type;
};

// 导出组件类
window.LoadingSpinner = LoadingSpinner;
