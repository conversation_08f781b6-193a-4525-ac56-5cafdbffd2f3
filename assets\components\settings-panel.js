/**
 * 设置面板组件
 * 提供页面自定义设置功能，包括加载动画样式选择等
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class SettingsPanel {
    constructor(options = {}) {
        // 配置选项
        this.options = {
            position: 'bottom-right',  // 位置：bottom-right, bottom-left
            autoInit: true,           // 是否自动初始化
            storageManager: null,     // 存储管理器
            toastManager: null,       // Toast管理器
            ...options
        };

        // 状态管理
        this.isVisible = false;

        // 管理器引用
        this.storageManager = this.options.storageManager || window.storageManager;
        this.toastManager = this.options.toastManager || window.toastManager;

        // 从存储管理器加载设置
        this.settings = this.loadSettings();

        // DOM元素
        this.fab = null;
        this.panel = null;

        // 设置项配置
        this.settingsConfig = {
            loadingAnimation: {
                title: '加载动画',
                desc: '选择页面加载动画样式',
                icon: this.getLoadingIcon(),
                type: 'selector',
                options: [
                    { value: 'spinner', name: '旋转圆环', preview: 'mini-spinner' },
                    { value: 'dots', name: '脉冲圆点', preview: 'mini-dots' },
                    { value: 'wave', name: '波浪动画', preview: 'mini-wave' },
                    { value: 'bounce', name: '弹跳球', preview: 'mini-bounce' },
                    { value: 'ring', name: '渐变圆环', preview: 'mini-ring' },
                    { value: 'pulse', name: '脉冲圆环', preview: 'mini-pulse' },
                    { value: 'progress', name: '进度条', preview: 'mini-progress' }
                ],
                default: 'spinner'
            },
            toastStyle: {
                title: 'Toast通知样式',
                desc: '选择消息通知的显示样式',
                icon: this.getToastIcon(),
                type: 'selector',
                options: [
                    { value: 'modern', name: '现代风格', preview: 'toast-modern' },
                    { value: 'minimal', name: '简约风格', preview: 'toast-minimal' },
                    { value: 'rounded', name: '圆角风格', preview: 'toast-rounded' },
                    { value: 'flat', name: '扁平风格', preview: 'toast-flat' },
                    { value: 'gradient', name: '渐变风格', preview: 'toast-gradient' },
                    { value: 'shadow', name: '阴影风格', preview: 'toast-shadow' }
                ],
                default: 'modern'
            },
            modalAnimation: {
                title: '模态弹窗动画',
                desc: '选择弹窗的进入和退出动画效果',
                icon: this.getModalAnimationIcon(),
                type: 'selector',
                options: [
                    { value: 'fade', name: '淡入淡出', preview: 'modal-fade' },
                    { value: 'slide', name: '滑动进入', preview: 'modal-slide' },
                    { value: 'zoom', name: '缩放效果', preview: 'modal-zoom' },
                    { value: 'bounce', name: '弹跳效果', preview: 'modal-bounce' }
                ],
                default: 'fade'
            },
            modalStyle: {
                title: '模态弹窗样式',
                desc: '选择弹窗的外观和视觉样式',
                icon: this.getModalStyleIcon(),
                type: 'selector',
                options: [
                    { value: 'modern', name: '现代风格', preview: 'modal-style-modern' },
                    { value: 'minimal', name: '简约风格', preview: 'modal-style-minimal' },
                    { value: 'rounded', name: '圆角风格', preview: 'modal-style-rounded' },
                    { value: 'shadow', name: '阴影风格', preview: 'modal-style-shadow' },
                    { value: 'glass', name: '毛玻璃风格', preview: 'modal-style-glass' },
                    { value: 'flat', name: '扁平风格', preview: 'modal-style-flat' }
                ],
                default: 'modern'
            }
        };

        // 如果启用自动初始化，则立即初始化
        if (this.options.autoInit) {
            this.init();
        }
    }

    /**
     * 初始化设置面板
     */
    init() {
        // 创建设置面板
        this.createSettingsPanel();

        // 绑定事件
        this.bindEvents();

        // 加载样式
        this.loadStyles();

        // 延迟应用已保存的设置，确保其他组件已初始化
        setTimeout(() => {
            this.applySettings();
        }, 200);
    }

    /**
     * 加载组件样式
     */
    loadStyles() {
        const existingLink = document.querySelector('link[href*="settings-panel.css"]');
        if (!existingLink) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'assets/components/settings-panel.css';
            document.head.appendChild(link);
        }
    }

    /**
     * 创建设置面板DOM结构
     */
    createSettingsPanel() {
        // 创建悬浮按钮
        this.fab = document.createElement('button');
        this.fab.className = 'settings-fab';
        this.fab.innerHTML = `
            <div class="settings-icon">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="3"/>
                    <path d="M12 1v6"/>
                    <path d="M12 17v6"/>
                    <path d="m4.22 4.22 1.42 1.42"/>
                    <path d="m18.36 18.36 1.42 1.42"/>
                    <path d="M1 12h6"/>
                    <path d="M17 12h6"/>
                    <path d="m4.22 19.78 1.42-1.42"/>
                    <path d="m18.36 5.64 1.42-1.42"/>
                </svg>
            </div>
        `;
        this.fab.setAttribute('aria-label', '打开设置面板');
        this.fab.setAttribute('title', '页面设置');

        // 创建设置面板
        this.panel = document.createElement('div');
        this.panel.className = 'settings-panel';
        this.panel.innerHTML = this.createPanelHTML();

        // 添加到页面
        document.body.appendChild(this.fab);
        document.body.appendChild(this.panel);
    }

    /**
     * 创建面板HTML内容
     */
    createPanelHTML() {
        return `
            <div class="settings-header">
                <h3 class="settings-title">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M12 1v6"/>
                        <path d="M12 17v6"/>
                        <path d="m4.22 4.22 1.42 1.42"/>
                        <path d="m18.36 18.36 1.42 1.42"/>
                        <path d="M1 12h6"/>
                        <path d="M17 12h6"/>
                        <path d="m4.22 19.78 1.42-1.42"/>
                        <path d="m18.36 5.64 1.42-1.42"/>
                    </svg>
                    页面设置
                </h3>
                <button class="settings-close" aria-label="关闭设置面板">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="settings-body">
                ${this.createSettingsItems()}
            </div>
        `;
    }

    /**
     * 创建设置项
     */
    createSettingsItems() {
        let html = '';
        
        for (const [key, config] of Object.entries(this.settingsConfig)) {
            html += `
                <div class="settings-item" data-setting="${key}">
                    <div class="settings-item-header">
                        <div class="settings-item-icon">${config.icon}</div>
                        <div class="settings-item-info">
                            <div class="settings-item-title">${config.title}</div>
                            <div class="settings-item-desc">${config.desc}</div>
                        </div>
                        <div class="settings-item-arrow">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9,18 15,12 9,6"/>
                            </svg>
                        </div>
                    </div>
                    <div class="settings-item-content">
                        ${this.createSettingContent(key, config)}
                    </div>
                </div>
            `;
        }
        
        return html;
    }

    /**
     * 创建设置项内容
     */
    createSettingContent(key, config) {
        if (config.type === 'selector') {
            return this.createSelectorContent(key, config);
        }
        return '';
    }

    /**
     * 创建选择器内容
     */
    createSelectorContent(key, config) {
        const currentValue = this.settings[key] || config.default;
        let selectorClass, optionClass;

        switch (key) {
            case 'toastStyle':
                selectorClass = 'toast-selector';
                optionClass = 'toast-option';
                break;
            case 'modalAnimation':
                selectorClass = 'modal-animation-selector';
                optionClass = 'modal-animation-option';
                break;
            case 'modalStyle':
                selectorClass = 'modal-style-selector';
                optionClass = 'modal-style-option';
                break;
            default:
                selectorClass = 'loading-selector';
                optionClass = 'loading-option';
        }

        let html = `<div class="${selectorClass}">`;

        config.options.forEach(option => {
            const isSelected = option.value === currentValue;
            html += `
                <div class="${optionClass} ${isSelected ? 'selected' : ''}" data-value="${option.value}" data-setting="${key}">
                    <div class="loading-option-preview">
                        ${this.createPreviewHTML(option.preview)}
                    </div>
                    <div class="loading-option-name">${option.name}</div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    /**
     * 创建预览HTML
     */
    createPreviewHTML(previewType) {
        const previews = {
            // 加载动画预览
            'mini-spinner': '<div class="mini-spinner"></div>',
            'mini-dots': '<div class="mini-dots"><div class="mini-dot"></div><div class="mini-dot"></div><div class="mini-dot"></div></div>',
            'mini-wave': '<div class="mini-wave"><div class="mini-wave-bar"></div><div class="mini-wave-bar"></div><div class="mini-wave-bar"></div><div class="mini-wave-bar"></div></div>',
            'mini-bounce': '<div class="mini-bounce"><div class="mini-bounce-ball"></div><div class="mini-bounce-ball"></div><div class="mini-bounce-ball"></div></div>',
            'mini-ring': '<div class="mini-ring"></div>',
            'mini-pulse': '<div class="mini-pulse"></div>',
            'mini-progress': '<div class="mini-progress"><div class="mini-progress-bar"></div></div>',

            // Toast样式预览
            'toast-modern': '<div class="mini-toast toast-modern-preview"><div class="mini-toast-icon">✓</div><div class="mini-toast-text">消息</div></div>',
            'toast-minimal': '<div class="mini-toast toast-minimal-preview"><div class="mini-toast-text">消息</div></div>',
            'toast-rounded': '<div class="mini-toast toast-rounded-preview"><div class="mini-toast-icon">✓</div><div class="mini-toast-text">消息</div></div>',
            'toast-flat': '<div class="mini-toast toast-flat-preview"><div class="mini-toast-text">消息</div></div>',
            'toast-gradient': '<div class="mini-toast toast-gradient-preview"><div class="mini-toast-icon">✓</div><div class="mini-toast-text">消息</div></div>',
            'toast-shadow': '<div class="mini-toast toast-shadow-preview"><div class="mini-toast-icon">✓</div><div class="mini-toast-text">消息</div></div>',

            // 模态弹窗动画预览
            'modal-fade': '<div class="mini-modal modal-fade-preview"><div class="mini-modal-content"><div class="mini-modal-header"></div><div class="mini-modal-body"></div></div></div>',
            'modal-slide': '<div class="mini-modal modal-slide-preview"><div class="mini-modal-content"><div class="mini-modal-header"></div><div class="mini-modal-body"></div></div></div>',
            'modal-zoom': '<div class="mini-modal modal-zoom-preview"><div class="mini-modal-content"><div class="mini-modal-header"></div><div class="mini-modal-body"></div></div></div>',
            'modal-bounce': '<div class="mini-modal modal-bounce-preview"><div class="mini-modal-content"><div class="mini-modal-header"></div><div class="mini-modal-body"></div></div></div>',

            // 模态弹窗样式预览
            'modal-style-modern': '<div class="mini-modal-style modern-style-preview"><div class="mini-modal-header-style"></div><div class="mini-modal-body-style"></div></div>',
            'modal-style-minimal': '<div class="mini-modal-style minimal-style-preview"><div class="mini-modal-header-style"></div><div class="mini-modal-body-style"></div></div>',
            'modal-style-rounded': '<div class="mini-modal-style rounded-style-preview"><div class="mini-modal-header-style"></div><div class="mini-modal-body-style"></div></div>',
            'modal-style-shadow': '<div class="mini-modal-style shadow-style-preview"><div class="mini-modal-header-style"></div><div class="mini-modal-body-style"></div></div>',
            'modal-style-glass': '<div class="mini-modal-style glass-style-preview"><div class="mini-modal-header-style"></div><div class="mini-modal-body-style"></div></div>',
            'modal-style-flat': '<div class="mini-modal-style flat-style-preview"><div class="mini-modal-header-style"></div><div class="mini-modal-body-style"></div></div>'
        };

        return previews[previewType] || '';
    }

    /**
     * 获取加载图标
     */
    getLoadingIcon() {
        return `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 12a9 9 0 11-6.219-8.56"/>
            </svg>
        `;
    }

    /**
     * 获取Toast图标
     */
    getToastIcon() {
        return `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                <path d="M8 9h8"/>
                <path d="M8 13h6"/>
            </svg>
        `;
    }

    /**
     * 获取模态弹窗动画图标
     */
    getModalAnimationIcon() {
        return `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2z"/>
                <polyline points="8,10 12,14 16,10"/>
            </svg>
        `;
    }

    /**
     * 获取模态弹窗样式图标
     */
    getModalStyleIcon() {
        return `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <path d="M9 9h6v6H9z"/>
                <path d="M16 3v4"/>
                <path d="M21 8h-4"/>
            </svg>
        `;
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 悬浮按钮点击
        this.fab.addEventListener('click', (e) => {
            e.stopPropagation();
            this.togglePanel();
        });

        // 关闭按钮点击
        this.panel.querySelector('.settings-close').addEventListener('click', () => {
            this.hidePanel();
        });

        // 设置项点击
        this.panel.addEventListener('click', (e) => {
            e.stopPropagation();
            
            // 设置项头部点击
            const itemHeader = e.target.closest('.settings-item-header');
            if (itemHeader) {
                const item = itemHeader.closest('.settings-item');
                this.toggleSettingItem(item);
                return;
            }
            
            // 加载动画选项点击
            const loadingOption = e.target.closest('.loading-option');
            if (loadingOption) {
                this.selectLoadingAnimation(loadingOption);
                return;
            }

            // Toast样式选项点击
            const toastOption = e.target.closest('.toast-option');
            if (toastOption) {
                this.selectToastStyle(toastOption);
                return;
            }

            // 模态弹窗动画选项点击
            const modalAnimationOption = e.target.closest('.modal-animation-option');
            if (modalAnimationOption) {
                this.selectModalAnimation(modalAnimationOption);
                return;
            }

            // 模态弹窗样式选项点击
            const modalStyleOption = e.target.closest('.modal-style-option');
            if (modalStyleOption) {
                this.selectModalStyle(modalStyleOption);
                return;
            }
        });

        // 点击外部关闭面板
        document.addEventListener('click', () => {
            this.hidePanel();
        });

        // 阻止面板内部点击冒泡
        this.panel.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    /**
     * 切换面板显示状态
     */
    togglePanel() {
        if (this.isVisible) {
            this.hidePanel();
        } else {
            this.showPanel();
        }
    }

    /**
     * 显示面板
     */
    showPanel() {
        this.panel.classList.add('show');
        this.fab.classList.add('active');
        this.isVisible = true;
        
        // 触发事件
        this.dispatchEvent('panelShow');
    }

    /**
     * 隐藏面板
     */
    hidePanel() {
        this.panel.classList.remove('show');
        this.fab.classList.remove('active');
        this.isVisible = false;
        
        // 收起所有展开的设置项
        const expandedItems = this.panel.querySelectorAll('.settings-item.expanded');
        expandedItems.forEach(item => {
            item.classList.remove('expanded');
        });
        
        // 触发事件
        this.dispatchEvent('panelHide');
    }

    /**
     * 切换设置项展开状态
     */
    toggleSettingItem(item) {
        const isExpanded = item.classList.contains('expanded');
        
        // 收起其他所有项
        const allItems = this.panel.querySelectorAll('.settings-item');
        allItems.forEach(otherItem => {
            if (otherItem !== item) {
                otherItem.classList.remove('expanded');
            }
        });
        
        // 切换当前项
        item.classList.toggle('expanded', !isExpanded);
    }

    /**
     * 选择加载动画
     */
    selectLoadingAnimation(option) {
        const value = option.dataset.value;
        const settingKey = 'loadingAnimation';
        
        // 更新UI
        const selector = option.closest('.loading-selector');
        const allOptions = selector.querySelectorAll('.loading-option');
        allOptions.forEach(opt => opt.classList.remove('selected'));
        option.classList.add('selected');
        
        // 保存设置
        this.updateSetting(settingKey, value);
        
        // 应用设置
        this.applyLoadingAnimationSetting(value);
        
        // 显示预览
        this.showLoadingPreview(value);
    }

    /**
     * 选择Toast样式
     */
    selectToastStyle(option) {
        const value = option.dataset.value;
        const settingKey = 'toastStyle';

        // 更新UI
        const selector = option.closest('.toast-selector');
        const allOptions = selector.querySelectorAll('.toast-option');
        allOptions.forEach(opt => opt.classList.remove('selected'));
        option.classList.add('selected');

        // 保存设置
        this.updateSetting(settingKey, value);

        // 应用设置
        this.applyToastStyleSetting(value);

        // 显示预览
        this.showToastPreview(value);
    }

    /**
     * 选择模态弹窗动画
     */
    selectModalAnimation(option) {
        const value = option.dataset.value;
        const settingKey = 'modalAnimation';

        // 更新UI
        const selector = option.closest('.modal-animation-selector');
        const allOptions = selector.querySelectorAll('.modal-animation-option');
        allOptions.forEach(opt => opt.classList.remove('selected'));
        option.classList.add('selected');

        // 保存设置
        this.updateSetting(settingKey, value);

        // 应用设置
        this.applyModalAnimationSetting(value);

        // 显示预览
        this.showModalAnimationPreview(value);
    }

    /**
     * 选择模态弹窗样式
     */
    selectModalStyle(option) {
        const value = option.dataset.value;
        const settingKey = 'modalStyle';

        // 更新UI
        const selector = option.closest('.modal-style-selector');
        const allOptions = selector.querySelectorAll('.modal-style-option');
        allOptions.forEach(opt => opt.classList.remove('selected'));
        option.classList.add('selected');

        // 保存设置
        this.updateSetting(settingKey, value);

        // 应用设置
        this.applyModalStyleSetting(value);

        // 显示预览
        this.showModalStylePreview(value);
    }

    /**
     * 显示加载动画预览
     */
    showLoadingPreview(type) {
        if (window.LoadingSpinner) {
            LoadingSpinner.show({
                type: type,
                text: `${this.getAnimationName(type)}预览`,
                subtext: '设置已保存并应用',
                autoHide: true,
                duration: 2000
            });
        }
    }

    /**
     * 显示Toast样式预览
     */
    showToastPreview(style) {
        if (window.toastManager && typeof window.toastManager.show === 'function') {
            // 显示不同类型的Toast预览
            setTimeout(() => {
                window.toastManager.show(`${this.getToastStyleName(style)}预览`, 'success', 3000);
            }, 100);
            setTimeout(() => {
                window.toastManager.show('这是信息提示', 'info', 3000);
            }, 800);
            setTimeout(() => {
                window.toastManager.show('这是警告提示', 'warning', 3000);
            }, 1500);
        } else {
            console.warn('Toast管理器未找到，无法显示预览');
            // 使用简单的alert作为备选方案
            alert(`${this.getToastStyleName(style)}样式已设置`);
        }
    }

    /**
     * 显示模态弹窗动画预览
     */
    showModalAnimationPreview(animation) {
        if (window.showModal && typeof window.showModal.info === 'function') {
            // 显示模态弹窗动画预览
            window.showModal.info(
                `${this.getModalAnimationName(animation)}预览`,
                `这是${this.getModalAnimationName(animation)}的模态弹窗动画效果预览。<br><br>包含进入和退出动画效果。<br><br>设置已保存并应用到所有弹窗。`,
                {
                    animation: animation,
                    maxWidth: 'large'
                }
            );
        } else {
            console.warn('模态弹窗管理器未找到，无法显示预览');
            alert(`${this.getModalAnimationName(animation)}动画已设置`);
        }
    }

    /**
     * 显示模态弹窗样式预览
     */
    showModalStylePreview(style) {
        if (window.showModal && typeof window.showModal.info === 'function') {
            // 获取当前动画设置
            const currentAnimation = this.settings.modalAnimation || 'fade';

            // 显示模态弹窗样式预览
            window.showModal.info(
                `${this.getModalStyleName(style)}预览`,
                `这是${this.getModalStyleName(style)}的模态弹窗样式预览。<br><br>包含圆角、阴影、背景等视觉效果。<br><br>设置已保存并应用到所有弹窗。`,
                {
                    animation: currentAnimation,
                    borderRadius: this.getStyleBorderRadius(style),
                    shadow: this.getStyleShadow(style),
                    backdrop: this.getStyleBackdrop(style),
                    maxWidth: 'large'
                }
            );
        } else {
            console.warn('模态弹窗管理器未找到，无法显示预览');
            alert(`${this.getModalStyleName(style)}样式已设置`);
        }
    }

    /**
     * 获取动画名称
     */
    getAnimationName(type) {
        const names = {
            spinner: '旋转圆环',
            dots: '脉冲圆点',
            wave: '波浪动画',
            bounce: '弹跳球',
            ring: '渐变圆环',
            pulse: '脉冲圆环',
            progress: '进度条'
        };
        return names[type] || '加载动画';
    }

    /**
     * 获取Toast样式名称
     */
    getToastStyleName(style) {
        const names = {
            modern: '现代风格',
            minimal: '简约风格',
            rounded: '圆角风格',
            flat: '扁平风格',
            gradient: '渐变风格',
            shadow: '阴影风格'
        };
        return names[style] || 'Toast样式';
    }

    /**
     * 获取模态弹窗动画名称
     */
    getModalAnimationName(animation) {
        const names = {
            fade: '淡入淡出',
            slide: '滑动进入',
            zoom: '缩放效果',
            bounce: '弹跳效果'
        };
        return names[animation] || '模态弹窗动画';
    }

    /**
     * 获取模态弹窗样式名称
     */
    getModalStyleName(style) {
        const names = {
            modern: '现代风格',
            minimal: '简约风格',
            rounded: '圆角风格',
            shadow: '阴影风格',
            glass: '毛玻璃风格',
            flat: '扁平风格'
        };
        return names[style] || '模态弹窗样式';
    }

    /**
     * 获取样式对应的圆角设置
     */
    getStyleBorderRadius(style) {
        const radiusMap = {
            modern: 'medium',
            minimal: 'small',
            rounded: 'large',
            shadow: 'medium',
            glass: 'large',
            flat: 'small'
        };
        return radiusMap[style] || 'medium';
    }

    /**
     * 获取样式对应的阴影设置
     */
    getStyleShadow(style) {
        const shadowMap = {
            modern: 'large',
            minimal: 'small',
            rounded: 'medium',
            shadow: 'xl',
            glass: 'large',
            flat: 'small'
        };
        return shadowMap[style] || 'large';
    }

    /**
     * 获取样式对应的背景设置
     */
    getStyleBackdrop(style) {
        const backdropMap = {
            modern: 'blur',
            minimal: 'light',
            rounded: 'blur',
            shadow: 'dark',
            glass: 'blur',
            flat: 'dark'
        };
        return backdropMap[style] || 'blur';
    }

    /**
     * 应用加载动画设置
     */
    applyLoadingAnimationSetting(type) {
        // 更新全局加载动画实例的默认类型
        if (window.loadingSpinner) {
            window.loadingSpinner.options.type = type;
        }

        // 更新LoadingSpinner类的默认配置
        if (window.LoadingSpinner) {
            window.LoadingSpinner.setDefaultType(type);
        }

        // 触发设置变更事件
        this.dispatchEvent('settingChanged', {
            key: 'loadingAnimation',
            value: type
        });

        console.log(`✅ 加载动画默认类型已设置为: ${type}`);
    }

    /**
     * 应用Toast样式设置
     */
    applyToastStyleSetting(style) {
        // 使用存储管理器保存设置
        if (this.storageManager) {
            this.storageManager.setToastStyle(style);
        }

        // 更新全局Toast管理器的默认样式
        if (this.toastManager && typeof this.toastManager.setDefaultStyle === 'function') {
            this.toastManager.setDefaultStyle(style);
        } else if (window.toastManager && typeof window.toastManager.setDefaultStyle === 'function') {
            window.toastManager.setDefaultStyle(style);
        } else {
            console.warn('Toast管理器未找到或不支持setDefaultStyle方法');
        }

        // 更新页面Toast样式类
        document.documentElement.setAttribute('data-toast-style', style);

        // 触发设置变更事件
        this.dispatchEvent('settingChanged', {
            key: 'toastStyle',
            value: style
        });

        console.log(`✅ Toast样式已设置为: ${style}`);
    }

    /**
     * 应用模态弹窗动画设置
     */
    applyModalAnimationSetting(animation) {
        // 更新全局模态弹窗管理器的默认动画
        if (window.modalManager && typeof window.modalManager.updatePreferences === 'function') {
            const animationPrefs = {
                animation: animation
            };
            window.modalManager.updatePreferences(animationPrefs);
            console.log('🎨 已更新模态弹窗管理器动画偏好:', animationPrefs);
        } else {
            console.warn('模态弹窗管理器未找到或不支持updatePreferences方法');
        }

        // 更新页面模态弹窗动画类
        document.documentElement.setAttribute('data-modal-animation', animation);

        // 触发设置变更事件
        this.dispatchEvent('settingChanged', {
            key: 'modalAnimation',
            value: animation
        });

        console.log(`✅ 模态弹窗动画已设置为: ${animation}`);
    }

    /**
     * 应用模态弹窗样式设置
     */
    applyModalStyleSetting(style) {
        // 更新全局模态弹窗管理器的默认样式
        if (window.modalManager && typeof window.modalManager.updatePreferences === 'function') {
            const stylePrefs = {
                borderRadius: this.getStyleBorderRadius(style),
                shadow: this.getStyleShadow(style),
                backdrop: this.getStyleBackdrop(style),
                style: style
            };
            window.modalManager.updatePreferences(stylePrefs);
            console.log('🎨 已更新模态弹窗管理器样式偏好:', stylePrefs);
        } else {
            console.warn('模态弹窗管理器未找到或不支持updatePreferences方法');
        }

        // 更新页面模态弹窗样式类
        document.documentElement.setAttribute('data-modal-style', style);

        // 触发设置变更事件
        this.dispatchEvent('settingChanged', {
            key: 'modalStyle',
            value: style
        });

        console.log(`✅ 模态弹窗样式已设置为: ${style}`);
    }

    /**
     * 更新设置
     */
    updateSetting(key, value) {
        this.settings[key] = value;

        // 使用统一存储管理器保存设置
        if (this.storageManager) {
            if (key === 'loadingAnimation') {
                this.storageManager.setLoadingStyle(value);
            } else if (key === 'toastStyle') {
                this.storageManager.setToastStyle(value);
            } else if (key === 'modalAnimation') {
                const currentPrefs = this.storageManager.get(CONFIG.storage.modalPreferences, {});
                this.storageManager.set(CONFIG.storage.modalPreferences, { ...currentPrefs, animation: value });
            } else if (key === 'modalStyle') {
                const currentPrefs = this.storageManager.get(CONFIG.storage.modalPreferences, {});
                this.storageManager.set(CONFIG.storage.modalPreferences, {
                    ...currentPrefs,
                    borderRadius: this.getStyleBorderRadius(value),
                    shadow: this.getStyleShadow(value),
                    backdrop: this.getStyleBackdrop(value),
                    style: value
                });
            }
        } else {
            // 回退到本地保存
            this.saveSettings();
        }
    }

    /**
     * 应用所有设置
     */
    applySettings() {
        // 应用加载动画设置
        const loadingType = this.settings.loadingAnimation || 'spinner';
        this.applyLoadingAnimationSetting(loadingType);

        // 应用Toast样式设置
        const toastStyle = this.settings.toastStyle || 'modern';
        this.applyToastStyleSetting(toastStyle);

        // 应用模态弹窗动画设置
        const modalAnimation = this.settings.modalAnimation || 'fade';
        this.applyModalAnimationSetting(modalAnimation);

        // 应用模态弹窗样式设置
        const modalStyle = this.settings.modalStyle || 'modern';
        this.applyModalStyleSetting(modalStyle);

        // 更新UI显示当前选中的设置
        this.updateUIFromSettings();
    }

    /**
     * 根据保存的设置更新UI
     */
    updateUIFromSettings() {
        // 等待DOM创建完成后更新UI
        setTimeout(() => {
            // 更新加载动画选择器的UI
            const loadingType = this.settings.loadingAnimation || 'spinner';
            const loadingSelector = this.panel.querySelector('.loading-selector');
            if (loadingSelector) {
                const allLoadingOptions = loadingSelector.querySelectorAll('.loading-option');
                allLoadingOptions.forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.value === loadingType) {
                        option.classList.add('selected');
                    }
                });
            }

            // 更新Toast样式选择器的UI
            const toastStyle = this.settings.toastStyle || 'modern';
            const toastSelector = this.panel.querySelector('.toast-selector');
            if (toastSelector) {
                const allToastOptions = toastSelector.querySelectorAll('.toast-option');
                allToastOptions.forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.value === toastStyle) {
                        option.classList.add('selected');
                    }
                });
            }

            // 更新模态弹窗动画选择器的UI
            const modalAnimation = this.settings.modalAnimation || 'fade';
            const modalAnimationSelector = this.panel.querySelector('.modal-animation-selector');
            if (modalAnimationSelector) {
                const allModalAnimationOptions = modalAnimationSelector.querySelectorAll('.modal-animation-option');
                allModalAnimationOptions.forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.value === modalAnimation) {
                        option.classList.add('selected');
                    }
                });
            }

            // 更新模态弹窗样式选择器的UI
            const modalStyle = this.settings.modalStyle || 'modern';
            const modalStyleSelector = this.panel.querySelector('.modal-style-selector');
            if (modalStyleSelector) {
                const allModalStyleOptions = modalStyleSelector.querySelectorAll('.modal-style-option');
                allModalStyleOptions.forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.value === modalStyle) {
                        option.classList.add('selected');
                    }
                });
            }
        }, 100);
    }

    /**
     * 加载设置
     */
    loadSettings() {
        if (this.storageManager) {
            // 从统一存储管理器加载设置
            const modalPrefs = this.storageManager.get(CONFIG.storage.modalPreferences, {
                animation: 'fade',
                style: 'modern'
            });
            return {
                loadingAnimation: this.storageManager.getLoadingStyle(),
                toastStyle: this.storageManager.getToastStyle(),
                modalAnimation: modalPrefs.animation || 'fade',
                modalStyle: modalPrefs.style || 'modern'
            };
        } else {
            // 回退到本地存储
            try {
                const saved = localStorage.getItem('pageSettings');
                return saved ? JSON.parse(saved) : {};
            } catch (error) {
                console.warn('Failed to load settings:', error);
                return {};
            }
        }
    }

    /**
     * 保存设置
     */
    saveSettings() {
        if (this.storageManager) {
            // 使用统一存储管理器保存设置
            if (this.settings.loadingAnimation) {
                this.storageManager.setLoadingStyle(this.settings.loadingAnimation);
            }
            if (this.settings.toastStyle) {
                this.storageManager.setToastStyle(this.settings.toastStyle);
            }
            if (this.settings.modalAnimation || this.settings.modalStyle) {
                const currentPrefs = this.storageManager.get(CONFIG.storage.modalPreferences, {});
                const newPrefs = { ...currentPrefs };

                if (this.settings.modalAnimation) {
                    newPrefs.animation = this.settings.modalAnimation;
                }
                if (this.settings.modalStyle) {
                    newPrefs.style = this.settings.modalStyle;
                    newPrefs.borderRadius = this.getStyleBorderRadius(this.settings.modalStyle);
                    newPrefs.shadow = this.getStyleShadow(this.settings.modalStyle);
                    newPrefs.backdrop = this.getStyleBackdrop(this.settings.modalStyle);
                }

                this.storageManager.set(CONFIG.storage.modalPreferences, newPrefs);
            }
        } else {
            // 回退到本地存储
            try {
                localStorage.setItem('pageSettings', JSON.stringify(this.settings));
            } catch (error) {
                console.warn('Failed to save settings:', error);
            }
        }
    }

    /**
     * 触发自定义事件
     */
    dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(`settingsPanel:${eventName}`, {
            detail: {
                instance: this,
                ...detail
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 获取当前设置
     */
    getSettings() {
        return { ...this.settings };
    }

    /**
     * 重置设置
     */
    resetSettings() {
        this.settings = {};
        this.saveSettings();
        this.applySettings();
        
        // 重新创建面板内容
        this.panel.innerHTML = this.createPanelHTML();
        this.bindPanelEvents();
        
        // 触发事件
        this.dispatchEvent('settingsReset');
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.fab && this.fab.parentNode) {
            this.fab.parentNode.removeChild(this.fab);
        }
        
        if (this.panel && this.panel.parentNode) {
            this.panel.parentNode.removeChild(this.panel);
        }
        
        this.fab = null;
        this.panel = null;
    }
}

// 导出组件类
window.SettingsPanel = SettingsPanel;
