<?php
/**
 * 个人资料修改验证码API
 * 
 * 功能：
 * - 发送个人资料修改验证码
 * - 验证个人资料修改验证码
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 开启会话
session_start();

// 开启错误报告（开发环境）
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入认证类
require_once '../auth/Auth.php';
require_once '../auth/SystemConfig.php';

// 检查用户是否已登录
Auth::requireLogin();

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);

    // 如果不是JSON请求，尝试从POST获取
    if (!$input) {
        $input = $_POST;
    }

    $action = trim($input['action'] ?? '');

    switch ($action) {
        case 'send_code':
            handleSendCode($input);
            break;
            
        case 'verify_code':
            handleVerifyCode($input);
            break;

        default:
            throw new Exception('无效的操作类型');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => time()
    ]);
}

/**
 * 处理发送验证码
 */
function handleSendCode($input) {
    $current_user = Auth::getCurrentUser();
    
    if (!$current_user) {
        throw new Exception('用户未登录');
    }
    
    $action_type = trim($input['action_type'] ?? 'profile_update');
    
    // 验证操作类型
    $allowed_types = ['profile_update', 'password_change', 'email_change'];
    if (!in_array($action_type, $allowed_types)) {
        throw new Exception('无效的操作类型');
    }
    
    // 检查是否启用了验证码要求
    $require_verify_code = SystemConfig::get('security', 'profile_require_verify_code', '1') === '1';
    if (!$require_verify_code) {
        throw new Exception('当前系统未启用验证码验证');
    }
    
    // 发送验证码
    $result = Auth::sendProfileVerifyCode($current_user['id'], $current_user['email'], $action_type);
    
    if ($result['success']) {
        // 记录发送成功日志
        error_log("个人资料修改验证码发送成功: {$current_user['email']} - {$action_type} - " . date('Y-m-d H:i:s'));
        
        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'email_sent' => $result['email_sent'] ?? false,
            'expire_minutes' => $result['expire_minutes'] ?? 30,
            'timestamp' => time()
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $result['message'],
            'timestamp' => time()
        ]);
    }
}

/**
 * 处理验证码验证
 */
function handleVerifyCode($input) {
    $current_user = Auth::getCurrentUser();
    
    if (!$current_user) {
        throw new Exception('用户未登录');
    }
    
    $code = trim($input['code'] ?? '');
    $action_type = trim($input['action_type'] ?? 'profile_update');
    
    if (empty($code)) {
        throw new Exception('请输入验证码');
    }
    
    // 验证操作类型
    $allowed_types = ['profile_update', 'password_change', 'email_change'];
    if (!in_array($action_type, $allowed_types)) {
        throw new Exception('无效的操作类型');
    }
    
    // 验证验证码
    $result = Auth::verifyProfileCode($current_user['id'], $current_user['email'], $code, $action_type);
    
    if ($result['success']) {
        // 记录验证成功日志
        error_log("个人资料修改验证码验证成功: {$current_user['email']} - {$action_type} - " . date('Y-m-d H:i:s'));
        
        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'timestamp' => time()
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $result['message'],
            'timestamp' => time()
        ]);
    }
}
?>
