<?php
/**
 * 🔐 验证码 API 接口
 * 
 * 功能：生成和验证各种类型的验证码
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

require_once '../auth/SystemConfig.php';

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'generate':
            $result = generateCaptcha($input['type'] ?? 'math', $input['difficulty'] ?? 'medium');
            break;
            
        case 'verify':
            $result = verifyCaptcha($input['answer'] ?? '', $input['session_key'] ?? '');
            break;
            
        case 'image':
            generateCaptchaImage();
            break;
            
        default:
            throw new Exception('无效的操作');
    }
    
    if ($action !== 'image') {
        echo json_encode($result);
    }
    
} catch (Exception $e) {
    if ($action !== 'image') {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'debug_info' => [
                'action' => $action,
                'input' => $input,
                'session_id' => session_id(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]
        ]);
    }
}

/**
 * 生成验证码
 */
function generateCaptcha($type = 'math', $difficulty = 'medium') {
    $sessionKey = 'captcha_' . uniqid();
    
    switch ($type) {
        case 'math':
            $captcha = generateMathCaptcha($difficulty);
            break;
        case 'alphanumeric':
            $captcha = generateAlphanumericCaptcha($difficulty);
            break;
        case 'slider':
            $captcha = generateSliderCaptcha();
            break;
        default:
            throw new Exception('不支持的验证码类型');
    }
    
    // 存储到会话
    $_SESSION[$sessionKey] = [
        'answer' => $captcha['answer'],
        'type' => $type,
        'created_at' => time(),
        'expires_at' => time() + 300 // 5分钟过期
    ];
    
    return [
        'success' => true,
        'session_key' => $sessionKey,
        'question' => $captcha['question'],
        'type' => $type,
        'difficulty' => $difficulty
    ];
}

/**
 * 验证验证码
 */
function verifyCaptcha($answer, $sessionKey) {
    if (empty($sessionKey) || !isset($_SESSION[$sessionKey])) {
        return [
            'success' => false,
            'message' => '验证码已过期或不存在'
        ];
    }
    
    $captchaData = $_SESSION[$sessionKey];
    
    // 检查是否过期
    if (time() > $captchaData['expires_at']) {
        unset($_SESSION[$sessionKey]);
        return [
            'success' => false,
            'message' => '验证码已过期'
        ];
    }
    
    // 验证答案
    $isValid = false;
    switch ($captchaData['type']) {
        case 'math':
        case 'alphanumeric':
            $isValid = (string)$answer === (string)$captchaData['answer'];
            break;
        case 'slider':
            $isValid = $answer === 'success'; // 滑块验证成功标识
            break;
    }
    
    if ($isValid) {
        // 验证成功，保存验证状态到session
        $_SESSION['captcha_verified_' . $sessionKey] = true;
        // 清除原始验证码数据
        unset($_SESSION[$sessionKey]);

        return [
            'success' => true,
            'message' => '验证成功',
            'session_key' => $sessionKey
        ];
    } else {
        // 验证失败，清除会话数据
        unset($_SESSION[$sessionKey]);

        return [
            'success' => false,
            'message' => '验证码错误'
        ];
    }
}

/**
 * 生成数学计算验证码
 */
function generateMathCaptcha($difficulty) {
    $num1 = 0;
    $num2 = 0;
    $operator = '+';
    $answer = 0;
    
    switch ($difficulty) {
        case 'easy':
            $num1 = rand(1, 10);
            $num2 = rand(1, 10);
            $operator = rand(0, 1) ? '+' : '-';
            if ($operator === '-' && $num1 < $num2) {
                list($num1, $num2) = [$num2, $num1];
            }
            break;
            
        case 'medium':
            $num1 = rand(1, 50);
            $num2 = rand(1, 50);
            $operators = ['+', '-', '×'];
            $operator = $operators[rand(0, 2)];
            if ($operator === '-' && $num1 < $num2) {
                list($num1, $num2) = [$num2, $num1];
            }
            if ($operator === '×') {
                $num1 = rand(1, 10);
                $num2 = rand(1, 10);
            }
            break;
            
        case 'hard':
            $num1 = rand(1, 100);
            $num2 = rand(1, 100);
            $operators = ['+', '-', '×', '÷'];
            $operator = $operators[rand(0, 3)];
            if ($operator === '-' && $num1 < $num2) {
                list($num1, $num2) = [$num2, $num1];
            }
            if ($operator === '×') {
                $num1 = rand(1, 20);
                $num2 = rand(1, 20);
            }
            if ($operator === '÷') {
                $num2 = rand(1, 10);
                $num1 = $num2 * rand(1, 10);
            }
            break;
    }
    
    // 计算答案
    switch ($operator) {
        case '+':
            $answer = $num1 + $num2;
            break;
        case '-':
            $answer = $num1 - $num2;
            break;
        case '×':
            $answer = $num1 * $num2;
            break;
        case '÷':
            $answer = $num1 / $num2;
            break;
    }
    
    return [
        'question' => "$num1 $operator $num2 = ?",
        'answer' => $answer
    ];
}

/**
 * 生成字母数字验证码
 */
function generateAlphanumericCaptcha($difficulty) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $length = 4;
    
    switch ($difficulty) {
        case 'easy':
            $length = 4;
            break;
        case 'medium':
            $length = 5;
            break;
        case 'hard':
            $length = 6;
            break;
    }
    
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $chars[rand(0, strlen($chars) - 1)];
    }
    
    return [
        'question' => $code,
        'answer' => $code
    ];
}

/**
 * 生成滑块验证码
 */
function generateSliderCaptcha() {
    $targetPosition = rand(60, 80); // 目标位置百分比
    
    return [
        'question' => '请拖动滑块完成验证',
        'answer' => 'success', // 滑块验证成功标识
        'target_position' => $targetPosition
    ];
}

/**
 * 生成验证码图片（用于字母数字验证码）
 */
function generateCaptchaImage() {
    $sessionKey = $_GET['key'] ?? '';
    if (empty($sessionKey) || !isset($_SESSION[$sessionKey])) {
        http_response_code(404);
        exit;
    }
    
    $captchaData = $_SESSION[$sessionKey];
    if ($captchaData['type'] !== 'alphanumeric') {
        http_response_code(400);
        exit;
    }
    
    // 创建图片
    $width = 120;
    $height = 40;
    $image = imagecreate($width, $height);
    
    // 设置颜色
    $bgColor = imagecolorallocate($image, 240, 240, 240);
    $textColor = imagecolorallocate($image, 60, 60, 60);
    $lineColor = imagecolorallocate($image, 200, 200, 200);
    
    // 添加干扰线
    for ($i = 0; $i < 5; $i++) {
        imageline($image, rand(0, $width), rand(0, $height), rand(0, $width), rand(0, $height), $lineColor);
    }
    
    // 添加文字
    $text = $captchaData['answer'];
    $fontSize = 5;
    $x = ($width - strlen($text) * imagefontwidth($fontSize)) / 2;
    $y = ($height - imagefontheight($fontSize)) / 2;
    
    imagestring($image, $fontSize, $x, $y, $text, $textColor);
    
    // 输出图片
    header('Content-Type: image/png');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    imagepng($image);
    imagedestroy($image);
}
?>
