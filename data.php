<?php
/**
 * 📦 通用数据导入工具
 * 
 * 功能：导入SQL文件到数据库，支持多文件选择
 * 安全：导入完成后可选择删除此文件
 */

require_once 'config/database.php';

$message = '';
$error = '';
$imported_files = [];

// 获取可用的SQL文件
$sql_files = [];
$data_dir = 'data/';
if (is_dir($data_dir)) {
    $files = scandir($data_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $sql_files[] = $file;
        }
    }
}

// 处理导入请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $selected_files = $_POST['sql_files'] ?? [];
    $delete_tool = isset($_POST['delete_tool']);
    
    if (empty($selected_files)) {
        $error = '请选择要导入的SQL文件';
    } else {
        try {
            $pdo->beginTransaction();
            
            foreach ($selected_files as $filename) {
                // 安全检查：确保文件在data目录下且是.sql文件
                if (!in_array($filename, $sql_files)) {
                    throw new Exception("无效的文件：{$filename}");
                }
                
                $sqlFile = $data_dir . $filename;
                if (!file_exists($sqlFile)) {
                    throw new Exception("文件不存在：{$filename}");
                }
                
                $sql = file_get_contents($sqlFile);
                if ($sql === false) {
                    throw new Exception("无法读取文件：{$filename}");
                }
                
                // 按分号分割SQL语句并清理注释
                $raw_statements = explode(';', $sql);
                $statements = [];

                foreach ($raw_statements as $stmt) {
                    $stmt = trim($stmt);
                    if (empty($stmt)) continue;

                    // 移除注释行，但保留包含SQL语句的内容
                    $lines = explode("\n", $stmt);
                    $sql_lines = [];
                    foreach ($lines as $line) {
                        $line = trim($line);
                        if (!empty($line) && !preg_match('/^\s*--/', $line)) {
                            $sql_lines[] = $line;
                        }
                    }

                    $clean_stmt = implode("\n", $sql_lines);
                    if (!empty($clean_stmt)) {
                        $statements[] = $clean_stmt;
                    }
                }
                
                // 执行SQL语句
                $executed_statements = 0;
                $affected_rows = 0;

                foreach ($statements as $statement) {
                    if (!empty(trim($statement))) {
                        try {
                            $result = $pdo->exec($statement);
                            $executed_statements++;
                            $affected_rows += $result;
                        } catch (PDOException $e) {
                            throw new Exception("执行SQL失败: " . $e->getMessage() . "\nSQL: " . substr($statement, 0, 100) . "...");
                        }
                    }
                }

                $imported_files[] = $filename . " (执行 {$executed_statements} 条语句，影响 {$affected_rows} 行)";
            }
            
            $pdo->commit();
            $message = '成功导入 ' . count($imported_files) . ' 个文件：' . implode(', ', $imported_files);
            
            // 如果选择删除工具，则删除此文件
            if ($delete_tool) {
                unlink(__FILE__);
                $message .= '<br><strong>数据导入工具已删除，请刷新页面。</strong>';
            }
            
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $error = '导入失败：' . $e->getMessage();
        }
    }
}

// 检查各个表是否存在
$table_status = [];
$check_tables = ['users', 'login_logs', 'operation_logs', 'settings'];

foreach ($check_tables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        $table_status[$table] = $stmt->rowCount() > 0;
    } catch (Exception $e) {
        $table_status[$table] = false;
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据导入工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .table-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .table-item {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
        }
        
        .table-item.exists {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .table-item.missing {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .file-selection {
            margin-bottom: 30px;
        }
        
        .file-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .file-item {
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-item:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .file-item.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .file-item input[type="checkbox"] {
            margin-right: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s ease;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .link {
            text-align: center;
            margin-top: 30px;
        }
        
        .link a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            margin: 0 15px;
        }
        
        .link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📦 数据导入工具</h1>
            <p>选择SQL文件导入到数据库</p>
        </div>
        
        <?php if ($message): ?>
        <div class="status success">
            ✅ <?php echo $message; ?>
        </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
        <div class="status error">
            ❌ <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <!-- 数据表状态 -->
        <h3>📋 数据表状态</h3>
        <div class="table-status">
            <?php foreach ($table_status as $table => $exists): ?>
            <div class="table-item <?php echo $exists ? 'exists' : 'missing'; ?>">
                <?php echo $exists ? '✅' : '❌'; ?> <?php echo $table; ?>
            </div>
            <?php endforeach; ?>
        </div>
        
        <?php if (!empty($sql_files)): ?>
        <form method="POST">
            <h3>📁 选择要导入的SQL文件</h3>
            <div class="file-selection">
                <div class="file-list">
                    <?php foreach ($sql_files as $file): ?>
                    <div class="file-item" onclick="toggleFile(this)">
                        <input type="checkbox" name="sql_files[]" value="<?php echo htmlspecialchars($file); ?>" id="file_<?php echo htmlspecialchars($file); ?>">
                        <label for="file_<?php echo htmlspecialchars($file); ?>"><?php echo htmlspecialchars($file); ?></label>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" name="delete_tool" id="delete_tool">
                <label for="delete_tool">
                    <strong>⚠️ 导入完成后删除此工具</strong>
                    <small>（推荐：提高系统安全性）</small>
                </label>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn">🚀 开始导入</button>
                <button type="button" class="btn btn-danger" onclick="selectAll()">📋 全选</button>
            </div>
        </form>
        <?php else: ?>
        <div class="status warning">
            ⚠️ 在 data/ 目录下没有找到SQL文件
        </div>
        <?php endif; ?>
        
        <div class="link">
            <a href="users/">← 返回管理系统</a>
            <a href="users/settings.php">⚙️ 系统设置</a>
        </div>
    </div>
    
    <script>
        function toggleFile(element) {
            const checkbox = element.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked;
            element.classList.toggle('selected', checkbox.checked);
        }
        
        function selectAll() {
            const checkboxes = document.querySelectorAll('input[name="sql_files[]"]');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = !allChecked;
                checkbox.closest('.file-item').classList.toggle('selected', checkbox.checked);
            });
        }
        
        // 初始化选中状态
        document.querySelectorAll('input[name="sql_files[]"]').forEach(checkbox => {
            checkbox.closest('.file-item').classList.toggle('selected', checkbox.checked);
        });
    </script>
</body>
</html>
