/**
 * 🎭 现代化PHP管理系统 - 模态弹窗组件样式
 *
 * 功能特性：
 * - 完整的模态弹窗样式系统
 * - 多种动画效果和过渡
 * - 响应式设计和移动端优化
 * - 主题适配和自定义样式
 * - 无障碍访问支持
 *
 * <AUTHOR>
 * @version 1.0.0
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 模态弹窗专用变量 */
    --modal-z-index: 2000;
    --modal-backdrop-blur: 8px;
    --modal-border-radius: 12px;
    --modal-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --modal-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* 动画时长 */
    --modal-duration-fast: 200ms;
    --modal-duration-normal: 300ms;
    --modal-duration-slow: 500ms;
}

/* ===== 模态弹窗基础样式 ===== */
.modal-overlay {
    position: fixed;
    inset: 0;
    z-index: var(--modal-z-index);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--modal-transition);
    padding: 1rem;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* ===== 背景样式变体 ===== */
.modal-backdrop-blur {
    /* 使用页面背景色作为遮罩基础，确保与主题一致 */
    background: color-mix(in srgb, var(--bg-secondary) 85%, transparent);
    backdrop-filter: blur(var(--modal-backdrop-blur));
    -webkit-backdrop-filter: blur(var(--modal-backdrop-blur));
}

.modal-backdrop-dark {
    background: color-mix(in srgb, var(--bg-secondary) 80%, black);
}

.modal-backdrop-light {
    background: color-mix(in srgb, var(--bg-secondary) 90%, white);
}

.modal-backdrop-transparent {
    background: transparent;
}

/* ===== 主题适配的背景遮罩（兼容性回退） ===== */
/* 亮色主题 */
[data-theme="light"] .modal-backdrop-blur {
    background: rgba(248, 250, 252, 0.85); /* --bg-secondary 的亮色主题值 */
}

[data-theme="light"] .modal-backdrop-dark {
    background: rgba(248, 250, 252, 0.8);
}

[data-theme="light"] .modal-backdrop-light {
    background: rgba(248, 250, 252, 0.9);
}

/* 暗色主题 */
[data-theme="dark"] .modal-backdrop-blur {
    background: rgba(51, 65, 85, 0.85); /* --bg-secondary 的暗色主题值 */
}

[data-theme="dark"] .modal-backdrop-dark {
    background: rgba(51, 65, 85, 0.8);
}

[data-theme="dark"] .modal-backdrop-light {
    background: rgba(51, 65, 85, 0.9);
}

/* 自动主题 - 跟随系统偏好 */
@media (prefers-color-scheme: dark) {
    [data-theme="auto"] .modal-backdrop-blur {
        background: rgba(51, 65, 85, 0.85);
    }

    [data-theme="auto"] .modal-backdrop-dark {
        background: rgba(51, 65, 85, 0.8);
    }

    [data-theme="auto"] .modal-backdrop-light {
        background: rgba(51, 65, 85, 0.9);
    }
}

@media (prefers-color-scheme: light) {
    [data-theme="auto"] .modal-backdrop-blur {
        background: rgba(248, 250, 252, 0.85);
    }

    [data-theme="auto"] .modal-backdrop-dark {
        background: rgba(248, 250, 252, 0.8);
    }

    [data-theme="auto"] .modal-backdrop-light {
        background: rgba(248, 250, 252, 0.9);
    }
}

/* ===== 模态框容器 ===== */
.modal-container {
    position: relative;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    transform: scale(0.9) translateY(20px);
    transition: var(--modal-transition);
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border-radius: var(--modal-border-radius);
    box-shadow: var(--modal-shadow);
    margin: 0 1rem;
    width: fit-content;
    max-width: calc(100vw - 2rem);
}

.modal-overlay.show .modal-container {
    transform: scale(1) translateY(0);
}

/* ===== 尺寸变体 ===== */
.modal-size-small {
    max-width: 400px;
    width: auto;
    min-width: 300px;
}

.modal-size-medium {
    max-width: 600px;
    width: auto;
    min-width: 400px;
}

.modal-size-large {
    max-width: 800px;
    width: auto;
    min-width: 600px;
}

.modal-size-xl {
    max-width: 1200px;
    width: auto;
    min-width: 600px;
}

.modal-size-full {
    width: 95%;
    height: 90%;
    max-width: none;
    max-height: 90vh;
}

/* ===== 圆角变体 ===== */
.modal-radius-small {
    border-radius: 6px;
}

.modal-radius-medium {
    border-radius: 12px;
}

.modal-radius-large {
    border-radius: 20px;
}

.modal-radius-rounded {
    border-radius: 50px;
}

/* ===== 阴影变体 ===== */
.modal-shadow-small {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.modal-shadow-medium {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.modal-shadow-large {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-shadow-xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* ===== 动画变体 ===== */
.modal-animation-fade .modal-container {
    transform: scale(0.95);
    opacity: 0;
}

.modal-animation-fade.show .modal-container {
    transform: scale(1);
    opacity: 1;
}

.modal-animation-slide .modal-container {
    transform: translateY(-100px);
}

.modal-animation-slide.show .modal-container {
    transform: translateY(0);
}

.modal-animation-zoom .modal-container {
    transform: scale(0.5);
}

.modal-animation-zoom.show .modal-container {
    transform: scale(1);
}

.modal-animation-bounce .modal-container {
    transform: scale(0.3);
    transition: all var(--modal-duration-normal) cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.modal-animation-bounce.show .modal-container {
    transform: scale(1);
}

/* ===== 位置变体 ===== */
.modal-position-top {
    align-items: flex-start;
    padding-top: 5vh;
}

.modal-position-bottom {
    align-items: flex-end;
    padding-bottom: 5vh;
}

.modal-position-center {
    align-items: center;
    justify-content: center;
}

/* ===== 模态框头部 ===== */
.modal-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
    background: var(--bg-primary);
    min-width: 0;
    position: relative;
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
}

.modal-close {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    z-index: 1;
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
    transform: scale(1.05);
}

.modal-close:active {
    transform: scale(0.95);
}



.modal-content {
    line-height: 1.6;
    color: var(--text-secondary);
    width: 100%;
    word-wrap: break-word;
    background: transparent;
}

.modal-content h1,
.modal-content h2,
.modal-content h3,
.modal-content h4,
.modal-content h5,
.modal-content h6 {
    color: var(--text-primary);
    margin-top: 0;
}

.modal-content p:last-child {
    margin-bottom: 0;
}

/* ===== 模态框底部 ===== */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    flex-shrink: 0;
    background: var(--bg-primary);
    min-width: 0;
    flex-wrap: wrap;
}

/* ===== 按钮样式 ===== */
.modal-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    min-width: 80px;
    white-space: nowrap;
}

.modal-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.modal-btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.modal-btn-primary:hover {
    background: var(--primary-hover, var(--primary-color));
    border-color: var(--primary-hover, var(--primary-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.3);
}

.modal-btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.modal-btn-secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--text-tertiary);
    transform: translateY(-1px);
}

.modal-btn-danger {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.modal-btn-danger:hover {
    background: var(--error-hover, var(--error-color));
    border-color: var(--error-hover, var(--error-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(var(--error-rgb), 0.3);
}

.modal-btn:active {
    transform: translateY(0);
}

/* ===== 类型图标 ===== */
.modal-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin: 0;
}

.modal-icon-info {
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
}

.modal-icon-success {
    background: rgba(var(--success-rgb), 0.1);
    color: var(--success-color);
}

.modal-icon-warning {
    background: rgba(var(--warning-rgb), 0.1);
    color: var(--warning-color);
}

.modal-icon-error {
    background: rgba(var(--error-rgb), 0.1);
    color: var(--error-color);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 0.5rem;
    }

    .modal-container {
        margin: 0;
        max-height: 95vh;
        width: calc(100vw - 1rem) !important;
        max-width: calc(100vw - 1rem) !important;
        min-width: auto !important;
    }
    
    .modal-header,
    .modal-footer {
        padding: 1rem;
    }
    
    .modal-title {
        font-size: 1.125rem;
    }
    
    .modal-footer {
        flex-direction: column-reverse;
        gap: 0.5rem;
    }
    
    .modal-btn {
        width: 100%;
        justify-content: center;
    }
    
    /* 移动端动画优化 */
    .modal-animation-slide .modal-container {
        transform: translateY(100px);
    }
    
    .modal-animation-bounce .modal-container {
        transition: all var(--modal-duration-fast) ease-out;
    }
}

/* ===== 无障碍访问 ===== */
@media (prefers-reduced-motion: reduce) {
    .modal-overlay,
    .modal-container,
    .modal-btn,
    .modal-close {
        transition: none;
        animation: none;
    }
}

/* ===== 高对比度模式 ===== */
@media (prefers-contrast: high) {
    .modal-container {
        border: 2px solid var(--text-primary);
    }
    
    .modal-btn {
        border-width: 2px;
    }
}

/* ===== 打印样式 ===== */
@media print {
    .modal-overlay {
        display: none !important;
    }
}

/* ===== 模态框表单样式 ===== */
.modal-content .user-form .form-group select {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px;
    padding-right: 2.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.modal-content .user-form .form-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    outline: none;
}

.modal-content .user-form .form-group select:hover {
    border-color: var(--primary-color);
}

.modal-content .user-form .form-group select option {
    background: var(--bg-primary);
    color: var(--text-primary);
    padding: 0.5rem;
    border: none;
}

.modal-content .user-form .form-group input {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.modal-content .user-form .form-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    background: var(--bg-primary);
    outline: none;
}

.modal-content .user-form .form-group input:hover {
    border-color: var(--primary-color);
}

.modal-content .user-form .form-group label {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

/* 暗色主题模态框表单 */
[data-theme="dark"] .modal-content .user-form .form-group select {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%239ca3af' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

[data-theme="dark"] .modal-content .user-form .form-group select:focus {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}
