<?php
/**
 * 记住我功能测试页面
 * 用于测试登录后的记住我功能是否正常工作
 */

// 引入认证中间件
require_once 'auth/Auth.php';
require_once 'auth/SystemConfig.php';

// 启动会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 获取当前用户信息
$current_user = Auth::getCurrentUser();
$is_logged_in = Auth::isLoggedIn();

// 获取记住我相关信息
$remember_me_status = $_SESSION['remember_me'] ?? false;
$remember_expires = $_SESSION['remember_expires'] ?? null;
$session_timeout = Auth::SESSION_TIMEOUT;
$last_activity = $_SESSION[Auth::SESSION_LAST_ACTIVITY] ?? null;

// 计算剩余时间
$remaining_time = null;
if ($remember_me_status && $remember_expires) {
    $remaining_time = $remember_expires - time();
} elseif ($last_activity) {
    $remaining_time = ($last_activity + $session_timeout) - time();
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记住我功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .test-subtitle {
            color: #718096;
            font-size: 1rem;
        }
        
        .status-card {
            background: #f7fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #4299e1;
        }
        
        .status-success {
            border-left-color: #48bb78;
            background: #f0fff4;
        }
        
        .status-warning {
            border-left-color: #ed8936;
            background: #fffaf0;
        }
        
        .status-error {
            border-left-color: #f56565;
            background: #fff5f5;
        }
        
        .status-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 10px;
            color: #2d3748;
        }
        
        .status-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 0.875rem;
            color: #718096;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-weight: 600;
            color: #2d3748;
        }
        
        .btn-group {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn {
            flex: 1;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: inline-block;
        }
        
        .btn-primary {
            background: #4299e1;
            color: white;
        }
        
        .btn-primary:hover {
            background: #3182ce;
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        
        .btn-danger {
            background: #f56565;
            color: white;
        }
        
        .btn-danger:hover {
            background: #e53e3e;
        }
        
        .countdown {
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
            font-weight: bold;
            color: #4299e1;
        }
        
        @media (max-width: 640px) {
            .status-info {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">🔐 记住我功能测试</h1>
            <p class="test-subtitle">检查登录状态和会话管理功能</p>
        </div>
        
        <?php if ($is_logged_in): ?>
            <div class="status-card <?php echo $remember_me_status ? 'status-success' : 'status-warning'; ?>">
                <div class="status-title">
                    <?php echo $remember_me_status ? '✅ 记住我功能已启用' : '⚠️ 普通会话模式'; ?>
                </div>
                <p>
                    <?php if ($remember_me_status): ?>
                        您的登录状态将在较长时间内保持有效，无需频繁重新登录。
                    <?php else: ?>
                        您使用的是普通会话模式，会话将在一定时间后过期。
                    <?php endif; ?>
                </p>
                
                <div class="status-info">
                    <div class="info-item">
                        <span class="info-label">用户名</span>
                        <span class="info-value"><?php echo htmlspecialchars($current_user['username']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">用户角色</span>
                        <span class="info-value"><?php echo htmlspecialchars($current_user['role']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">登录时间</span>
                        <span class="info-value"><?php echo date('Y-m-d H:i:s', $current_user['login_time']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">最后活动</span>
                        <span class="info-value"><?php echo date('Y-m-d H:i:s', $current_user['last_activity']); ?></span>
                    </div>
                    <?php if ($remember_me_status && $remember_expires): ?>
                    <div class="info-item">
                        <span class="info-label">记住我到期时间</span>
                        <span class="info-value"><?php echo date('Y-m-d H:i:s', $remember_expires); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">剩余时间</span>
                        <span class="info-value countdown" id="countdown">
                            <?php echo $remaining_time > 0 ? gmdate('H:i:s', $remaining_time) : '已过期'; ?>
                        </span>
                    </div>
                    <?php else: ?>
                    <div class="info-item">
                        <span class="info-label">会话超时时间</span>
                        <span class="info-value"><?php echo gmdate('H:i:s', $session_timeout); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">会话剩余时间</span>
                        <span class="info-value countdown" id="countdown">
                            <?php echo $remaining_time > 0 ? gmdate('H:i:s', $remaining_time) : '已过期'; ?>
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <div class="status-card status-error">
                <div class="status-title">❌ 未登录状态</div>
                <p>您当前未登录，请先登录以测试记住我功能。</p>
            </div>
        <?php endif; ?>
        
        <div class="btn-group">
            <?php if ($is_logged_in): ?>
                <a href="users/index.php" class="btn btn-primary">返回仪表盘</a>
                <button onclick="location.reload()" class="btn btn-secondary">刷新测试</button>
                <a href="api/logout.api.php" class="btn btn-danger">退出登录</a>
            <?php else: ?>
                <a href="login.php" class="btn btn-primary">前往登录</a>
                <button onclick="location.reload()" class="btn btn-secondary">刷新页面</button>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // 倒计时功能
        <?php if ($is_logged_in && $remaining_time > 0): ?>
        let remainingTime = <?php echo $remaining_time; ?>;
        const countdownElement = document.getElementById('countdown');
        
        function updateCountdown() {
            if (remainingTime <= 0) {
                countdownElement.textContent = '已过期';
                countdownElement.style.color = '#f56565';
                return;
            }
            
            const hours = Math.floor(remainingTime / 3600);
            const minutes = Math.floor((remainingTime % 3600) / 60);
            const seconds = remainingTime % 60;
            
            countdownElement.textContent = 
                String(hours).padStart(2, '0') + ':' +
                String(minutes).padStart(2, '0') + ':' +
                String(seconds).padStart(2, '0');
            
            remainingTime--;
        }
        
        // 每秒更新倒计时
        setInterval(updateCountdown, 1000);
        <?php endif; ?>
        
        // 自动刷新功能（每30秒）
        setTimeout(() => {
            location.reload();
        }, 30000);
        
        console.log('🔐 记住我功能测试页面已加载');
        console.log('登录状态:', <?php echo $is_logged_in ? 'true' : 'false'; ?>);
        console.log('记住我状态:', <?php echo $remember_me_status ? 'true' : 'false'; ?>);
    </script>
</body>
</html>
