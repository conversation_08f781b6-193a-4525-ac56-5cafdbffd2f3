<?php
/**
 * 🎯 会员管理页面
 * 
 * 功能：用户会员管理、分组配置、升级操作
 * 权限：仅管理员可访问
 */

session_start();

// 引入认证中间件
require_once '../auth/auth.php';
require_once '../auth/SystemConfig.php';
require_once 'includes/MembershipManager.php';

// 检查用户是否已登录且为管理员
Auth::requireLogin();
if (!Auth::hasRole(Auth::ROLE_ADMIN)) {
    header('Location: index.php');
    exit;
}

// 获取当前用户信息
$current_user = Auth::getCurrentUser();
$page_title = '会员管理';

// 处理分页和搜索
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$group_filter = isset($_GET['group']) ? trim($_GET['group']) : '';

// 获取用户列表
$result = MembershipManager::getUsersWithMembership($page, 20, $search, $group_filter);
$users = $result['users'];
$total_users = $result['total'];
$total_pages = $result['pages'];

// 获取所有分组
$groups = MembershipManager::getAllGroups();

// 获取统计信息
$stats = MembershipManager::getMembershipStats();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle($page_title); ?></title>
    
    <!-- 引入会员管理样式 -->
    <link rel="stylesheet" href="assets/css/membership.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- 头部 -->
        <?php include 'includes/header.php'; ?>
        
        <!-- 主要内容 -->
        <main class="main-content">
            <div class="membership-container">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="header-left">
                        <h1 class="page-title">
                            <svg class="title-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                            会员管理
                        </h1>
                        <p class="page-subtitle">管理用户分组和会员权限</p>
                    </div>
                    
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="showGroupModal()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="5" x2="12" y2="19"/>
                                <line x1="5" y1="12" x2="19" y2="12"/>
                            </svg>
                            添加分组
                        </button>
                    </div>
                </div>
                
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <?php foreach ($stats['by_group'] as $group_stat): ?>
                    <div class="stat-card">
                        <div class="stat-icon <?php echo $group_stat['group_type']; ?>">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                            </svg>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number"><?php echo $group_stat['count']; ?></h3>
                            <p class="stat-label"><?php echo htmlspecialchars($group_stat['group_name']); ?></p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    
                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <line x1="12" y1="8" x2="12" y2="12"/>
                                <line x1="12" y1="16" x2="12.01" y2="16"/>
                            </svg>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number"><?php echo $stats['expiring_soon']; ?></h3>
                            <p class="stat-label">即将过期</p>
                        </div>
                    </div>
                </div>
                
                <!-- 搜索和过滤 -->
                <div class="filters-section">
                    <form method="GET" class="filters-form">
                        <div class="search-box">
                            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="m21 21-4.35-4.35"/>
                            </svg>
                            <input type="text" name="search" placeholder="搜索用户..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        
                        <select name="group" class="filter-select">
                            <option value="">所有分组</option>
                            <?php foreach ($groups as $group): ?>
                            <option value="<?php echo $group['group_key']; ?>" <?php echo $group_filter === $group['group_key'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($group['group_name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <button type="submit" class="btn btn-secondary">搜索</button>
                        <a href="membership.php" class="btn btn-outline">重置</a>
                    </form>
                </div>
                
                <!-- 用户列表 -->
                <div class="users-section">
                    <div class="section-header">
                        <h2>用户列表</h2>
                        <span class="user-count">共 <?php echo $total_users; ?> 个用户</span>
                    </div>
                    
                    <div class="users-table-container">
                        <table class="users-table">
                            <thead>
                                <tr>
                                    <th>用户信息</th>
                                    <th>分组</th>
                                    <th>会员类型</th>
                                    <th>状态</th>
                                    <th>到期时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="user-info">
                                            <div class="user-avatar">
                                                <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                            </div>
                                            <div class="user-details">
                                                <div class="user-name"><?php echo htmlspecialchars($user['username']); ?></div>
                                                <div class="user-email"><?php echo htmlspecialchars($user['email']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="group-badge <?php echo $user['group_type'] ?? 'free'; ?>">
                                            <?php echo htmlspecialchars($user['group_name'] ?? '未分组'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="membership-type <?php echo $user['membership_type'] ?? 'free'; ?>">
                                            <?php 
                                            $types = ['free' => '免费', 'trial' => '试用', 'paid' => '付费', 'gift' => '赠送'];
                                            echo $types[$user['membership_type']] ?? '未知';
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge <?php echo $user['validity'] ?? 'unknown'; ?>">
                                            <?php 
                                            $statuses = ['active' => '正常', 'expired' => '已过期', 'permanent' => '永久'];
                                            echo $statuses[$user['validity']] ?? '未知';
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($user['end_date']): ?>
                                            <div class="expire-info">
                                                <div class="expire-date"><?php echo date('Y-m-d', strtotime($user['end_date'])); ?></div>
                                                <?php if ($user['days_remaining'] !== null): ?>
                                                <div class="days-remaining <?php echo $user['days_remaining'] <= 7 ? 'warning' : ''; ?>">
                                                    <?php echo $user['days_remaining'] > 0 ? "剩余{$user['days_remaining']}天" : '已过期'; ?>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="permanent">永久</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-action" onclick="upgradeUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')" title="升级">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <line x1="12" y1="19" x2="12" y2="5"/>
                                                    <polyline points="5,12 12,5 19,12"/>
                                                </svg>
                                            </button>
                                            <button class="btn-action" onclick="renewUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')" title="续费">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <polyline points="23,4 23,10 17,10"/>
                                                    <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"/>
                                                </svg>
                                            </button>
                                            <button class="btn-action" onclick="viewHistory(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')" title="历史">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <circle cx="12" cy="12" r="10"/>
                                                    <polyline points="12,6 12,12 16,14"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&group=<?php echo urlencode($group_filter); ?>" class="page-btn">上一页</a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&group=<?php echo urlencode($group_filter); ?>" 
                           class="page-btn <?php echo $i === $page ? 'active' : ''; ?>"><?php echo $i; ?></a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&group=<?php echo urlencode($group_filter); ?>" class="page-btn">下一页</a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
        
        <!-- 页脚 -->
        <?php include 'includes/footer.php'; ?>
    </div>
    
    <!-- 模态框容器 -->
    <div id="modalContainer"></div>
    
    <!-- 引入会员管理脚本 -->
    <script src="assets/js/membership.js"></script>
    <script>
        // 传递PHP数据到JavaScript
        window.membershipData = {
            groups: <?php echo json_encode($groups); ?>,
            currentUser: <?php echo json_encode($current_user); ?>
        };
    </script>
</body>
</html>
