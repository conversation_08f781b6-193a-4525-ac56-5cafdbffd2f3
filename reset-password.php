<?php
/**
 * 🔄 现代化PHP管理系统 - 重置密码页面
 * 
 * 功能：用户密码重置界面（通过邮件链接访问）
 * 设计：采用现代化UI设计语言，与其他页面保持一致
 * 特色：响应式布局、主题切换、表单验证、密码强度检测
 */

session_start();

// 引入认证类
require_once 'auth/Auth.php';

// 获取重置令牌
$token = $_GET['token'] ?? '';
$error_message = '';
$success_message = '';
$user_email = '';

// 验证令牌
if (empty($token)) {
    $error_message = '无效的重置链接，请重新申请密码重置';
} else {
    // 验证令牌的有效性
    $validation = Auth::validateResetToken($token);
    if (!$validation['valid']) {
        $error_message = $validation['message'] ?? '重置链接已过期或无效，请重新申请密码重置';
    } else {
        $user_email = $validation['email'];
    }
}

// 处理密码重置逻辑
if ($_SERVER['REQUEST_METHOD'] === 'POST' && empty($error_message)) {
    $password = trim($_POST['password'] ?? '');
    $confirm_password = trim($_POST['confirm_password'] ?? '');

    // 基础验证
    if (empty($password)) {
        $error_message = '请输入新密码';
    } elseif ($password !== $confirm_password) {
        $error_message = '两次输入的密码不一致';
    } else {
        // 验证密码强度
        require_once 'auth/SystemConfig.php';
        $passwordValidation = SystemConfig::validatePasswordComplexity($password);
        if (!$passwordValidation['valid']) {
            $error_message = implode('；', $passwordValidation['errors']);
        } else {
            // 重置密码
            $result = Auth::resetPassword($token, $password);
            if ($result['success']) {
                $success_message = $result['message'];
                // 清空令牌，防止重复使用
                $token = '';
            } else {
                $error_message = $result['message'];
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码 - 现代化PHP管理系统</title>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- SEO和社交媒体标签 -->
    <meta name="description" content="现代化PHP管理系统密码重置页面，安全地设置您的新密码">
    <meta name="keywords" content="PHP, 管理系统, 重置密码, 密码设置">
    <meta name="author" content="现代化PHP管理系统">
    
    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔄</text></svg>">
</head>
<body>
    <!-- 重置密码容器 -->
    <div class="login-container">
        <!-- 重置密码卡片 -->
        <div class="login-card">
            <!-- 重置密码头部 -->
            <div class="login-header">
                <h1 class="login-title">重置密码</h1>
                <p class="login-subtitle">请设置您的新密码，确保密码安全性</p>
            </div>
            
            <!-- 重置密码表单 -->
            <div class="login-body">
                <?php if (empty($error_message) && empty($success_message)): ?>
                <form id="resetPasswordForm" method="POST" action="" data-validate>
                    <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                    
                    <!-- 新密码输入框 -->
                    <div class="form-group">
                        <label for="password" class="form-label">新密码</label>
                        <div class="password-input-wrapper">
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="form-control" 
                                placeholder="请输入新密码（至少6个字符）"
                                autocomplete="new-password"
                                required
                            >
                            <button type="button" class="password-toggle" aria-label="显示/隐藏密码">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                        <!-- 密码强度指示器 -->
                        <div class="password-strength" id="passwordStrength">
                            <div class="password-strength-bar">
                                <div class="password-strength-fill"></div>
                            </div>
                            <span class="password-strength-text">请输入密码</span>
                        </div>
                    </div>
                    
                    <!-- 确认密码输入框 -->
                    <div class="form-group">
                        <label for="confirm_password" class="form-label">确认新密码</label>
                        <div class="password-input-wrapper">
                            <input 
                                type="password" 
                                id="confirm_password" 
                                name="confirm_password" 
                                class="form-control" 
                                placeholder="请再次输入新密码"
                                autocomplete="new-password"
                                required
                            >
                            <button type="button" class="password-toggle" aria-label="显示/隐藏密码">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 重置密码按钮 -->
                    <button type="submit" class="btn btn-primary w-full mb-4" data-tooltip="设置新密码后您需要使用新密码重新登录">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
                        </svg>
                        重置密码
                    </button>
                    
                    <!-- 返回登录链接 -->
                    <div class="text-center">
                        <a href="login.php" class="text-sm text-muted hover:text-primary">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="inline mr-1">
                                <polyline points="15,18 9,12 15,6"></polyline>
                            </svg>
                            返回登录
                        </a>
                    </div>
                </form>
                <?php else: ?>
                <!-- 错误或成功状态显示 -->
                <div class="text-center">
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-error mb-4">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <line x1="15" y1="9" x2="9" y2="15"/>
                                <line x1="9" y1="9" x2="15" y2="15"/>
                            </svg>
                            <?php echo htmlspecialchars($error_message); ?>
                        </div>
                        <a href="forgot-password.php" class="btn btn-primary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                            重新申请重置
                        </a>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success mb-4">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                                <polyline points="22,4 12,14.01 9,11.01"/>
                            </svg>
                            <?php echo htmlspecialchars($success_message); ?>
                        </div>
                        <a href="login.php" class="btn btn-primary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                                <polyline points="10,17 15,12 10,7"></polyline>
                                <line x1="15" y1="12" x2="3" y2="12"></line>
                            </svg>
                            立即登录
                        </a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- 页脚信息 -->
        <div class="page-footer">
            <p class="text-center text-sm text-muted">
                © <?php echo date('Y'); ?> 现代化PHP管理系统. 保留所有权利.
            </p>
        </div>
    </div>
    
    <!-- 统一组件加载器 - 只需要引入这一个文件 -->
    <script src="assets/js/main.js"></script>

    <!-- 页面特定脚本 -->
    <script>
        /**
         * 页面特定的初始化逻辑
         */
        function initializeResetPasswordPage() {
            // 显示服务器端消息
            <?php if (!empty($error_message)): ?>
                showToast('<?php echo addslashes($error_message); ?>', 'error');
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                showToast('<?php echo addslashes($success_message); ?>', 'success');
                // 成功重置后延迟跳转到登录页面
                setTimeout(() => {
                    window.location.href = 'login.php?password_reset=1';
                }, 3000);
            <?php endif; ?>

            // 密码显示/隐藏切换
            initPasswordToggle();

            // 密码强度检测
            initPasswordStrength();

            // 密码确认验证
            initPasswordConfirmation();

            // 重置密码表单提交处理
            initResetPasswordFormSubmit();

            console.log('🔄 重置密码页面特定功能已初始化完成');
        }

        // 密码显示/隐藏切换
        function initPasswordToggle() {
            const toggleButtons = document.querySelectorAll('.password-toggle');

            toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const input = this.parentElement.querySelector('input');
                    const isPassword = input.type === 'password';

                    input.type = isPassword ? 'text' : 'password';

                    // 更新图标
                    this.innerHTML = isPassword ?
                        `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                            <line x1="1" y1="1" x2="23" y2="23"/>
                        </svg>` :
                        `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                            <circle cx="12" cy="12" r="3"/>
                        </svg>`;

                    // 添加点击动画
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 100);
                });
            });
        }

        // 密码强度检测
        function initPasswordStrength() {
            const passwordInput = document.getElementById('password');
            const strengthIndicator = document.getElementById('passwordStrength');

            if (passwordInput && strengthIndicator) {
                const strengthBar = strengthIndicator.querySelector('.password-strength-fill');
                const strengthText = strengthIndicator.querySelector('.password-strength-text');

                passwordInput.addEventListener('input', async function() {
                    const password = this.value;
                    const strength = await calculatePasswordStrength(password);

                    // 更新强度条
                    strengthBar.style.width = `${strength.percentage}%`;
                    strengthBar.className = `password-strength-fill strength-${strength.level}`;

                    // 更新强度文本
                    let displayText = strength.text;
                    if (strength.feedback && strength.feedback.length > 0) {
                        displayText += ` (${strength.feedback.join(', ')})`;
                    }
                    strengthText.textContent = displayText;
                    strengthText.className = `password-strength-text text-${strength.level}`;
                });
            }
        }

        // 安全配置缓存
        let securityConfig = null;

        // 获取安全配置
        async function getSecurityConfig() {
            if (securityConfig) return securityConfig;

            try {
                const response = await fetch('auth/SystemConfig.php?action=get_security_config');
                const data = await response.json();
                if (data.success) {
                    securityConfig = data.config;
                    return securityConfig;
                }
            } catch (error) {
                console.error('获取安全配置失败:', error);
            }

            // 使用默认配置
            return {
                password_min_length: 6,
                password_complexity: {
                    require_uppercase: false,
                    require_lowercase: false,
                    require_numbers: false,
                    require_symbols: false
                }
            };
        }

        // 计算密码强度 - 使用动态配置
        async function calculatePasswordStrength(password) {
            if (!password) {
                return { level: 'weak', percentage: 0, text: '请输入密码' };
            }

            const config = await getSecurityConfig();
            let score = 0;
            let feedback = [];

            // 基础长度检查
            if (password.length >= config.password_min_length) score += 2;
            if (password.length >= config.password_min_length + 2) score += 1;
            if (password.length >= 12) score += 1;

            // 复杂度检查
            const hasLowercase = /[a-z]/.test(password);
            const hasUppercase = /[A-Z]/.test(password);
            const hasNumbers = /\d/.test(password);
            const hasSymbols = /[^A-Za-z0-9]/.test(password);

            if (hasLowercase) score += 1;
            if (hasUppercase) score += 1;
            if (hasNumbers) score += 1;
            if (hasSymbols) score += 1;

            // 检查必需的复杂度要求
            if (config.password_complexity.require_lowercase && !hasLowercase) {
                feedback.push('需要小写字母');
            }
            if (config.password_complexity.require_uppercase && !hasUppercase) {
                feedback.push('需要大写字母');
            }
            if (config.password_complexity.require_numbers && !hasNumbers) {
                feedback.push('需要数字');
            }
            if (config.password_complexity.require_symbols && !hasSymbols) {
                feedback.push('需要特殊字符');
            }

            // 确定强度等级
            if (score <= 2) {
                return { level: 'weak', percentage: 20, text: '弱', feedback: feedback };
            } else if (score <= 4) {
                return { level: 'fair', percentage: 40, text: '一般', feedback: feedback };
            } else if (score <= 5) {
                return { level: 'good', percentage: 60, text: '良好', feedback: feedback };
            } else if (score <= 6) {
                return { level: 'strong', percentage: 80, text: '强', feedback: feedback };
            } else {
                return { level: 'very-strong', percentage: 100, text: '很强', feedback: feedback };
            }
        }

        // 密码确认验证
        function initPasswordConfirmation() {
            const passwordInput = document.getElementById('password');
            const confirmInput = document.getElementById('confirm_password');

            if (passwordInput && confirmInput) {
                function validatePasswordMatch() {
                    if (confirmInput.value && passwordInput.value !== confirmInput.value) {
                        confirmInput.setCustomValidity('密码不匹配');
                        confirmInput.classList.add('error');
                    } else {
                        confirmInput.setCustomValidity('');
                        confirmInput.classList.remove('error');
                    }
                }

                passwordInput.addEventListener('input', validatePasswordMatch);
                confirmInput.addEventListener('input', validatePasswordMatch);
            }
        }

        // 重置密码表单提交处理
        function initResetPasswordFormSubmit() {
            const resetPasswordForm = document.getElementById('resetPasswordForm');
            if (!resetPasswordForm) return;

            const submitBtn = resetPasswordForm.querySelector('button[type="submit"]');

            resetPasswordForm.addEventListener('submit', function(e) {
                // 显示加载动画
                showToast('正在重置密码...', 'info');

                // 添加按钮加载状态
                submitBtn.disabled = true;
                submitBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="loading-spinner">
                        <path d="M21 12a9 9 0 11-6.219-8.56"/>
                    </svg>
                    重置中...
                `;

                // 如果是客户端验证失败，恢复按钮状态
                setTimeout(() => {
                    if (submitBtn.disabled && !document.querySelector('.toast.success')) {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = `
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
                            </svg>
                            重置密码
                        `;
                    }
                }, 3000);
            });
        }

        // 当页面加载完成时初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeResetPasswordPage);
        } else {
            initializeResetPasswordPage();
        }
    </script>
