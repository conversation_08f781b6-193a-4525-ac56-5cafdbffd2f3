-- =============================================
-- 现代化PHP管理系统 - 数据库初始化脚本
-- =============================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `modern_php_admin` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `modern_php_admin`;

-- =============================================
-- 用户表 (users)
-- =============================================
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码哈希',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像路径',
  `role` enum('admin','user','guest') NOT NULL DEFAULT 'user' COMMENT '用户角色',
  `user_group` varchar(50) DEFAULT 'default' COMMENT '用户分组',
  `status` enum('active','inactive','banned') NOT NULL DEFAULT 'active' COMMENT '用户状态',
  `email_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT '邮箱是否验证',
  `email_verify_token` varchar(255) DEFAULT NULL COMMENT '邮箱验证令牌',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `last_login_device` varchar(255) DEFAULT NULL COMMENT '最后登录设备',
  `last_login_location` varchar(255) DEFAULT NULL COMMENT '最后登录地区',
  `login_count` int(11) NOT NULL DEFAULT 0 COMMENT '登录次数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_email_verify_token` (`email_verify_token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- =============================================
-- 密码重置验证码表 (password_reset_codes)
-- =============================================
CREATE TABLE IF NOT EXISTS `password_reset_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `used` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已使用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_code` (`code`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='密码重置验证码表';

-- =============================================
-- 个人资料修改验证码表 (profile_verify_codes)
-- =============================================
CREATE TABLE IF NOT EXISTS `profile_verify_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `action_type` varchar(50) NOT NULL DEFAULT 'profile_update' COMMENT '操作类型',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `used` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已使用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_email` (`email`),
  KEY `idx_code` (`code`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_action_type` (`action_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='个人资料修改验证码表';

-- =============================================
-- 密码重置表 (password_resets)
-- =============================================
CREATE TABLE IF NOT EXISTS `password_resets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `token` varchar(255) NOT NULL COMMENT '重置令牌',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `used` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已使用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_token` (`token`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='密码重置表';

-- =============================================
-- 登录日志表 (login_logs)
-- =============================================
CREATE TABLE IF NOT EXISTS `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `device_info` varchar(255) DEFAULT NULL COMMENT '设备信息',
  `location` varchar(255) DEFAULT NULL COMMENT '登录地区',
  `login_status` enum('success','failed','blocked') NOT NULL COMMENT '登录状态',
  `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `session_id` varchar(255) DEFAULT NULL COMMENT '会话ID',
  `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_username` (`username`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_login_status` (`login_status`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';

-- =============================================
-- 操作日志表 (operation_logs)
-- =============================================
CREATE TABLE IF NOT EXISTS `operation_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `operation_desc` varchar(255) NOT NULL COMMENT '操作描述',
  `operation_data` json DEFAULT NULL COMMENT '操作数据',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `response_status` int(11) DEFAULT NULL COMMENT '响应状态码',
  `execution_time` decimal(10,3) DEFAULT NULL COMMENT '执行时间(秒)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_username` (`username`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- =============================================
-- 插入默认管理员用户
-- =============================================
INSERT IGNORE INTO `users` (
  `username`,
  `email`,
  `password`,
  `nickname`,
  `role`,
  `user_group`,
  `status`,
  `email_verified`
) VALUES (
  'admin',
  '<EMAIL>',
  '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', -- password: secret123
  '系统管理员',
  'admin',
  'administrators',
  'active',
  1
);

-- =============================================
-- 系统设置表 (settings)
-- =============================================
CREATE TABLE IF NOT EXISTS `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category` varchar(50) NOT NULL COMMENT '设置分类',
  `setting_key` varchar(100) NOT NULL COMMENT '设置键名',
  `setting_value` text COMMENT '设置值',
  `description` varchar(255) COMMENT '设置描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_setting` (`category`, `setting_key`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- =============================================
-- 插入完整的系统设置数据
-- =============================================
INSERT INTO `settings` (`category`, `setting_key`, `setting_value`, `description`) VALUES
-- 网站基本信息 (website)
('website', 'site_name', '管理系统', '网站名称'),
('website', 'site_description', '现代化PHP管理系统', '网站描述'),
('website', 'site_url', 'http://localhost:8080', '网站地址'),
('website', 'admin_email', '<EMAIL>', '管理员邮箱'),
('website', 'timezone', 'Asia/Shanghai', '时区设置'),
('website', 'language', 'zh-CN', '默认语言'),
-- 邮件设置 (email)
('email', 'smtp_host', '', 'SMTP服务器地址'),
('email', 'smtp_port', '587', 'SMTP端口号'),
('email', 'smtp_username', '', 'SMTP用户名'),
('email', 'smtp_password', '', 'SMTP密码'),
('email', 'smtp_encryption', 'tls', '加密方式（tls/ssl/none）'),
('email', 'from_email', '', '发件人邮箱地址'),
('email', 'from_name', '系统通知', '发件人显示名称'),
-- 安全设置 (security) - 基础安全策略
('security', 'session_timeout', '3600', '会话超时时间（秒）'),
('security', 'max_login_attempts', '5', '最大登录尝试次数'),
('security', 'password_min_length', '6', '密码最小长度'),
('security', 'password_require_uppercase', '0', '密码必须包含大写字母'),
('security', 'password_require_lowercase', '0', '密码必须包含小写字母'),
('security', 'password_require_numbers', '0', '密码必须包含数字'),
('security', 'password_require_symbols', '0', '密码必须包含特殊字符'),
('security', 'login_attempt_lockout_time', '900', '登录失败锁定时间（秒）'),
('security', 'remember_me_duration', '2592000', '记住我功能持续时间（秒）'),
('security', 'require_email_verification', '1', '要求邮箱验证'),
('security', 'enable_two_factor', '0', '启用双因素认证'),
('security', 'login_log_retention', '30', '登录日志保留天数'),
-- 功能开关
('security', 'enable_login', '1', '网站开放访问（关闭后仅管理员可登录）'),
('security', 'enable_register', '1', '启用注册功能'),
('security', 'enable_password_reset', '1', '启用密码找回功能'),
('security', 'profile_require_verify_code', '1', '个人资料修改需要验证码'),
-- 验证码配置
('security', 'captcha_type', 'math', '验证码类型（math/alphanumeric/slider）'),
('security', 'captcha_difficulty', 'medium', '验证码难度（easy/medium/hard）'),
('security', 'enable_login_captcha', '1', '登录时启用验证码'),
('security', 'enable_register_captcha', '1', '注册时启用验证码'),
('security', 'enable_reset_captcha', '1', '密码找回时启用验证码'),
-- 邮件模板设置 (email_template) - 注册验证邮件
('email_template', 'register_verify_subject', '欢迎注册 - 请验证您的邮箱', '注册验证邮件主题'),
('email_template', 'register_verify_template', 'register_verify.html', '注册验证邮件模板文件'),
-- 密码重置邮件
('email_template', 'password_reset_subject', '密码重置请求', '密码重置邮件主题'),
('email_template', 'password_reset_template', 'password_reset.html', '密码重置邮件模板文件'),
-- 通用验证码邮件
('email_template', 'verify_code_subject', '您的验证码', '通用验证码邮件主题'),
('email_template', 'verify_code_template', 'verify_code.html', '通用验证码邮件模板文件'),
-- 验证码配置
('email_template', 'code_expire_time', '30', '验证码有效期（分钟）')

ON DUPLICATE KEY UPDATE
    `setting_value` = VALUES(`setting_value`),
    `description` = VALUES(`description`),
    `updated_at` = CURRENT_TIMESTAMP;
