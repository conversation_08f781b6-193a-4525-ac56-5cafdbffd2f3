/* ===== 会员管理页面样式 ===== */

/* CSS变量定义 */
:root {
    --primary-color-light: #60a5fa;
    --primary-rgb: 59, 130, 246;
    --border-light: rgba(0, 0, 0, 0.05);
    --spacing-16: 4rem;
}

/* 基础容器 */
.members-container {
    padding: var(--spacing-6);
    max-width: 1400px;
    margin: 0 auto;
}

/* ===== 页面头部 ===== */
.members-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-8);
    gap: var(--spacing-6);
}

.header-left {
    flex: 1;
}

.page-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-2) 0;
}

.title-icon {
    color: var(--primary-color);
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
    line-height: 1.5;
}

.header-actions {
    display: flex;
    gap: var(--spacing-3);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.action-btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* ===== 统计卡片 ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: var(--spacing-5);
    margin-bottom: var(--spacing-8);
}

.stat-card {
    background: var(--bg-primary);
    border: none;
    border-radius: 16px;
    padding: var(--spacing-6);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light, #60a5fa));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-icon {
    width: 56px;
    height: 56px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light, #60a5fa));
    color: white;
    box-shadow: 0 4px 12px rgba(var(--primary-rgb, 59, 130, 246), 0.3);
}

.stat-content {
    flex: 1;
    min-width: 0;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-primary);
    line-height: 1.1;
    margin-bottom: var(--spacing-1);
    letter-spacing: -0.02em;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    line-height: 1.4;
}

/* ===== 搜索和筛选 ===== */
.filters-section {
    background: var(--bg-primary);
    border: none;
    border-radius: 16px;
    padding: var(--spacing-5);
    margin-bottom: var(--spacing-6);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: var(--spacing-4);
}

.filters-form {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-4);
    align-items: flex-end;
    flex: 1;
}

/* ===== 视图切换按钮 ===== */
.view-toggle {
    display: flex;
    gap: 2px;
    background: var(--bg-secondary);
    border-radius: 10px;
    padding: 4px;
    flex-shrink: 0;
}

.view-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.view-btn:hover {
    color: var(--text-primary);
    background: var(--bg-primary);
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 8px rgba(var(--primary-rgb, 59, 130, 246), 0.3);
}

.view-btn.active:hover {
    background: var(--primary-color);
    color: white;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 280px;
}

.search-icon {
    position: absolute;
    left: var(--spacing-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    pointer-events: none;
}

.search-box input {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-3) 2.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.filter-selects {
    display: flex;
    gap: var(--spacing-3);
    flex-wrap: wrap;
}

.filter-selects select {
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    min-width: 120px;
    cursor: pointer;
    transition: var(--transition);
}

.filter-selects select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.filter-actions {
    display: flex;
    gap: var(--spacing-2);
}

.btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn.primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn.secondary:hover {
    border-color: var(--text-secondary);
}

/* ===== 用户卡片网格 ===== */
.members-grid-container {
    margin-bottom: var(--spacing-6);
}

.members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: var(--spacing-5);
    margin-bottom: var(--spacing-6);
}

.user-card {
    background: var(--bg-primary);
    border: none;
    border-radius: 16px;
    padding: var(--spacing-6);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(20px);
    animation: cardFadeIn 0.6s ease-out forwards;
}

.user-card:nth-child(1) { animation-delay: 0.1s; }
.user-card:nth-child(2) { animation-delay: 0.2s; }
.user-card:nth-child(3) { animation-delay: 0.3s; }
.user-card:nth-child(4) { animation-delay: 0.4s; }
.user-card:nth-child(5) { animation-delay: 0.5s; }
.user-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes cardFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light, #60a5fa));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.user-card:hover::before {
    opacity: 1;
}

/* ===== 表格视图样式 ===== */
.members-table-container {
    background: var(--bg-primary);
    border: none;
    border-radius: 16px;
    overflow: hidden;
    margin-bottom: var(--spacing-6);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    display: none; /* 默认隐藏表格视图 */
}

.members-table-container.active {
    display: block;
}

.members-grid-container.active {
    display: block;
}

.table-wrapper {
    overflow-x: auto;
}

.members-table {
    width: 100%;
    border-collapse: collapse;
}

.members-table th {
    background: var(--bg-secondary);
    padding: var(--spacing-4);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap;
}

.members-table td {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--border-light);
    vertical-align: top;
}

.user-row:hover {
    background: var(--bg-secondary);
}

/* ===== 用户卡片内容 ===== */
.user-card-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-5);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    flex: 1;
    min-width: 0;
}

.user-avatar {
    width: 52px;
    height: 52px;
    border-radius: 14px;
    overflow: hidden;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light, #60a5fa));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    letter-spacing: -0.02em;
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
    font-size: 1.1rem;
    line-height: 1.3;
}

.user-email {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 2px;
    font-weight: 500;
}

.user-username {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    font-weight: 500;
}

.user-card-actions {
    display: flex;
    gap: var(--spacing-2);
    flex-shrink: 0;
}

.user-card-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-5);
}

.user-card-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.section-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.section-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

/* ===== 卡片内容样式 ===== */
.group-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.group-badge:hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.member-level {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    font-weight: 500;
}

.member-type {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 4px;
    border: none;
    transition: all 0.2s ease;
}

.member-type:hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.member-type-regular {
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    color: #374151;
}

.member-type-vip {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
}

.member-type-premium {
    background: linear-gradient(135deg, #ede9fe, #ddd6fe);
    color: #7c3aed;
}

.member-expiry {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 2px;
    font-weight: 500;
}

.member-status {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-block;
}

.member-status-永久 {
    color: #059669;
    background: rgba(5, 150, 105, 0.1);
}

.member-status-有效 {
    color: #059669;
    background: rgba(5, 150, 105, 0.1);
}

.member-status-已过期 {
    color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
}

/* ===== 积分和状态样式 ===== */
.points-display {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-1);
}

.points-available {
    font-weight: 700;
    color: var(--text-primary);
    font-size: 1.1rem;
    line-height: 1.2;
}

.points-total {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.status-badges {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 4px;
    border: none;
    transition: all 0.2s ease;
    justify-content: center;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-active {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
}

.status-inactive {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
}

.status-banned {
    background: linear-gradient(135deg, #fecaca, #fca5a5);
    color: #991b1b;
}

.verified-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.verified-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(30, 64, 175, 0.2);
}

/* 日期信息列 */
.date-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
    white-space: nowrap;
    min-width: 140px;
}

/* ===== 操作按钮样式 ===== */
.user-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-4);
    border-top: 1px solid var(--border-light);
}

.date-info {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    font-weight: 500;
}

.action-buttons {
    display: flex;
    gap: var(--spacing-2);
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-color);
    opacity: 0;
    transition: opacity 0.2s ease;
    border-radius: 10px;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    color: white;
}

.action-btn:hover::before {
    opacity: 1;
}

.action-btn svg,
.action-btn i {
    position: relative;
    z-index: 1;
}

.action-btn.edit-btn:hover {
    color: white;
}

.action-btn.edit-btn:hover::before {
    background: #3b82f6;
}

.action-btn.manage-btn:hover {
    color: white;
}

.action-btn.manage-btn:hover::before {
    background: #10b981;
}

.action-btn.delete-btn:hover {
    color: white;
}

.action-btn.delete-btn:hover::before {
    background: #ef4444;
}

/* ===== 空状态样式 ===== */
.empty-state {
    text-align: center;
    padding: var(--spacing-16) var(--spacing-6);
    background: var(--bg-primary);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.empty-content {
    color: var(--text-secondary);
    max-width: 300px;
    margin: 0 auto;
}

.empty-content svg {
    margin-bottom: var(--spacing-6);
    color: var(--text-tertiary);
    opacity: 0.6;
}

.empty-content p {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
    line-height: 1.5;
}

/* ===== 分页 ===== */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-2);
    margin-top: var(--spacing-6);
}

.page-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: var(--transition);
}

.page-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.page-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* ===== 模态框 ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-6);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-3);
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

/* 表单样式 */
.form-group {
    margin-bottom: var(--spacing-4);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-help {
    display: block;
    margin-top: var(--spacing-1);
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

.points-input {
    display: flex;
    gap: var(--spacing-2);
}

.points-input input {
    flex: 1;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
    .members-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
}

@media (max-width: 1024px) {
    .members-container {
        padding: var(--spacing-4);
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .members-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
}

@media (max-width: 768px) {
    .members-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-4);
    }

    .filters-form {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filter-selects {
        flex-direction: column;
    }

    .filter-selects select {
        min-width: auto;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .members-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .user-card {
        padding: var(--spacing-5);
    }

    .user-card-body {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }

    .user-card-header {
        flex-direction: column;
        gap: var(--spacing-3);
    }

    .user-card-actions {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .members-container {
        padding: var(--spacing-3);
    }

    .page-title {
        font-size: 1.5rem;
    }

    .modal-content {
        width: 95%;
        margin: var(--spacing-4);
    }

    .modal-body {
        padding: var(--spacing-4);
    }

    .points-input {
        flex-direction: column;
    }

    .user-card {
        padding: var(--spacing-4);
    }

    .user-avatar {
        width: 44px;
        height: 44px;
    }

    .stat-card {
        padding: var(--spacing-4);
    }

    .stat-icon {
        width: 48px;
        height: 48px;
    }

    .stat-number {
        font-size: 1.75rem;
    }
}
