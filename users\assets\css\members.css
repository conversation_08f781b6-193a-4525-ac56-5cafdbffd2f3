/* ===== 会员管理页面样式 ===== */

/* 基础容器 */
.members-container {
    padding: var(--spacing-6);
    max-width: 1400px;
    margin: 0 auto;
}

/* ===== 页面头部 ===== */
.members-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-8);
    gap: var(--spacing-6);
}

.header-left {
    flex: 1;
}

.page-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-2) 0;
}

.title-icon {
    color: var(--primary-color);
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
    line-height: 1.5;
}

.header-actions {
    display: flex;
    gap: var(--spacing-3);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.action-btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* ===== 统计卡片 ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    transition: var(--transition);
}

.stat-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 2px;
}

/* ===== 搜索和筛选 ===== */
.filters-section {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.filters-form {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-4);
    align-items: flex-end;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 280px;
}

.search-icon {
    position: absolute;
    left: var(--spacing-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    pointer-events: none;
}

.search-box input {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-3) 2.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.filter-selects {
    display: flex;
    gap: var(--spacing-3);
    flex-wrap: wrap;
}

.filter-selects select {
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    min-width: 120px;
    cursor: pointer;
    transition: var(--transition);
}

.filter-selects select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.filter-actions {
    display: flex;
    gap: var(--spacing-2);
}

.btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn.primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn.secondary:hover {
    border-color: var(--text-secondary);
}

/* ===== 用户表格 ===== */
.members-table-container {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: var(--spacing-6);
}

.table-wrapper {
    overflow-x: auto;
}

.members-table {
    width: 100%;
    border-collapse: collapse;
}

.members-table th {
    background: var(--bg-secondary);
    padding: var(--spacing-4);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap;
}

.members-table td {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--border-light);
    vertical-align: top;
}

.user-row:hover {
    background: var(--bg-secondary);
}

/* 用户信息列 */
.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    min-width: 200px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1rem;
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.user-email {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 2px;
}

.user-username {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

/* 分组信息列 */
.group-info {
    min-width: 140px;
}

.group-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 4px;
}

.member-level {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

/* 会员信息列 */
.member-info {
    min-width: 120px;
}

.member-type {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
    margin-bottom: 4px;
}

.member-type-regular {
    background: #f3f4f6;
    color: #374151;
}

.member-type-vip {
    background: #fef3c7;
    color: #92400e;
}

.member-type-premium {
    background: #ede9fe;
    color: #7c3aed;
}

.member-expiry {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 2px;
}

.member-status {
    font-size: 0.75rem;
    font-weight: 500;
}

.member-status-永久 {
    color: #059669;
}

.member-status-有效 {
    color: #059669;
}

.member-status-已过期 {
    color: #dc2626;
}

/* 积分信息列 */
.points-info {
    text-align: right;
    min-width: 100px;
}

.points-available {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.points-total {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* 状态信息列 */
.status-info {
    min-width: 100px;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 4px;
}

.status-active {
    background: #dcfce7;
    color: #166534;
}

.status-inactive {
    background: #fef3c7;
    color: #92400e;
}

.status-banned {
    background: #fecaca;
    color: #991b1b;
}

.verified-badge {
    display: inline-block;
    padding: 2px 6px;
    background: #dbeafe;
    color: #1e40af;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* 日期信息列 */
.date-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
    white-space: nowrap;
    min-width: 140px;
}

/* 操作列 */
.actions {
    min-width: 80px;
}

.action-buttons {
    display: flex;
    gap: var(--spacing-2);
}

.action-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--primary-color)10;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-12) var(--spacing-6);
}

.empty-content {
    color: var(--text-secondary);
}

.empty-content svg {
    margin-bottom: var(--spacing-4);
    color: var(--text-tertiary);
}

.empty-content p {
    margin: 0;
    font-size: 1rem;
}

/* ===== 分页 ===== */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-2);
    margin-top: var(--spacing-6);
}

.page-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: var(--transition);
}

.page-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.page-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* ===== 模态框 ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-6);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-3);
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

/* 表单样式 */
.form-group {
    margin-bottom: var(--spacing-4);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-help {
    display: block;
    margin-top: var(--spacing-1);
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

.points-input {
    display: flex;
    gap: var(--spacing-2);
}

.points-input input {
    flex: 1;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
    .members-container {
        padding: var(--spacing-4);
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    .members-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-4);
    }
    
    .filters-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .filter-selects {
        flex-direction: column;
    }
    
    .filter-selects select {
        min-width: auto;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .members-table th,
    .members-table td {
        padding: var(--spacing-2);
    }
    
    .user-info {
        min-width: 150px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .members-container {
        padding: var(--spacing-3);
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .modal-content {
        width: 95%;
        margin: var(--spacing-4);
    }
    
    .modal-body {
        padding: var(--spacing-4);
    }
    
    .points-input {
        flex-direction: column;
    }
}
