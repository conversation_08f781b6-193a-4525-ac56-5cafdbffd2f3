<?php
/**
 * 👥 用户管理API接口
 * 
 * 功能：处理用户管理相关请求
 * 方法：GET, POST
 * 权限：仅管理员可访问
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入认证类
require_once '../auth/Auth.php';

// 检查用户是否已登录
if (!Auth::isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
}

// 检查管理员权限
if (!Auth::hasRole(Auth::ROLE_ADMIN)) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => '权限不足，仅管理员可访问'
    ]);
    exit;
}

// 引入数据库配置
require_once '../config/database.php';

try {
    $db = Database::getInstance();
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        handleGetRequest($db);
    } elseif ($method === 'POST') {
        handlePostRequest($db);
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => '不支持的请求方法'
        ]);
    }
    
} catch (Exception $e) {
    error_log("用户管理API错误: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误'
    ]);
}

/**
 * 处理GET请求
 */
function handleGetRequest($db) {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'get':
            getUserInfo($db);
            break;
        case 'list':
            getUserList($db);
            break;
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => '无效的操作'
            ]);
    }
}

/**
 * 处理POST请求
 */
function handlePostRequest($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST;
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'create':
            createUser($db, $input);
            break;
        case 'update':
            updateUser($db, $input);
            break;
        case 'delete':
            deleteUser($db, $input);
            break;
        case 'toggle_status':
            toggleUserStatus($db, $input);
            break;
        case 'get_unverified_count':
            getUnverifiedCount($db);
            break;
        case 'cleanup_unverified':
            cleanupUnverifiedUsers($db);
            break;
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => '无效的操作'
            ]);
    }
}

/**
 * 获取单个用户信息
 */
function getUserInfo($db) {
    $userId = intval($_GET['id'] ?? 0);
    
    if ($userId <= 0) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => '无效的用户ID'
        ]);
        return;
    }
    
    $user = $db->fetch(
        "SELECT id, username, email, nickname, role, user_group, status, email_verified, 
                last_login_time, last_login_ip, login_count, created_at, updated_at 
         FROM users WHERE id = ?",
        [$userId]
    );
    
    if (!$user) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => '用户不存在'
        ]);
        return;
    }
    
    echo json_encode([
        'success' => true,
        'user' => $user
    ]);
}

/**
 * 获取用户列表
 */
function getUserList($db) {
    $page = max(1, intval($_GET['page'] ?? 1));
    $per_page = min(100, max(1, intval($_GET['per_page'] ?? 20)));
    $offset = ($page - 1) * $per_page;
    
    $search = trim($_GET['search'] ?? '');
    $role_filter = $_GET['role'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    
    // 构建查询条件
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(username LIKE ? OR email LIKE ? OR nickname LIKE ?)";
        $search_param = "%{$search}%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    if (!empty($role_filter)) {
        $where_conditions[] = "role = ?";
        $params[] = $role_filter;
    }
    
    if (!empty($status_filter)) {
        $where_conditions[] = "status = ?";
        $params[] = $status_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // 获取总数
    $total_sql = "SELECT COUNT(*) as total FROM users {$where_clause}";
    $total_result = $db->fetch($total_sql, $params);
    $total = $total_result['total'];
    
    // 获取用户列表
    $users_sql = "SELECT id, username, email, nickname, role, user_group, status, email_verified, 
                         last_login_time, last_login_ip, login_count, created_at, updated_at 
                  FROM users {$where_clause} 
                  ORDER BY created_at DESC 
                  LIMIT {$per_page} OFFSET {$offset}";
    $users = $db->fetchAll($users_sql, $params);
    
    echo json_encode([
        'success' => true,
        'users' => $users,
        'pagination' => [
            'page' => $page,
            'per_page' => $per_page,
            'total' => $total,
            'total_pages' => ceil($total / $per_page)
        ]
    ]);
}

/**
 * 创建用户
 */
function createUser($db, $input) {
    // 验证输入
    $username = trim($input['username'] ?? '');
    $email = trim($input['email'] ?? '');
    $nickname = trim($input['nickname'] ?? '');
    $role = $input['role'] ?? 'user';
    $status = $input['status'] ?? 'active';
    $password = $input['password'] ?? '';
    $confirm_password = $input['confirm_password'] ?? '';
    $email_verified = intval($input['email_verified'] ?? 0);

    // 基本验证
    if (empty($username) || empty($email) || empty($password)) {
        throw new Exception('用户名、邮箱和密码不能为空');
    }

    if ($password !== $confirm_password) {
        throw new Exception('两次输入的密码不一致');
    }

    if (strlen($password) < 6) {
        throw new Exception('密码长度至少6位');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('请输入有效的邮箱地址');
    }

    if (!in_array($role, ['admin', 'user', 'guest'])) {
        throw new Exception('无效的用户角色');
    }

    if (!in_array($status, ['active', 'inactive', 'banned'])) {
        throw new Exception('无效的用户状态');
    }

    if (!in_array($email_verified, [0, 1])) {
        throw new Exception('无效的邮箱验证状态');
    }
    
    // 检查用户名是否已存在
    $existing_user = $db->fetch("SELECT id FROM users WHERE username = ?", [$username]);
    if ($existing_user) {
        throw new Exception('用户名已存在');
    }
    
    // 检查邮箱是否已存在
    $existing_email = $db->fetch("SELECT id FROM users WHERE email = ?", [$email]);
    if ($existing_email) {
        throw new Exception('邮箱已被使用');
    }
    
    // 密码加密
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // 插入用户
    $user_id = $db->insert(
        "INSERT INTO users (username, email, password, nickname, role, status, email_verified, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, NOW())",
        [$username, $email, $hashed_password, $nickname ?: $username, $role, $status, $email_verified]
    );

    // 记录操作日志
    Auth::logOperation('user_create', '创建用户', [
        'target_user_id' => $user_id,
        'username' => $username,
        'email' => $email,
        'role' => $role,
        'status' => $status,
        'email_verified' => $email_verified
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => '用户创建成功',
        'user_id' => $user_id
    ]);
}

/**
 * 更新用户
 */
function updateUser($db, $input) {
    $userId = intval($input['id'] ?? 0);
    
    if ($userId <= 0) {
        throw new Exception('无效的用户ID');
    }
    
    // 检查用户是否存在
    $user = $db->fetch("SELECT * FROM users WHERE id = ?", [$userId]);
    if (!$user) {
        throw new Exception('用户不存在');
    }
    
    // 验证输入
    $email = trim($input['email'] ?? '');
    $nickname = trim($input['nickname'] ?? '');
    $role = $input['role'] ?? $user['role'];
    $status = $input['status'] ?? $user['status'];
    $password = $input['password'] ?? '';
    $confirm_password = $input['confirm_password'] ?? '';
    $email_verified = intval($input['email_verified'] ?? $user['email_verified']);

    if (empty($email)) {
        throw new Exception('邮箱不能为空');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('请输入有效的邮箱地址');
    }

    if (!in_array($role, ['admin', 'user', 'guest'])) {
        throw new Exception('无效的用户角色');
    }

    if (!in_array($status, ['active', 'inactive', 'banned'])) {
        throw new Exception('无效的用户状态');
    }

    if (!in_array($email_verified, [0, 1])) {
        throw new Exception('无效的邮箱验证状态');
    }
    
    // 检查邮箱是否被其他用户使用
    $existing_email = $db->fetch("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $userId]);
    if ($existing_email) {
        throw new Exception('邮箱已被其他用户使用');
    }
    
    // 密码验证
    if (!empty($password)) {
        if ($password !== $confirm_password) {
            throw new Exception('两次输入的密码不一致');
        }
        
        if (strlen($password) < 6) {
            throw new Exception('密码长度至少6位');
        }
    }
    
    // 更新用户信息
    if (!empty($password)) {
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        $affected = $db->update(
            "UPDATE users SET email = ?, nickname = ?, role = ?, status = ?, email_verified = ?, password = ?, updated_at = NOW() WHERE id = ?",
            [$email, $nickname ?: $user['username'], $role, $status, $email_verified, $hashed_password, $userId]
        );
    } else {
        $affected = $db->update(
            "UPDATE users SET email = ?, nickname = ?, role = ?, status = ?, email_verified = ?, updated_at = NOW() WHERE id = ?",
            [$email, $nickname ?: $user['username'], $role, $status, $email_verified, $userId]
        );
    }

    // 记录操作日志
    Auth::logOperation('user_update', '更新用户信息', [
        'target_user_id' => $userId,
        'username' => $user['username'],
        'changes' => [
            'email' => $email,
            'nickname' => $nickname,
            'role' => $role,
            'status' => $status,
            'email_verified' => $email_verified,
            'password_changed' => !empty($password)
        ]
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => '用户信息更新成功'
    ]);
}

/**
 * 删除用户
 */
function deleteUser($db, $input) {
    $userId = intval($input['id'] ?? 0);

    if ($userId <= 0) {
        throw new Exception('无效的用户ID');
    }

    // 检查用户是否存在
    $user = $db->fetch("SELECT * FROM users WHERE id = ?", [$userId]);
    if (!$user) {
        throw new Exception('用户不存在');
    }

    // 获取当前用户信息
    $current_user = Auth::getCurrentUser();

    // 不能删除自己
    if ($userId == $current_user['id']) {
        throw new Exception('不能删除自己的账户');
    }

    // 开始事务
    $db->beginTransaction();

    try {
        // 删除用户相关的登录日志（设置user_id为NULL）
        $db->update("UPDATE login_logs SET user_id = NULL WHERE user_id = ?", [$userId]);

        // 删除用户相关的操作日志（设置user_id为NULL）
        $db->update("UPDATE operation_logs SET user_id = NULL WHERE user_id = ?", [$userId]);

        // 删除用户
        $affected = $db->delete("DELETE FROM users WHERE id = ?", [$userId]);

        if ($affected === 0) {
            throw new Exception('删除用户失败');
        }

        // 提交事务
        $db->commit();

        // 记录操作日志
        Auth::logOperation('user_delete', '删除用户', [
            'target_user_id' => $userId,
            'username' => $user['username'],
            'email' => $user['email']
        ]);

        echo json_encode([
            'success' => true,
            'message' => '用户删除成功'
        ]);

    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        throw $e;
    }
}

/**
 * 切换用户状态
 */
function toggleUserStatus($db, $input) {
    $userId = intval($input['id'] ?? 0);
    $newStatus = $input['status'] ?? '';

    if ($userId <= 0) {
        throw new Exception('无效的用户ID');
    }

    if (!in_array($newStatus, ['active', 'inactive', 'banned'])) {
        throw new Exception('无效的用户状态');
    }

    // 检查用户是否存在
    $user = $db->fetch("SELECT * FROM users WHERE id = ?", [$userId]);
    if (!$user) {
        throw new Exception('用户不存在');
    }

    // 获取当前用户信息
    $current_user = Auth::getCurrentUser();

    // 不能修改自己的状态
    if ($userId == $current_user['id']) {
        throw new Exception('不能修改自己的账户状态');
    }

    // 更新用户状态
    $affected = $db->update(
        "UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?",
        [$newStatus, $userId]
    );

    if ($affected === 0) {
        throw new Exception('更新用户状态失败');
    }

    // 状态名称映射
    $statusNames = [
        'active' => '启用',
        'inactive' => '设为未激活',
        'banned' => '禁用'
    ];

    $actionName = $statusNames[$newStatus] ?? '修改状态';

    // 记录操作日志
    Auth::logOperation('user_status_change', "用户状态变更：{$actionName}", [
        'target_user_id' => $userId,
        'username' => $user['username'],
        'old_status' => $user['status'],
        'new_status' => $newStatus
    ]);

    echo json_encode([
        'success' => true,
        'message' => "用户{$actionName}成功"
    ]);
}

/**
 * 获取未验证账户数量
 */
function getUnverifiedCount($db) {
    try {
        $result = $db->fetch("SELECT COUNT(*) as count FROM users WHERE email_verified = 0 AND role != 'admin'");

        echo json_encode([
            'success' => true,
            'count' => (int)$result['count']
        ]);

    } catch (Exception $e) {
        error_log("获取未验证账户数量失败: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => '获取未验证账户数量失败'
        ]);
    }
}

/**
 * 清理未验证账户
 */
function cleanupUnverifiedUsers($db) {
    try {
        // 开始事务
        $db->beginTransaction();

        // 获取要删除的用户列表（用于日志记录）
        $usersToDelete = $db->fetchAll("SELECT id, username, email FROM users WHERE email_verified = 0 AND role != 'admin'");

        if (empty($usersToDelete)) {
            $db->rollback();
            echo json_encode([
                'success' => true,
                'deleted_count' => 0,
                'message' => '没有未验证的账户需要清理'
            ]);
            return;
        }

        // 删除相关的登录日志
        $userIds = array_column($usersToDelete, 'id');
        if (!empty($userIds)) {
            $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
            $db->query("DELETE FROM login_logs WHERE user_id IN ({$placeholders})", $userIds);
            $db->query("DELETE FROM operation_logs WHERE user_id IN ({$placeholders})", $userIds);
        }

        // 删除未验证的用户账户
        $deletedCount = $db->query("DELETE FROM users WHERE email_verified = 0 AND role != 'admin'")->rowCount();

        // 提交事务
        $db->commit();

        // 记录操作日志
        Auth::logOperation('cleanup_unverified_users', '清理未验证账户', [
            'deleted_count' => $deletedCount,
            'deleted_users' => array_map(function($user) {
                return [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email']
                ];
            }, $usersToDelete)
        ]);

        echo json_encode([
            'success' => true,
            'deleted_count' => $deletedCount,
            'message' => "成功清理了 {$deletedCount} 个未验证账户"
        ]);

    } catch (Exception $e) {
        $db->rollback();
        error_log("清理未验证账户失败: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => '清理未验证账户失败：' . $e->getMessage()
        ]);
    }
}
?>
