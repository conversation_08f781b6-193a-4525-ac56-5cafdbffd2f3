<?php
/**
 * 📧 邮件模板管理 API 接口
 * 
 * 功能：处理邮件模板的保存、获取和管理
 */

header('Content-Type: application/json; charset=utf-8');

require_once '../config/database.php';
require_once '../auth/auth.php';

// 检查管理员权限
if (!Auth::isLoggedIn() || !Auth::hasRole(Auth::ROLE_ADMIN)) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => '权限不足'
    ]);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

try {
    switch ($action) {
        case 'save':
            $result = saveTemplate($input['filename'] ?? '', $input['content'] ?? '');
            break;
            
        case 'load':
            $result = loadTemplate($input['filename'] ?? '');
            break;
            
        case 'list':
            $result = listTemplates();
            break;
            
        default:
            throw new Exception('无效的操作');
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 保存邮件模板
 */
function saveTemplate($filename, $content) {
    if (empty($filename)) {
        throw new Exception('文件名不能为空');
    }
    
    if (empty($content)) {
        throw new Exception('模板内容不能为空');
    }
    
    // 验证文件名安全性
    if (!preg_match('/^[a-zA-Z0-9_-]+\.html$/', $filename)) {
        throw new Exception('无效的文件名格式');
    }
    
    // 模板目录路径
    $templateDir = __DIR__ . '/../users/muban/';
    
    // 确保目录存在
    if (!is_dir($templateDir)) {
        if (!mkdir($templateDir, 0755, true)) {
            throw new Exception('无法创建模板目录');
        }
    }
    
    $filePath = $templateDir . $filename;
    
    // 备份现有文件
    if (file_exists($filePath)) {
        $backupPath = $filePath . '.backup.' . date('Y-m-d_H-i-s');
        if (!copy($filePath, $backupPath)) {
            error_log("无法创建模板备份: $backupPath");
        }
    }
    
    // 保存文件
    if (file_put_contents($filePath, $content) === false) {
        throw new Exception('保存模板文件失败');
    }
    
    // 记录操作日志
    Auth::logOperation('template_save', '保存邮件模板', [
        'filename' => $filename,
        'size' => strlen($content)
    ]);
    
    return [
        'success' => true,
        'message' => '模板保存成功',
        'filename' => $filename
    ];
}

/**
 * 加载邮件模板
 */
function loadTemplate($filename) {
    if (empty($filename)) {
        throw new Exception('文件名不能为空');
    }
    
    // 验证文件名安全性
    if (!preg_match('/^[a-zA-Z0-9_-]+\.html$/', $filename)) {
        throw new Exception('无效的文件名格式');
    }
    
    $templateDir = __DIR__ . '/../users/muban/';
    $filePath = $templateDir . $filename;
    
    if (!file_exists($filePath)) {
        throw new Exception('模板文件不存在');
    }
    
    $content = file_get_contents($filePath);
    if ($content === false) {
        throw new Exception('读取模板文件失败');
    }
    
    return [
        'success' => true,
        'filename' => $filename,
        'content' => $content,
        'size' => strlen($content),
        'modified' => date('Y-m-d H:i:s', filemtime($filePath))
    ];
}

/**
 * 获取模板列表
 */
function listTemplates() {
    $templateDir = __DIR__ . '/../users/muban/';
    
    if (!is_dir($templateDir)) {
        return [
            'success' => true,
            'templates' => []
        ];
    }
    
    $templates = [];
    $files = glob($templateDir . '*.html');
    
    foreach ($files as $file) {
        $filename = basename($file);
        $templates[] = [
            'filename' => $filename,
            'name' => pathinfo($filename, PATHINFO_FILENAME),
            'size' => filesize($file),
            'modified' => date('Y-m-d H:i:s', filemtime($file))
        ];
    }
    
    // 按修改时间排序
    usort($templates, function($a, $b) {
        return strtotime($b['modified']) - strtotime($a['modified']);
    });
    
    return [
        'success' => true,
        'templates' => $templates
    ];
}

/**
 * 渲染邮件模板
 */
function renderTemplate($templateName, $variables = []) {
    $templateDir = __DIR__ . '/../users/muban/';
    $filePath = $templateDir . $templateName;
    
    if (!file_exists($filePath)) {
        throw new Exception("模板文件不存在: $templateName");
    }
    
    $content = file_get_contents($filePath);
    if ($content === false) {
        throw new Exception("读取模板文件失败: $templateName");
    }
    
    // 替换模板变量
    foreach ($variables as $key => $value) {
        $content = str_replace('{{' . $key . '}}', $value, $content);
    }
    
    return $content;
}

/**
 * 获取默认模板变量
 */
function getDefaultTemplateVariables() {
    require_once '../auth/SystemConfig.php';
    
    return [
        'SITE_NAME' => SystemConfig::getSiteName(),
        'SITE_DESCRIPTION' => SystemConfig::getSiteDescription(),
        'SITE_URL' => SystemConfig::getSiteUrl(),
        'ADMIN_EMAIL' => SystemConfig::getAdminEmail(),
        'CURRENT_YEAR' => date('Y'),
        'REQUEST_TIME' => date('Y-m-d H:i:s'),
        'REQUEST_IP' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];
}
?>
