/**
 * 🎯 功能中心页面脚本
 * 
 * 功能：
 * - 视图切换（卡片/列表）
 * - 搜索过滤
 * - 分类过滤
 * - 响应式适配
 * - SVG图标动态加载
 */

class FeaturesManager {
    constructor() {
        this.currentView = 'grid';
        this.currentCategory = 'all';
        this.searchQuery = '';
        this.features = [];
        
        this.init();
    }
    
    /**
     * 初始化
     */
    init() {
        this.initElements();
        this.loadSVGIcons();
        this.bindEvents();
        this.loadUserPreferences();
        this.updateDisplay();
        
        console.log('🎯 功能中心已初始化');
    }
    
    /**
     * 初始化DOM元素
     */
    initElements() {
        this.featuresGrid = document.getElementById('featuresGrid');
        this.searchInput = document.getElementById('searchInput');
        this.emptyState = document.getElementById('emptyState');
        this.viewButtons = document.querySelectorAll('.view-btn');
        this.categoryButtons = document.querySelectorAll('.category-btn');
        this.featureItems = document.querySelectorAll('.feature-item');
        
        // 收集功能数据
        this.features = Array.from(this.featureItems).map(item => ({
            element: item,
            category: item.dataset.category,
            status: item.dataset.status,
            name: item.dataset.name,
            description: item.dataset.description
        }));
    }
    
    /**
     * 加载SVG图标
     */
    loadSVGIcons() {
        const iconMap = {
            dashboard: '<rect x="3" y="3" width="7" height="7"/><rect x="14" y="3" width="7" height="7"/><rect x="14" y="14" width="7" height="7"/><rect x="3" y="14" width="7" height="7"/>',
            users: '<path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/>',
            user: '<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/>',
            settings: '<circle cx="12" cy="12" r="3"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>',
            shield: '<path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>',
            chart: '<line x1="18" y1="20" x2="18" y2="10"/><line x1="12" y1="20" x2="12" y2="4"/><line x1="6" y1="20" x2="6" y2="14"/>',
            bell: '<path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/><path d="M13.73 21a2 2 0 0 1-3.46 0"/>',
            folder: '<path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>',
            database: '<ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"/><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"/>',
            list: '<line x1="8" y1="6" x2="21" y2="6"/><line x1="8" y1="12" x2="21" y2="12"/><line x1="8" y1="18" x2="21" y2="18"/><line x1="3" y1="6" x2="3.01" y2="6"/><line x1="3" y1="12" x2="3.01" y2="12"/><line x1="3" y1="18" x2="3.01" y2="18"/>'
        };
        
        // 为每个图标元素设置SVG内容
        Object.keys(iconMap).forEach(iconName => {
            const iconElements = document.querySelectorAll(`.icon-${iconName}`);
            iconElements.forEach(element => {
                element.innerHTML = iconMap[iconName];
            });
        });
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 视图切换
        this.viewButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.currentTarget.dataset.view;
                this.switchView(view);
            });
        });
        
        // 分类过滤
        this.categoryButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const category = e.currentTarget.dataset.category;
                this.filterByCategory(category);
            });
        });
        
        // 搜索
        this.searchInput.addEventListener('input', (e) => {
            this.searchQuery = e.target.value.toLowerCase().trim();
            this.updateDisplay();
        });
        
        // 搜索框快捷键
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.searchInput.value = '';
                this.searchQuery = '';
                this.updateDisplay();
                this.searchInput.blur();
            }
        });
        
        // 响应式处理
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // 功能卡片点击事件
        this.featureItems.forEach(item => {
            const actionBtn = item.querySelector('.action-btn.primary, .list-action-btn.primary');
            if (actionBtn && !actionBtn.disabled) {
                // 添加键盘导航支持
                item.setAttribute('tabindex', '0');
                item.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        actionBtn.click();
                    }
                });
            }
        });
    }
    
    /**
     * 切换视图
     */
    switchView(view) {
        if (this.currentView === view) return;
        
        this.currentView = view;
        
        // 更新按钮状态
        this.viewButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.view === view);
        });
        
        // 更新网格类名
        this.featuresGrid.className = `features-grid view-${view}`;
        
        // 保存用户偏好
        this.saveUserPreferences();
        
        // 触发重新布局
        setTimeout(() => {
            this.updateDisplay();
        }, 100);
        
        console.log(`🔄 切换到${view === 'grid' ? '卡片' : '列表'}视图`);
    }
    
    /**
     * 按分类过滤
     */
    filterByCategory(category) {
        if (this.currentCategory === category) return;
        
        this.currentCategory = category;
        
        // 更新按钮状态
        this.categoryButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.category === category);
        });
        
        this.updateDisplay();
        
        console.log(`🏷️ 过滤分类: ${category}`);
    }
    
    /**
     * 更新显示
     */
    updateDisplay() {
        let visibleCount = 0;
        
        this.features.forEach(feature => {
            const matchesCategory = this.currentCategory === 'all' || feature.category === this.currentCategory;
            const matchesSearch = !this.searchQuery || 
                feature.name.includes(this.searchQuery) || 
                feature.description.includes(this.searchQuery);
            
            const isVisible = matchesCategory && matchesSearch;
            
            if (isVisible) {
                feature.element.style.display = '';
                visibleCount++;
            } else {
                feature.element.style.display = 'none';
            }
        });
        
        // 显示/隐藏空状态
        if (visibleCount === 0) {
            this.emptyState.style.display = 'block';
            this.featuresGrid.style.display = 'none';
        } else {
            this.emptyState.style.display = 'none';
            this.featuresGrid.style.display = '';
        }
        
        // 重新应用动画
        this.applyAnimations();
    }
    
    /**
     * 应用动画
     */
    applyAnimations() {
        const visibleItems = this.features.filter(f => f.element.style.display !== 'none');
        
        visibleItems.forEach((feature, index) => {
            feature.element.style.animationDelay = `${index * 0.1}s`;
            feature.element.style.animation = 'none';
            
            // 强制重绘
            feature.element.offsetHeight;
            
            feature.element.style.animation = 'fadeInUp 0.6s ease-out';
        });
    }
    
    /**
     * 响应式处理
     */
    handleResize() {
        const width = window.innerWidth;
        
        // 在小屏幕上自动切换到列表视图
        if (width <= 768 && this.currentView === 'grid') {
            // 可选：自动切换视图
            // this.switchView('list');
        }
        
        // 更新搜索框占位符
        if (width <= 480) {
            this.searchInput.placeholder = '搜索...';
        } else {
            this.searchInput.placeholder = '搜索功能...';
        }
    }
    
    /**
     * 加载用户偏好
     */
    loadUserPreferences() {
        try {
            const preferences = JSON.parse(localStorage.getItem('features_preferences') || '{}');
            
            if (preferences.view && ['grid', 'list'].includes(preferences.view)) {
                this.switchView(preferences.view);
            }
            
            if (preferences.category) {
                this.filterByCategory(preferences.category);
            }
        } catch (error) {
            console.warn('加载用户偏好失败:', error);
        }
    }
    
    /**
     * 保存用户偏好
     */
    saveUserPreferences() {
        try {
            const preferences = {
                view: this.currentView,
                category: this.currentCategory
            };
            
            localStorage.setItem('features_preferences', JSON.stringify(preferences));
        } catch (error) {
            console.warn('保存用户偏好失败:', error);
        }
    }
    
    /**
     * 搜索功能
     */
    search(query) {
        this.searchInput.value = query;
        this.searchQuery = query.toLowerCase().trim();
        this.updateDisplay();
    }
    
    /**
     * 重置过滤器
     */
    resetFilters() {
        this.searchInput.value = '';
        this.searchQuery = '';
        this.filterByCategory('all');
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        const stats = {
            total: this.features.length,
            visible: this.features.filter(f => f.element.style.display !== 'none').length,
            byCategory: {},
            byStatus: {}
        };
        
        this.features.forEach(feature => {
            // 按分类统计
            stats.byCategory[feature.category] = (stats.byCategory[feature.category] || 0) + 1;
            
            // 按状态统计
            stats.byStatus[feature.status] = (stats.byStatus[feature.status] || 0) + 1;
        });
        
        return stats;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待系统初始化完成
    const checkSystemReady = () => {
        if (window.appManager && window.appManager.initialized) {
            // 初始化功能中心
            window.featuresManager = new FeaturesManager();
            console.log('🎯 功能中心页面已完全初始化');
        } else {
            setTimeout(checkSystemReady, 100);
        }
    };
    
    checkSystemReady();
});

// 导出到全局
window.FeaturesManager = FeaturesManager;
