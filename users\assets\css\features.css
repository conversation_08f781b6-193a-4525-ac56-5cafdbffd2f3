/* ===== 功能中心页面样式 ===== */

/* 基础容器 */
.features-container {
    padding: var(--spacing-6);
    max-width: 1400px;
    margin: 0 auto;
}

/* ===== 页面头部 ===== */
.features-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-8);
    gap: var(--spacing-6);
}

.header-left {
    flex: 1;
}

.page-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-2) 0;
}

.title-icon {
    color: var(--primary-color);
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
    line-height: 1.5;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

/* ===== 搜索框 ===== */
.search-box {
    position: relative;
    min-width: 280px;
}

.search-icon {
    position: absolute;
    left: var(--spacing-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    pointer-events: none;
}

.search-box input {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-3) 2.75rem;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.search-box input::placeholder {
    color: var(--text-tertiary);
}

/* ===== 视图切换 ===== */
.view-toggle {
    display: flex;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 2px;
}

.view-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 36px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.view-btn:hover {
    color: var(--text-primary);
    background: var(--bg-primary);
}

.view-btn.active {
    color: var(--primary-color);
    background: var(--bg-primary);
    box-shadow: var(--shadow-sm);
}

/* ===== 分类过滤器 ===== */
.category-filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-4);
    border-bottom: 1px solid var(--border-light);
}

.category-btn {
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.category-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.category-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* ===== 功能网格/列表容器 ===== */
.features-content {
    position: relative;
}

.features-grid {
    transition: all 0.3s ease;
}

/* ===== 卡片视图 ===== */
.features-grid.view-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-6);
}

.feature-item {
    position: relative;
    transition: var(--transition);
}

.feature-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: var(--spacing-6);
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 56px;
    height: 56px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-4);
    transition: var(--transition);
}

.feature-content {
    flex: 1;
    margin-bottom: var(--spacing-4);
}

.feature-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-2) 0;
    line-height: 1.4;
}

.feature-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.6;
    margin: 0 0 var(--spacing-4) 0;
}

.feature-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    flex-wrap: wrap;
}

.feature-status {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.feature-status.status-active {
    background: #dcfce7;
    color: #166534;
}

.feature-status.status-coming_soon {
    background: #fef3c7;
    color: #92400e;
}

.feature-status.status-development {
    background: #dbeafe;
    color: #1e40af;
}

.feature-status.status-planning {
    background: #f3f4f6;
    color: #374151;
}

.feature-category {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    font-weight: 500;
}

.feature-actions {
    display: flex;
    gap: var(--spacing-2);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    flex: 1;
    justify-content: center;
}

.action-btn.primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.action-btn.primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-1px);
}

.action-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn.disabled:hover {
    transform: none;
}

/* ===== 列表视图 ===== */
.features-grid.view-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.features-grid.view-list .feature-card {
    display: none;
}

.feature-list-item {
    display: none;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-4);
    transition: var(--transition);
}

.features-grid.view-list .feature-list-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.feature-list-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.list-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.list-content {
    flex: 1;
    min-width: 0;
}

.list-main {
    margin-bottom: var(--spacing-2);
}

.list-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    line-height: 1.4;
}

.list-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
}

.list-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.list-category {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    font-weight: 500;
}

.list-status {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.list-actions {
    flex-shrink: 0;
}

.list-action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.list-action-btn.primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.list-action-btn.primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.list-action-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ===== 空状态 ===== */
.empty-state {
    text-align: center;
    padding: var(--spacing-12) var(--spacing-6);
    color: var(--text-secondary);
}

.empty-icon {
    margin-bottom: var(--spacing-4);
    color: var(--text-tertiary);
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-2) 0;
}

.empty-state p {
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.5;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
    .features-container {
        padding: var(--spacing-4);
    }
    
    .features-grid.view-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--spacing-4);
    }
    
    .search-box {
        min-width: 240px;
    }
}

@media (max-width: 768px) {
    .features-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-4);
    }
    
    .header-controls {
        flex-direction: column;
        gap: var(--spacing-3);
    }
    
    .search-box {
        min-width: auto;
    }
    
    .view-toggle {
        align-self: flex-end;
    }
    
    .features-grid.view-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }
    
    .feature-card {
        padding: var(--spacing-4);
    }
    
    .feature-list-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
    }
    
    .features-grid.view-list .feature-list-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .list-content {
        width: 100%;
    }
    
    .list-actions {
        width: 100%;
    }
    
    .list-action-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .features-container {
        padding: var(--spacing-3);
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .category-filters {
        gap: var(--spacing-1);
    }
    
    .category-btn {
        padding: 6px var(--spacing-3);
        font-size: 0.8rem;
    }
    
    .feature-card {
        padding: var(--spacing-3);
    }
    
    .feature-icon {
        width: 48px;
        height: 48px;
    }
    
    .feature-name {
        font-size: 1.125rem;
    }
}

/* ===== 暗色主题适配 ===== */
[data-theme="dark"] .feature-card:hover {
    border-color: var(--primary-color);
}

[data-theme="dark"] .feature-list-item:hover {
    border-color: var(--primary-color);
}

/* ===== 动画效果 ===== */
.feature-item {
    animation: fadeInUp 0.6s ease-out;
}

.feature-item:nth-child(1) { animation-delay: 0.1s; }
.feature-item:nth-child(2) { animation-delay: 0.2s; }
.feature-item:nth-child(3) { animation-delay: 0.3s; }
.feature-item:nth-child(4) { animation-delay: 0.4s; }
.feature-item:nth-child(5) { animation-delay: 0.5s; }
.feature-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 加载状态 ===== */
.features-grid.loading {
    opacity: 0.6;
    pointer-events: none;
}

.features-grid.loading .feature-item {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* ===== SVG图标样式 ===== */
.icon-dashboard { }
.icon-users { }
.icon-user { }
.icon-settings { }
.icon-shield { }
.icon-chart { }
.icon-bell { }
.icon-folder { }
.icon-database { }
.icon-list { }
