<?php
/**
 * 🏷️ 用户分组管理页面
 * 
 * 功能：管理用户分组、权限配置、会员等级设置
 */

session_start();

// 引入认证中间件
require_once '../auth/auth.php';
require_once '../auth/SystemConfig.php';
require_once 'classes/MemberManager.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 要求用户登录
Auth::requireLogin('../login.php');

// 检查管理员权限
if (!Auth::hasRole(Auth::ROLE_ADMIN)) {
    header('Location: index.php?error=permission_denied');
    exit;
}

// 获取当前用户信息
$current_user = Auth::getCurrentUser();
$page_title = '分组管理';

// 初始化会员管理器
$memberManager = new MemberManager();

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_SERVER['HTTP_X_REQUESTED_WITH'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'create_group':
                $data = [
                    'group_key' => $_POST['group_key'],
                    'group_name' => $_POST['group_name'],
                    'group_description' => $_POST['group_description'] ?? '',
                    'group_color' => $_POST['group_color'] ?? '#3b82f6',
                    'group_icon' => $_POST['group_icon'] ?? 'users',
                    'is_member_group' => intval($_POST['is_member_group'] ?? 0),
                    'member_level' => intval($_POST['member_level'] ?? 0),
                    'permissions' => json_decode($_POST['permissions'] ?? '{}', true),
                    'features' => json_decode($_POST['features'] ?? '{}', true),
                    'limits' => json_decode($_POST['limits'] ?? '{}', true),
                    'sort_order' => intval($_POST['sort_order'] ?? 0)
                ];
                
                $result = $memberManager->createUserGroup($data);
                echo json_encode(['success' => true, 'message' => '分组创建成功']);
                break;
                
            case 'update_group':
                $groupKey = $_POST['group_key'];
                $data = [
                    'group_name' => $_POST['group_name'],
                    'group_description' => $_POST['group_description'] ?? '',
                    'group_color' => $_POST['group_color'] ?? '#3b82f6',
                    'group_icon' => $_POST['group_icon'] ?? 'users',
                    'is_member_group' => intval($_POST['is_member_group'] ?? 0),
                    'member_level' => intval($_POST['member_level'] ?? 0),
                    'permissions' => json_decode($_POST['permissions'] ?? '{}', true),
                    'features' => json_decode($_POST['features'] ?? '{}', true),
                    'limits' => json_decode($_POST['limits'] ?? '{}', true),
                    'sort_order' => intval($_POST['sort_order'] ?? 0)
                ];
                
                $result = $memberManager->updateUserGroup($groupKey, $data);
                echo json_encode(['success' => true, 'message' => '分组更新成功']);
                break;
                
            case 'delete_group':
                $groupKey = $_POST['group_key'];
                $result = $memberManager->deleteUserGroup($groupKey);
                echo json_encode(['success' => true, 'message' => '分组删除成功']);
                break;
                
            default:
                throw new Exception('无效的操作');
        }
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// 获取所有用户分组
$userGroups = $memberManager->getUserGroups('');

// 获取统计信息
$stats = $memberManager->getMemberStats();

// 预定义的权限和功能选项
$availablePermissions = [
    'login' => '登录系统',
    'profile' => '个人资料',
    'basic_features' => '基础功能',
    'advanced_features' => '高级功能',
    'premium_features' => '高级会员功能',
    'admin_features' => '管理员功能'
];

$availableFeatures = [
    'dashboard' => '仪表盘',
    'profile' => '个人资料',
    'reports' => '报表中心',
    'advanced_tools' => '高级工具',
    'premium_tools' => '高级会员工具',
    'admin_panel' => '管理面板',
    'user_management' => '用户管理',
    'system_settings' => '系统设置'
];

$availableLimits = [
    'max_files' => '最大文件数',
    'max_storage' => '最大存储空间',
    'daily_actions' => '每日操作次数'
];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle($page_title); ?></title>
    
    <!-- 引入分组管理样式 -->
    <link rel="stylesheet" href="assets/css/member-groups.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- 头部 -->
        <?php include 'includes/header.php'; ?>
        
        <!-- 主要内容 -->
        <main class="main-content">
            <div class="groups-container">
                <!-- 页面标题 -->
                <div class="groups-header">
                    <div class="header-left">
                        <h1 class="page-title">
                            <svg class="title-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            </svg>
                            分组管理
                        </h1>
                        <p class="page-subtitle">配置用户分组、权限和会员等级</p>
                    </div>
                    
                    <div class="header-actions">
                        <button class="action-btn primary" onclick="showCreateGroupModal()">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="5" x2="12" y2="19"/>
                                <line x1="5" y1="12" x2="19" y2="12"/>
                            </svg>
                            新建分组
                        </button>
                        <a href="members.php" class="action-btn secondary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                            会员管理
                        </a>
                    </div>
                </div>
                
                <!-- 分组列表 -->
                <div class="groups-grid">
                    <?php foreach ($userGroups as $group): ?>
                    <div class="group-card" data-group-key="<?php echo $group['group_key']; ?>">
                        <div class="group-header">
                            <div class="group-icon" style="background: <?php echo $group['group_color']; ?>20;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="<?php echo $group['group_color']; ?>" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                </svg>
                            </div>
                            
                            <div class="group-info">
                                <h3 class="group-name"><?php echo htmlspecialchars($group['group_name']); ?></h3>
                                <p class="group-description"><?php echo htmlspecialchars($group['group_description']); ?></p>
                            </div>
                            
                            <div class="group-badges">
                                <?php if ($group['is_member_group']): ?>
                                <span class="badge member-badge">会员分组</span>
                                <?php endif; ?>
                                <span class="badge level-badge">等级 <?php echo $group['member_level']; ?></span>
                            </div>
                        </div>
                        
                        <div class="group-stats">
                            <?php
                            $userCount = 0;
                            foreach ($stats['group_stats'] as $stat) {
                                if ($stat['group_name'] === $group['group_name']) {
                                    $userCount = $stat['user_count'];
                                    break;
                                }
                            }
                            ?>
                            <div class="stat-item">
                                <span class="stat-number"><?php echo $userCount; ?></span>
                                <span class="stat-label">用户数</span>
                            </div>
                            
                            <div class="stat-item">
                                <?php
                                $permissions = json_decode($group['permissions'] ?? '{}', true);
                                $permissionCount = count(array_filter($permissions));
                                ?>
                                <span class="stat-number"><?php echo $permissionCount; ?></span>
                                <span class="stat-label">权限数</span>
                            </div>
                            
                            <div class="stat-item">
                                <?php
                                $features = json_decode($group['features'] ?? '{}', true);
                                $featureCount = count(array_filter($features));
                                ?>
                                <span class="stat-number"><?php echo $featureCount; ?></span>
                                <span class="stat-label">功能数</span>
                            </div>
                        </div>
                        
                        <div class="group-permissions">
                            <h4>权限配置</h4>
                            <div class="permission-tags">
                                <?php
                                $permissions = json_decode($group['permissions'] ?? '{}', true);
                                foreach ($permissions as $key => $value) {
                                    if ($value && isset($availablePermissions[$key])) {
                                        echo '<span class="permission-tag">' . htmlspecialchars($availablePermissions[$key]) . '</span>';
                                    }
                                }
                                ?>
                            </div>
                        </div>
                        
                        <div class="group-actions">
                            <button class="action-btn edit-group" data-group-key="<?php echo $group['group_key']; ?>">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                </svg>
                                编辑
                            </button>
                            
                            <?php if ($group['group_key'] !== 'default' && $group['group_key'] !== 'admin'): ?>
                            <button class="action-btn delete-group" data-group-key="<?php echo $group['group_key']; ?>">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="3,6 5,6 21,6"/>
                                    <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                                </svg>
                                删除
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </main>
        
        <!-- 页脚 -->
        <?php include 'includes/footer.php'; ?>
    </div>
    
    <!-- 分组编辑模态框 -->
    <div id="groupModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="modalTitle">新建分组</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="groupForm">
                    <input type="hidden" id="modalGroupKey" name="group_key">
                    <input type="hidden" id="modalAction" name="action" value="create_group">
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="groupKey">分组标识 *</label>
                            <input type="text" id="groupKey" name="group_key" required 
                                   placeholder="如：vip_gold" pattern="[a-z0-9_]+" 
                                   title="只能包含小写字母、数字和下划线">
                            <small class="form-help">用于系统内部识别，创建后不可修改</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="groupName">分组名称 *</label>
                            <input type="text" id="groupName" name="group_name" required placeholder="如：黄金VIP">
                        </div>
                        
                        <div class="form-group">
                            <label for="groupColor">分组颜色</label>
                            <input type="color" id="groupColor" name="group_color" value="#3b82f6">
                        </div>
                        
                        <div class="form-group">
                            <label for="memberLevel">会员等级</label>
                            <input type="number" id="memberLevel" name="member_level" min="0" max="999" value="0">
                            <small class="form-help">0=普通用户，1-998=会员等级，999=管理员</small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="groupDescription">分组描述</label>
                        <textarea id="groupDescription" name="group_description" rows="3" 
                                  placeholder="描述此分组的用途和特点"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="isMemberGroup" name="is_member_group" value="1">
                            这是会员分组
                        </label>
                    </div>
                    
                    <div class="permissions-section">
                        <h4>权限配置</h4>
                        <div class="permission-grid">
                            <?php foreach ($availablePermissions as $key => $label): ?>
                            <label class="permission-item">
                                <input type="checkbox" name="permissions[<?php echo $key; ?>]" value="1">
                                <span><?php echo htmlspecialchars($label); ?></span>
                            </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="features-section">
                        <h4>功能权限</h4>
                        <div class="feature-grid">
                            <?php foreach ($availableFeatures as $key => $label): ?>
                            <label class="feature-item">
                                <input type="checkbox" name="features[<?php echo $key; ?>]" value="1">
                                <span><?php echo htmlspecialchars($label); ?></span>
                            </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="limits-section">
                        <h4>限制配置</h4>
                        <div class="limits-grid">
                            <div class="form-group">
                                <label for="maxFiles">最大文件数</label>
                                <input type="number" id="maxFiles" name="limits[max_files]" min="-1" placeholder="10">
                                <small class="form-help">-1表示无限制</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="maxStorage">最大存储空间</label>
                                <input type="text" id="maxStorage" name="limits[max_storage]" placeholder="100MB">
                                <small class="form-help">如：100MB, 1GB, unlimited</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="dailyActions">每日操作次数</label>
                                <input type="number" id="dailyActions" name="limits[daily_actions]" min="-1" placeholder="100">
                                <small class="form-help">-1表示无限制</small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn secondary" onclick="closeGroupModal()">取消</button>
                <button type="button" class="btn primary" onclick="saveGroup()">保存</button>
            </div>
        </div>
    </div>
    
    <!-- 引入分组管理脚本 -->
    <script src="assets/js/member-groups.js"></script>
    
    <!-- 内联数据 -->
    <script>
        window.groupsData = {
            availablePermissions: <?php echo json_encode($availablePermissions); ?>,
            availableFeatures: <?php echo json_encode($availableFeatures); ?>,
            availableLimits: <?php echo json_encode($availableLimits); ?>,
            userGroups: <?php echo json_encode($userGroups); ?>
        };
    </script>
</body>
</html>
