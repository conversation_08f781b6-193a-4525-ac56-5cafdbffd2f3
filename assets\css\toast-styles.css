/**
 * Toast通知样式系统
 * 支持多种样式风格的Toast通知
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

/* ===== Toast容器 ===== */
.toast-container {
    position: fixed;
    top: 80px; /* 导航栏高度64px + 16px间距 */
    right: var(--spacing-6, 1.5rem);
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2, 0.5rem);
    max-width: 400px;
    pointer-events: none;
}

/* ===== Toast基础样式 ===== */
.toast {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3, 0.75rem);
    padding: var(--spacing-4, 1rem);
    border-radius: 8px;
    box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
    backdrop-filter: blur(10px);
    pointer-events: auto;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 100%;
    word-wrap: break-word;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.toast-icon svg {
    width: 100%;
    height: 100%;
    stroke-width: 2.5;
}

.toast-content {
    flex: 1;
    min-width: 0;
}

.toast-message {
    font-size: 0.875rem;
    line-height: 1.5;
    font-weight: 500;
    margin: 0;
}

.toast-close {
    flex-shrink: 0;
    background: none;
    border: none;
    cursor: pointer;
    padding: 2px;
    border-radius: 4px;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.toast-close:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
}

/* ===== 现代风格 (Modern) ===== */
.toast-style-modern {
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #e5e7eb);
    color: var(--text-primary, #1f2937);
}

.toast-style-modern .toast-icon {
    color: var(--text-secondary, #6b7280);
}

.toast-style-modern.toast-success {
    background: var(--success-color, #10b981);
    color: white;
    border-color: var(--success-color, #10b981);
}

.toast-style-modern.toast-success .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.toast-style-modern.toast-error {
    background: var(--error-color, #ef4444);
    color: white;
    border-color: var(--error-color, #ef4444);
}

.toast-style-modern.toast-error .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.toast-style-modern.toast-warning {
    background: var(--warning-color, #f59e0b);
    color: white;
    border-color: var(--warning-color, #f59e0b);
}

.toast-style-modern.toast-warning .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.toast-style-modern.toast-info {
    background: var(--primary-color, #3b82f6);
    color: white;
    border-color: var(--primary-color, #3b82f6);
}

.toast-style-modern.toast-info .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* ===== 简约风格 (Minimal) ===== */
.toast-style-minimal {
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #e5e7eb);
    color: var(--text-primary, #1f2937);
    border-radius: 4px;
    box-shadow: var(--shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
}

.toast-style-minimal .toast-icon {
    color: var(--text-secondary, #6b7280);
}

.toast-style-minimal.toast-success .toast-icon {
    color: var(--success-color, #10b981);
}

.toast-style-minimal.toast-error .toast-icon {
    color: var(--error-color, #ef4444);
}

.toast-style-minimal.toast-warning .toast-icon {
    color: var(--warning-color, #f59e0b);
}

.toast-style-minimal.toast-info .toast-icon {
    color: var(--primary-color, #3b82f6);
}

/* ===== 圆角风格 (Rounded) ===== */
.toast-style-rounded {
    border-radius: 20px;
    padding: var(--spacing-4, 1rem) var(--spacing-5, 1.25rem);
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #e5e7eb);
    color: var(--text-primary, #1f2937);
}

.toast-style-rounded .toast-icon {
    color: var(--text-secondary, #6b7280);
}

.toast-style-rounded.toast-success {
    background: var(--success-color, #10b981);
    color: white;
    border-color: var(--success-color, #10b981);
}

.toast-style-rounded.toast-success .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.toast-style-rounded.toast-error {
    background: var(--error-color, #ef4444);
    color: white;
    border-color: var(--error-color, #ef4444);
}

.toast-style-rounded.toast-error .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.toast-style-rounded.toast-warning {
    background: var(--warning-color, #f59e0b);
    color: white;
    border-color: var(--warning-color, #f59e0b);
}

.toast-style-rounded.toast-warning .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.toast-style-rounded.toast-info {
    background: var(--primary-color, #3b82f6);
    color: white;
    border-color: var(--primary-color, #3b82f6);
}

.toast-style-rounded.toast-info .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* ===== 扁平风格 (Flat) ===== */
.toast-style-flat {
    border-radius: 0;
    box-shadow: none;
    border: none;
    border-left: 4px solid;
}

.toast-style-flat.toast-success {
    background: var(--bg-primary, #ffffff);
    color: var(--text-primary, #1f2937);
    border-left-color: var(--success-color, #10b981);
}

.toast-style-flat.toast-error {
    background: var(--bg-primary, #ffffff);
    color: var(--text-primary, #1f2937);
    border-left-color: var(--error-color, #ef4444);
}

.toast-style-flat.toast-warning {
    background: var(--bg-primary, #ffffff);
    color: var(--text-primary, #1f2937);
    border-left-color: var(--warning-color, #f59e0b);
}

.toast-style-flat.toast-info {
    background: var(--bg-primary, #ffffff);
    color: var(--text-primary, #1f2937);
    border-left-color: var(--primary-color, #3b82f6);
}

.toast-style-flat .toast-icon {
    color: var(--text-secondary, #6b7280);
}

.toast-style-flat.toast-success .toast-icon {
    color: var(--success-color, #10b981);
}

.toast-style-flat.toast-error .toast-icon {
    color: var(--error-color, #ef4444);
}

.toast-style-flat.toast-warning .toast-icon {
    color: var(--warning-color, #f59e0b);
}

.toast-style-flat.toast-info .toast-icon {
    color: var(--primary-color, #3b82f6);
}

/* ===== 渐变风格 (Gradient) ===== */
.toast-style-gradient {
    border: none;
    color: white;
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #e5e7eb);
    color: var(--text-primary, #1f2937);
}

.toast-style-gradient .toast-icon {
    color: var(--text-secondary, #6b7280);
}

.toast-style-gradient.toast-success {
    background: linear-gradient(135deg, var(--success-color, #10b981), #059669);
    color: white;
    border: none;
}

.toast-style-gradient.toast-success .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4));
}

.toast-style-gradient.toast-error {
    background: linear-gradient(135deg, var(--error-color, #ef4444), #dc2626);
    color: white;
    border: none;
}

.toast-style-gradient.toast-error .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4));
}

.toast-style-gradient.toast-warning {
    background: linear-gradient(135deg, var(--warning-color, #f59e0b), #d97706);
    color: white;
    border: none;
}

.toast-style-gradient.toast-warning .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4));
}

.toast-style-gradient.toast-info {
    background: linear-gradient(135deg, var(--primary-color, #3b82f6), #2563eb);
    color: white;
    border: none;
}

.toast-style-gradient.toast-info .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4));
}

/* ===== 阴影风格 (Shadow) ===== */
.toast-style-shadow {
    background: var(--bg-primary, #ffffff);
    color: var(--text-primary, #1f2937);
    border: 1px solid var(--border-light, #f3f4f6);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.toast-style-shadow .toast-icon {
    color: var(--text-secondary, #6b7280);
}

.toast-style-shadow.toast-success .toast-icon {
    color: var(--success-color, #10b981);
}

.toast-style-shadow.toast-error .toast-icon {
    color: var(--error-color, #ef4444);
}

.toast-style-shadow.toast-warning .toast-icon {
    color: var(--warning-color, #f59e0b);
}

.toast-style-shadow.toast-info .toast-icon {
    color: var(--primary-color, #3b82f6);
}

/* ===== 暗色主题适配 ===== */
[data-theme="dark"] .toast-style-modern,
[data-theme="dark"] .toast-style-minimal,
[data-theme="dark"] .toast-style-flat,
[data-theme="dark"] .toast-style-shadow,
[data-theme="dark"] .toast-style-rounded,
[data-theme="dark"] .toast-style-gradient {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .toast-style-shadow {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* 暗色主题下的图标优化 */
[data-theme="dark"] .toast-style-modern .toast-icon,
[data-theme="dark"] .toast-style-minimal .toast-icon,
[data-theme="dark"] .toast-style-flat .toast-icon,
[data-theme="dark"] .toast-style-shadow .toast-icon,
[data-theme="dark"] .toast-style-rounded .toast-icon,
[data-theme="dark"] .toast-style-gradient .toast-icon {
    color: var(--text-secondary);
}

/* 暗色主题下有背景色的Toast图标保持白色 */
[data-theme="dark"] .toast-style-modern.toast-success .toast-icon,
[data-theme="dark"] .toast-style-modern.toast-error .toast-icon,
[data-theme="dark"] .toast-style-modern.toast-warning .toast-icon,
[data-theme="dark"] .toast-style-modern.toast-info .toast-icon,
[data-theme="dark"] .toast-style-rounded.toast-success .toast-icon,
[data-theme="dark"] .toast-style-rounded.toast-error .toast-icon,
[data-theme="dark"] .toast-style-rounded.toast-warning .toast-icon,
[data-theme="dark"] .toast-style-rounded.toast-info .toast-icon,
[data-theme="dark"] .toast-style-gradient.toast-success .toast-icon,
[data-theme="dark"] .toast-style-gradient.toast-error .toast-icon,
[data-theme="dark"] .toast-style-gradient.toast-warning .toast-icon,
[data-theme="dark"] .toast-style-gradient.toast-info .toast-icon {
    color: white;
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.5));
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .toast-container {
        top: 80px; /* 移动端导航栏高度64px + 16px间距 */
        right: var(--spacing-4, 1rem);
        left: var(--spacing-4, 1rem);
        max-width: none;
    }

    .toast {
        padding: var(--spacing-3, 0.75rem);
    }

    .toast-message {
        font-size: 0.8rem;
    }
}

/* ===== 图标增强效果 ===== */
.toast-icon:hover {
    transform: scale(1.1);
}

/* 为不同类型的Toast添加特殊的图标效果 */
.toast-success .toast-icon {
    animation: iconPulse 2s ease-in-out infinite;
}

.toast-error .toast-icon {
    animation: iconShake 0.5s ease-in-out;
}

.toast-warning .toast-icon {
    animation: iconBounce 1s ease-in-out;
}

.toast-info .toast-icon {
    animation: iconGlow 2s ease-in-out infinite alternate;
}

/* ===== 动画效果 ===== */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes iconPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes iconShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

@keyframes iconBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

@keyframes iconGlow {
    from { filter: brightness(1); }
    to { filter: brightness(1.2); }
}
