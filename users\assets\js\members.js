/**
 * 👑 会员管理页面脚本
 * 
 * 功能：
 * - 会员信息编辑
 * - 分组管理
 * - 积分调整
 * - 权限管理
 */

class MembersManager {
    constructor() {
        this.currentUserId = null;
        this.modal = null;
        
        this.init();
    }
    
    /**
     * 初始化
     */
    init() {
        this.initElements();
        this.bindEvents();
        
        console.log('👑 会员管理已初始化');
    }
    
    /**
     * 初始化DOM元素
     */
    initElements() {
        this.modal = document.getElementById('memberModal');
        this.memberForm = document.getElementById('memberForm');
        this.editButtons = document.querySelectorAll('.edit-user');
        this.manageButtons = document.querySelectorAll('.manage-member');
        this.modalClose = document.querySelector('.modal-close');
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 编辑用户按钮
        this.editButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.currentTarget.dataset.userId;
                this.editUser(userId);
            });
        });
        
        // 会员管理按钮
        this.manageButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.currentTarget.dataset.userId;
                this.manageMember(userId);
            });
        });
        
        // 模态框关闭
        if (this.modalClose) {
            this.modalClose.addEventListener('click', () => {
                this.closeModal();
            });
        }
        
        // 点击模态框外部关闭
        if (this.modal) {
            this.modal.addEventListener('click', (e) => {
                if (e.target === this.modal) {
                    this.closeModal();
                }
            });
        }
        
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal && this.modal.classList.contains('show')) {
                this.closeModal();
            }
        });
        
        // 表单提交
        if (this.memberForm) {
            this.memberForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveMemberChanges();
            });
        }
    }
    
    /**
     * 编辑用户
     */
    editUser(userId) {
        // 这里可以跳转到用户编辑页面或打开编辑模态框
        window.location.href = `user-edit.php?id=${userId}`;
    }
    
    /**
     * 管理会员
     */
    manageMember(userId) {
        this.currentUserId = userId;
        this.loadUserData(userId);
        this.showModal();
    }
    
    /**
     * 加载用户数据
     */
    async loadUserData(userId) {
        try {
            // 从表格行中获取用户数据
            const userRow = document.querySelector(`[data-user-id="${userId}"]`);
            if (!userRow) return;
            
            // 获取当前分组
            const groupBadge = userRow.querySelector('.group-badge');
            const currentGroup = groupBadge ? groupBadge.textContent.trim() : '';
            
            // 获取会员到期时间
            const memberExpiry = userRow.querySelector('.member-expiry');
            const expiryText = memberExpiry ? memberExpiry.textContent.replace('到期：', '').trim() : '';
            
            // 设置表单值
            document.getElementById('modalUserId').value = userId;
            
            // 设置分组选择
            const groupSelect = document.getElementById('userGroup');
            if (groupSelect) {
                // 根据当前分组名称找到对应的option
                for (let option of groupSelect.options) {
                    if (option.textContent.trim() === currentGroup) {
                        option.selected = true;
                        break;
                    }
                }
            }
            
            // 设置到期时间
            const expiryInput = document.getElementById('memberExpiry');
            if (expiryInput && expiryText && expiryText !== '永久') {
                // 转换日期格式为datetime-local格式
                const date = new Date(expiryText);
                if (!isNaN(date.getTime())) {
                    const localDateTime = new Date(date.getTime() - date.getTimezoneOffset() * 60000)
                        .toISOString().slice(0, 16);
                    expiryInput.value = localDateTime;
                }
            }
            
            // 清空积分调整字段
            document.getElementById('pointsChange').value = '';
            document.getElementById('pointsReason').value = '';
            
        } catch (error) {
            console.error('加载用户数据失败:', error);
            this.showToast('加载用户数据失败', 'error');
        }
    }
    
    /**
     * 显示模态框
     */
    showModal() {
        if (this.modal) {
            this.modal.classList.add('show');
            document.body.style.overflow = 'hidden';
            
            // 聚焦到第一个输入框
            const firstInput = this.modal.querySelector('input, select');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }
        }
    }
    
    /**
     * 关闭模态框
     */
    closeModal() {
        if (this.modal) {
            this.modal.classList.remove('show');
            document.body.style.overflow = '';
            this.currentUserId = null;
        }
    }
    
    /**
     * 保存会员变更
     */
    async saveMemberChanges() {
        if (!this.currentUserId) return;
        
        const formData = new FormData(this.memberForm);
        const changes = [];
        
        try {
            // 更新用户分组
            const groupKey = formData.get('group_key');
            if (groupKey) {
                await this.updateUserGroup(this.currentUserId, groupKey);
                changes.push('分组');
            }
            
            // 设置会员到期时间
            const expireDate = formData.get('expire_date');
            if (expireDate) {
                await this.setMemberExpiry(this.currentUserId, expireDate);
                changes.push('会员期限');
            }
            
            // 调整积分
            const pointsChange = parseInt(formData.get('points_change'));
            const reason = formData.get('reason');
            if (pointsChange && reason) {
                await this.adjustPoints(this.currentUserId, pointsChange, reason);
                changes.push('积分');
            }
            
            if (changes.length > 0) {
                this.showToast(`${changes.join('、')}更新成功`, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                this.showToast('没有需要更新的内容', 'info');
            }
            
            this.closeModal();
            
        } catch (error) {
            console.error('保存失败:', error);
            this.showToast(error.message || '保存失败', 'error');
        }
    }
    
    /**
     * 更新用户分组
     */
    async updateUserGroup(userId, groupKey) {
        const response = await fetch('members.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({
                action: 'update_user_group',
                user_id: userId,
                group_key: groupKey
            })
        });
        
        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message);
        }
        
        return data;
    }
    
    /**
     * 设置会员到期时间
     */
    async setMemberExpiry(userId, expireDate) {
        const response = await fetch('members.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({
                action: 'set_member_expiry',
                user_id: userId,
                expire_date: expireDate
            })
        });
        
        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message);
        }
        
        return data;
    }
    
    /**
     * 调整积分
     */
    async adjustPoints(userId, pointsChange, reason) {
        const response = await fetch('members.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({
                action: 'adjust_points',
                user_id: userId,
                points_change: pointsChange,
                reason: reason
            })
        });
        
        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message);
        }
        
        return data;
    }
    
    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
        // 检查是否有全局的toast函数
        if (typeof window.showToast === 'function') {
            window.showToast(message, type);
            return;
        }
        
        // 简单的alert替代
        alert(message);
    }
    
    /**
     * 批量操作
     */
    batchOperation(operation, userIds) {
        // 预留批量操作功能
        console.log(`批量操作: ${operation}`, userIds);
    }
    
    /**
     * 导出用户数据
     */
    exportUsers(format = 'csv') {
        const params = new URLSearchParams(window.location.search);
        params.set('export', format);
        
        window.location.href = `members.php?${params.toString()}`;
    }
    
    /**
     * 刷新统计数据
     */
    refreshStats() {
        // 重新加载页面或通过AJAX更新统计数据
        window.location.reload();
    }
}

// 全局函数，供HTML调用
window.closeModal = function() {
    if (window.membersManager) {
        window.membersManager.closeModal();
    }
};

window.saveMemberChanges = function() {
    if (window.membersManager) {
        window.membersManager.saveMemberChanges();
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待系统初始化完成
    const checkSystemReady = () => {
        if (window.appManager && window.appManager.initialized) {
            // 初始化会员管理
            window.membersManager = new MembersManager();
            console.log('👑 会员管理页面已完全初始化');
        } else {
            setTimeout(checkSystemReady, 100);
        }
    };
    
    checkSystemReady();
});

// 导出到全局
window.MembersManager = MembersManager;
