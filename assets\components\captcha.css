/* 🔐 验证码组件样式 */

.captcha-container {
    margin: 15px 0;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    transition: all 0.3s ease;
}

.captcha-container.success {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.05);
}

.captcha-container.error {
    border-color: var(--danger-color);
    background: rgba(239, 68, 68, 0.05);
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 数学计算验证码 */
.captcha-math {
    display: flex;
    align-items: center;
    gap: 15px;
}

.captcha-question {
    flex: 1;
}

.math-expression {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    background: var(--bg-primary);
    padding: 10px 15px;
    border-radius: 6px;
    border: 1px solid var(--border-light);
    display: inline-block;
    min-width: 120px;
    text-align: center;
}

.captcha-input {
    display: flex;
    align-items: center;
    gap: 10px;
}

.captcha-answer {
    width: 100px;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 16px;
    text-align: center;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.captcha-answer:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.captcha-container.success .captcha-answer {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.05);
}

.captcha-container.error .captcha-answer {
    border-color: var(--danger-color);
    background: rgba(239, 68, 68, 0.05);
}

.captcha-refresh {
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.captcha-refresh:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--primary-color);
}

/* 字母数字验证码 */
.captcha-alphanumeric {
    display: flex;
    align-items: center;
    gap: 15px;
}

.captcha-image {
    flex: 1;
}

.code-display {
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 3px;
    color: var(--primary-color);
    background: linear-gradient(45deg, #f8fafc, #e2e8f0);
    padding: 12px 20px;
    border-radius: 6px;
    border: 1px solid var(--border-light);
    text-align: center;
    font-family: 'Courier New', monospace;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    user-select: none;
    min-width: 140px;
}

.captcha-alphanumeric .captcha-answer {
    width: 120px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* 滑块验证码 */
.captcha-slider {
    display: flex;
    align-items: center;
    gap: 15px;
}

.slider-track {
    flex: 1;
    height: 40px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.slider-bg {
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--bg-tertiary), var(--primary-light));
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.slider-track.success .slider-bg {
    background: linear-gradient(90deg, var(--success-light), var(--success-color));
}

.slider-text {
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    user-select: none;
}

.slider-track.success .slider-text {
    color: white;
    font-weight: 600;
}

.slider-thumb {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 36px;
    height: 36px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
    transition: left 0.3s ease, box-shadow 0.3s ease;
    color: var(--text-secondary);
}

.slider-thumb:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.slider-thumb:active {
    cursor: grabbing;
    box-shadow: 0 6px 16px rgba(0,0,0,0.25);
}

.slider-track.success .slider-thumb {
    background: var(--success-color);
    color: white;
    left: calc(100% - 38px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .captcha-math,
    .captcha-alphanumeric,
    .captcha-slider {
        flex-direction: column;
        gap: 10px;
    }
    
    .captcha-input {
        justify-content: center;
    }
    
    .captcha-answer {
        width: 120px;
    }
    
    .slider-track {
        width: 100%;
        max-width: 300px;
    }
    
    .math-expression,
    .code-display {
        min-width: auto;
        width: 100%;
        max-width: 200px;
    }
}

/* 暗色主题适配 */
[data-theme="dark"] .captcha-container {
    background: rgba(255, 255, 255, 0.02);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .math-expression,
[data-theme="dark"] .code-display {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

[data-theme="dark"] .captcha-answer {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

[data-theme="dark"] .captcha-refresh {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .slider-track {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .slider-bg {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.05), rgba(59, 130, 246, 0.2));
}

/* 加载状态 */
.captcha-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--text-secondary);
    font-style: italic;
}

.captcha-loading::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
