<?php
/**
 * 📋 仪表盘头部导航栏组件
 * 
 * 功能：顶部导航栏，包含logo、导航菜单、用户信息等
 */

// 确保用户已登录
if (!Auth::isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

$current_user = Auth::getCurrentUser();

// 引入系统配置
require_once '../auth/SystemConfig.php';
?>

<header class="dashboard-header">
    <div class="header-container">
        <!-- 移动端汉堡菜单按钮 -->
        <button class="mobile-menu-toggle" aria-label="打开菜单">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
        </button>

        <!-- Logo和系统名称 -->
        <div class="header-brand">
            <div class="brand-logo">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                </svg>
            </div>
            <div class="brand-text">
                <h1 class="brand-title"><?php echo SystemConfig::getSiteName(); ?></h1>
                <span class="brand-subtitle"><?php echo SystemConfig::getSiteDescription(); ?></span>
            </div>
        </div>

        <!-- 移动端页面标题 -->
        <div class="mobile-page-title">
            <h1><?php
                $current_page = basename($_SERVER['PHP_SELF'], '.php');
                $page_titles = [
                    'index' => '仪表盘',
                    'features' => '功能中心',
                    'profile' => '个人资料',
                    'logs' => '日志查看',
                    'users' => '用户管理',
                    'membership' => '会员管理',
                    'settings' => '系统设置'
                ];
                echo $page_titles[$current_page] ?? '管理系统';
            ?></h1>
        </div>

        <!-- 主导航菜单 -->
        <nav class="header-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="index.php" class="nav-link <?php echo ($current_page === 'index') ? 'active' : ''; ?>">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                        </svg>
                        仪表盘
                    </a>
                </li>

                <?php if (Auth::hasRole(Auth::ROLE_ADMIN)): ?>
                <li class="nav-item">
                    <a href="features.php" class="nav-link <?php echo ($current_page === 'features') ? 'active' : ''; ?>">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="7" height="7"/>
                            <rect x="14" y="3" width="7" height="7"/>
                            <rect x="14" y="14" width="7" height="7"/>
                            <rect x="3" y="14" width="7" height="7"/>
                        </svg>
                        功能中心
                    </a>
                </li>
                <li class="nav-item">
                    <a href="users.php" class="nav-link <?php echo ($current_page === 'users') ? 'active' : ''; ?>">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                            <circle cx="9" cy="7" r="4"/>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                        </svg>
                        用户管理
                    </a>
                </li>

                <li class="nav-item">
                    <a href="membership.php" class="nav-link <?php echo ($current_page === 'membership') ? 'active' : ''; ?>">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        会员管理
                    </a>
                </li>

                <li class="nav-item">
                    <a href="settings.php" class="nav-link <?php echo ($current_page === 'settings') ? 'active' : ''; ?>">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                        </svg>
                        系统设置
                    </a>
                </li>
                <?php endif; ?>
                
                <li class="nav-item">
                    <a href="profile.php" class="nav-link <?php echo ($current_page === 'profile') ? 'active' : ''; ?>">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                        个人资料
                    </a>
                </li>
            </ul>
        </nav>
        
        <!-- 右侧工具栏 -->
        <div class="header-toolbar">
            <!-- 通知按钮 -->
            <div class="notification-menu">
                <button class="toolbar-btn notification-btn" title="通知">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                        <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
                    </svg>
                    <span class="notification-badge">3</span>
                </button>

                <div class="notification-dropdown">
                    <div class="notification-header">
                        <h4>通知</h4>
                        <button class="mark-all-read">全部已读</button>
                    </div>
                    <div class="notification-list">
                        <div class="notification-item unread">
                            <div class="notification-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                    <circle cx="8.5" cy="7" r="4"/>
                                    <line x1="20" y1="8" x2="20" y2="14"/>
                                    <line x1="23" y1="11" x2="17" y2="11"/>
                                </svg>
                            </div>
                            <div class="notification-content">
                                <p>新用户注册</p>
                                <span class="notification-time">5分钟前</span>
                            </div>
                        </div>
                        <div class="notification-item">
                            <div class="notification-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                </svg>
                            </div>
                            <div class="notification-content">
                                <p>系统备份完成</p>
                                <span class="notification-time">1小时前</span>
                            </div>
                        </div>
                        <div class="notification-item">
                            <div class="notification-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                            </div>
                            <div class="notification-content">
                                <p>系统设置已更新</p>
                                <span class="notification-time">3小时前</span>
                            </div>
                        </div>
                    </div>
                    <div class="notification-footer">
                        <a href="#" class="view-all">查看全部通知</a>
                    </div>
                </div>
            </div>

            <!-- 用户菜单 -->
            <div class="user-menu">
                <button class="user-menu-trigger">
                    <div class="user-avatar">
                        <?php
                        // 生成用户头像首字母
                        $initials = '';
                        $name_parts = explode(' ', $current_user['name']);
                        foreach ($name_parts as $part) {
                            if (!empty($part)) {
                                $initials .= mb_substr($part, 0, 1);
                                if (mb_strlen($initials) >= 2) break;
                            }
                        }
                        if (empty($initials)) {
                            $initials = mb_substr($current_user['username'], 0, 2);
                        }
                        ?>
                        <span class="avatar-text"><?php echo strtoupper($initials); ?></span>
                    </div>
                    <div class="user-info">
                        <span class="user-name"><?php echo htmlspecialchars($current_user['name']); ?></span>
                        <span class="user-role">
                            <?php
                            $role_names = [
                                Auth::ROLE_ADMIN => '管理员',
                                Auth::ROLE_USER => '用户',
                                Auth::ROLE_GUEST => '访客'
                            ];
                            echo $role_names[$current_user['role']] ?? '未知';
                            ?>
                        </span>
                    </div>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="dropdown-arrow">
                        <polyline points="6,9 12,15 18,9"/>
                    </svg>
                </button>
                
                <div class="user-menu-dropdown">
                    <div class="dropdown-header">
                        <div class="user-avatar-large">
                            <span class="avatar-text-large"><?php echo strtoupper($initials); ?></span>
                        </div>
                        <div class="user-details">
                            <h4><?php echo htmlspecialchars($current_user['name']); ?></h4>
                            <p><?php echo htmlspecialchars($current_user['email']); ?></p>
                            <span class="user-status">
                                <span class="status-dot"></span>
                                在线
                            </span>
                        </div>
                    </div>
                    
                    <div class="dropdown-menu">
                        <a href="profile.php" class="dropdown-item">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            个人资料
                        </a>
                        
                        <a href="logs.php" class="dropdown-item">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                                <line x1="16" y1="13" x2="8" y2="13"/>
                                <line x1="16" y1="17" x2="8" y2="17"/>
                                <polyline points="10,9 9,9 8,9"/>
                            </svg>
                            日志查看
                        </a>

                        <a href="#" class="dropdown-item">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                            </svg>
                            账户设置
                        </a>
                        
                        <div class="dropdown-divider"></div>
                        
                        <button class="dropdown-item logout-btn" onclick="handleLogout()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                                <polyline points="16,17 21,12 16,7"/>
                                <line x1="21" y1="12" x2="9" y2="12"/>
                            </svg>
                            退出登录
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- 移动端侧边栏导航 -->
<div class="mobile-sidebar-overlay"></div>
<nav class="mobile-sidebar">
    <div class="mobile-sidebar-header">
        <div class="mobile-brand">
            <div class="brand-logo">
                <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                </svg>
            </div>
            <div class="mobile-brand-text">
                <h2><?php echo SystemConfig::getSiteName(); ?></h2>
                <span><?php echo SystemConfig::getSiteDescription(); ?></span>
            </div>
        </div>
        <button class="mobile-sidebar-close" aria-label="关闭菜单">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
        </button>
    </div>

    <div class="mobile-sidebar-content">
        <!-- 用户信息 -->
        <div class="mobile-user-info">
            <div class="mobile-user-avatar">
                <?php
                // 生成用户头像首字母
                $initials = '';
                $name_parts = explode(' ', $current_user['name']);
                foreach ($name_parts as $part) {
                    if (!empty($part)) {
                        $initials .= mb_substr($part, 0, 1);
                        if (mb_strlen($initials) >= 2) break;
                    }
                }
                if (empty($initials)) {
                    $initials = mb_substr($current_user['username'], 0, 2);
                }
                ?>
                <span class="mobile-avatar-text"><?php echo strtoupper($initials); ?></span>
            </div>
            <div class="mobile-user-details">
                <h3><?php echo htmlspecialchars($current_user['name']); ?></h3>
                <p><?php echo htmlspecialchars($current_user['email']); ?></p>
                <span class="mobile-user-role">
                    <?php
                    $role_names = [
                        Auth::ROLE_ADMIN => '管理员',
                        Auth::ROLE_USER => '用户',
                        Auth::ROLE_GUEST => '访客'
                    ];
                    echo $role_names[$current_user['role']] ?? '未知';
                    ?>
                </span>
            </div>
        </div>

        <!-- 导航菜单 -->
        <ul class="mobile-nav-list">
            <li class="mobile-nav-item">
                <a href="index.php" class="mobile-nav-link <?php echo ($current_page === 'index') ? 'active' : ''; ?>">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                    </svg>
                    仪表盘
                </a>
            </li>

            <?php if (Auth::hasRole(Auth::ROLE_ADMIN)): ?>
            <li class="mobile-nav-item">
                <a href="users.php" class="mobile-nav-link <?php echo ($current_page === 'users') ? 'active' : ''; ?>">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                        <circle cx="9" cy="7" r="4"/>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                    </svg>
                    用户管理
                </a>
            </li>

            <li class="mobile-nav-item">
                <a href="#" class="mobile-nav-link">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                    </svg>
                    系统设置
                </a>
            </li>
            <?php endif; ?>

            <li class="mobile-nav-item">
                <a href="profile.php" class="mobile-nav-link <?php echo ($current_page === 'profile') ? 'active' : ''; ?>">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    个人资料
                </a>
            </li>
        </ul>

        <!-- 快捷操作 -->
        <div class="mobile-sidebar-footer">
            <button class="mobile-logout-btn" onclick="handleLogout()">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                    <polyline points="16,17 21,12 16,7"/>
                    <line x1="21" y1="12" x2="9" y2="12"/>
                </svg>
                退出登录
            </button>
        </div>
    </div>
</nav>

<style>
/* ===== 仪表盘头部样式 ===== */
.dashboard-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-6);
    height: 64px;
    max-width: 1400px;
    margin: 0 auto;
}

/* 品牌区域 */
.header-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.brand-logo svg {
    color: var(--primary-color);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1;
}

.brand-subtitle {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 导航菜单 */
.header-nav {
    flex: 1;
    margin: 0 var(--spacing-8);
}

.nav-list {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--text-primary);
    background: var(--bg-secondary);
}

.nav-link.active {
    color: var(--primary-color);
    background: rgba(var(--primary-rgb), 0.1);
}

/* 工具栏 */
.header-toolbar {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.toolbar-btn {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.toolbar-btn:hover {
    color: var(--text-primary);
    background: var(--bg-secondary);
}

.notification-badge {
    position: absolute;
    top: 6px;
    right: 6px;
    background: var(--error-color);
    color: white;
    font-size: 0.625rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

/* 通知菜单 */
.notification-menu {
    position: relative;
}

.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--spacing-2);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    min-width: 320px;
    max-width: 400px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
}

.notification-menu.active .notification-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--border-color);
}

.notification-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.mark-all-read {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.875rem;
    cursor: pointer;
    padding: 0;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.notification-item:hover {
    background: var(--bg-secondary);
}

.notification-item.unread {
    background: rgba(var(--primary-rgb), 0.05);
    border-left: 3px solid var(--primary-color);
}

.notification-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-content p {
    margin: 0 0 var(--spacing-1) 0;
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 500;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.notification-footer {
    padding: var(--spacing-3) var(--spacing-4);
    text-align: center;
    border-top: 1px solid var(--border-color);
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

.view-all:hover {
    text-decoration: underline;
}

/* 用户菜单 */
.user-menu {
    position: relative;
}

.user-menu-trigger {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-2) var(--spacing-3);
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.user-menu-trigger:hover {
    background: var(--bg-secondary);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark, #3b82f6));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.avatar-text {
    font-family: inherit;
    letter-spacing: 0.5px;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    gap: var(--spacing-1);
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    line-height: 1.2;
}

.user-role {
    font-size: 0.75rem;
    color: var(--text-secondary);
    line-height: 1;
    background: rgba(var(--primary-rgb), 0.1);
    padding: 3px 8px;
    border-radius: 6px;
    font-weight: 500;
    margin-top: 2px;
}

.dropdown-arrow {
    color: var(--text-secondary);
    transition: var(--transition);
}

.user-menu.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* 用户菜单下拉框 */
.user-menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--spacing-2);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    min-width: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
}

.user-menu.active .user-menu-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--border-color);
}

.user-avatar-large {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark, #3b82f6));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    border: 3px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.avatar-text-large {
    font-family: inherit;
    letter-spacing: 0.5px;
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.user-details h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.3;
}

.user-details p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.2;
}

.user-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    font-size: 0.75rem;
    color: var(--success-color);
    margin-top: var(--spacing-1);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(34, 197, 94, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
}

.dropdown-menu {
    padding: var(--spacing-2);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    width: 100%;
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--text-secondary);
    text-decoration: none;
    border: none;
    background: transparent;
    border-radius: 8px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
}

.dropdown-item:hover {
    color: var(--text-primary);
    background: var(--bg-secondary);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-2) 0;
}

/* ===== 移动端汉堡菜单按钮 ===== */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: var(--transition);
}

.mobile-menu-toggle:hover {
    background: var(--bg-secondary);
}

.hamburger-line {
    width: 20px;
    height: 2px;
    background: var(--text-primary);
    margin: 2px 0;
    transition: var(--transition);
    border-radius: 1px;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* 移动端页面标题 */
.mobile-page-title {
    display: none;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.mobile-page-title h1 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-container {
        padding: 0 var(--spacing-4);
        position: relative;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-page-title {
        display: block;
    }

    .header-nav {
        display: none;
    }

    .header-brand {
        display: none;
    }

    .user-info {
        display: none;
    }

    .user-menu-dropdown {
        min-width: 260px;
        right: -10px;
    }

    .notification-badge {
        font-size: 0.5rem;
        padding: 1px 4px;
    }
}

/* ===== 移动端侧边栏 ===== */
.mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(15, 23, 42, 0.6);
    backdrop-filter: blur(4px);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: var(--bg-primary);
    border-right: 1px solid var(--border-color);
    z-index: 9999;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
    box-shadow: var(--shadow-2xl);
}

.mobile-sidebar.active {
    transform: translateX(0);
}

.mobile-sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.mobile-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.mobile-brand .brand-logo svg {
    color: var(--primary-color);
}

.mobile-brand-text h2 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.mobile-brand-text span {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.mobile-sidebar-close {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--bg-tertiary);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
}

.mobile-sidebar-close:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.mobile-sidebar-content {
    padding: var(--spacing-4);
}

/* 移动端用户信息 */
.mobile-user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    background: var(--bg-secondary);
    border-radius: 12px;
    margin-bottom: var(--spacing-6);
}

.mobile-user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark, #3b82f6));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    border: 3px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    flex-shrink: 0;
}

.mobile-avatar-text {
    font-family: inherit;
    letter-spacing: 0.5px;
}

.mobile-user-details {
    flex: 1;
    min-width: 0;
}

.mobile-user-details h3 {
    margin: 0 0 var(--spacing-1) 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mobile-user-details p {
    margin: 0 0 var(--spacing-1) 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mobile-user-role {
    display: inline-block;
    font-size: 0.75rem;
    color: var(--text-secondary);
    background: rgba(var(--primary-rgb), 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}

@media (max-width: 480px) {
    .user-menu-dropdown {
        position: fixed;
        top: 60px !important;
        right: 10px;
        left: 10px;
        width: auto;
        min-width: auto;
    }

    .mobile-sidebar {
        width: 100%;
        max-width: 320px;
    }
}

/* 移动端导航菜单 */
.mobile-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-nav-item {
    margin-bottom: var(--spacing-1);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 12px;
    font-weight: 500;
    transition: var(--transition);
    font-size: 0.875rem;
}

.mobile-nav-link:hover {
    color: var(--text-primary);
    background: var(--bg-secondary);
}

.mobile-nav-link.active {
    color: var(--primary-color);
    background: rgba(var(--primary-rgb), 0.1);
    font-weight: 600;
}

.mobile-nav-link svg {
    flex-shrink: 0;
}

/* 移动端侧边栏底部 */
.mobile-sidebar-footer {
    margin-top: var(--spacing-8);
    padding-top: var(--spacing-4);
    border-top: 1px solid var(--border-color);
}

.mobile-logout-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.mobile-logout-btn:hover {
    background: var(--error-light);
    border-color: var(--error-color);
    color: var(--error-color);
}

.mobile-logout-btn svg {
    flex-shrink: 0;
}
</style>

<script>
// 导航栏交互
document.addEventListener('DOMContentLoaded', function() {
    // 移动端侧边栏交互
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileSidebar = document.querySelector('.mobile-sidebar');
    const mobileSidebarOverlay = document.querySelector('.mobile-sidebar-overlay');
    const mobileSidebarClose = document.querySelector('.mobile-sidebar-close');

    function openMobileSidebar() {
        mobileSidebar?.classList.add('active');
        mobileSidebarOverlay?.classList.add('active');
        mobileMenuToggle?.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    function closeMobileSidebar() {
        mobileSidebar?.classList.remove('active');
        mobileSidebarOverlay?.classList.remove('active');
        mobileMenuToggle?.classList.remove('active');
        document.body.style.overflow = '';
    }

    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            if (mobileSidebar?.classList.contains('active')) {
                closeMobileSidebar();
            } else {
                openMobileSidebar();
            }
        });
    }

    if (mobileSidebarClose) {
        mobileSidebarClose.addEventListener('click', closeMobileSidebar);
    }

    if (mobileSidebarOverlay) {
        mobileSidebarOverlay.addEventListener('click', closeMobileSidebar);
    }

    // 移动端导航链接点击后关闭侧边栏
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', function() {
            closeMobileSidebar();
        });
    });

    // 用户菜单交互
    const userMenu = document.querySelector('.user-menu');
    const userMenuTrigger = document.querySelector('.user-menu-trigger');

    if (userMenuTrigger) {
        userMenuTrigger.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('active');
            // 关闭通知菜单和移动端侧边栏
            document.querySelector('.notification-menu')?.classList.remove('active');
            closeMobileSidebar();
        });
    }

    // 通知菜单交互
    const notificationMenu = document.querySelector('.notification-menu');
    const notificationBtn = document.querySelector('.notification-btn');

    if (notificationBtn) {
        notificationBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationMenu.classList.toggle('active');
            // 关闭用户菜单和移动端侧边栏
            userMenu?.classList.remove('active');
            closeMobileSidebar();
        });
    }

    // 点击外部关闭所有菜单
    document.addEventListener('click', function() {
        userMenu?.classList.remove('active');
        notificationMenu?.classList.remove('active');
    });

    // 阻止菜单内部点击事件冒泡
    const userDropdown = document.querySelector('.user-menu-dropdown');
    const notificationDropdown = document.querySelector('.notification-dropdown');

    if (userDropdown) {
        userDropdown.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    if (notificationDropdown) {
        notificationDropdown.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // 标记所有通知为已读
    const markAllReadBtn = document.querySelector('.mark-all-read');
    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', function() {
            const unreadItems = document.querySelectorAll('.notification-item.unread');
            unreadItems.forEach(item => {
                item.classList.remove('unread');
            });

            // 更新通知徽章
            const badge = document.querySelector('.notification-badge');
            if (badge) {
                badge.style.display = 'none';
            }

            showToast('所有通知已标记为已读', 'success');
        });
    }
});

// 处理登出
async function handleLogout() {
    try {
        const response = await fetch('../api/logout.api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            showToast('已安全退出', 'success');
            setTimeout(() => {
                window.location.href = '../login.php';
            }, 1000);
        } else {
            showToast(data.message || '退出失败', 'error');
        }
    } catch (error) {
        console.error('退出错误:', error);
        showToast('退出失败，请重试', 'error');
    }
}
</script>
