<?php
/**
 * 📝 注册API接口
 * 
 * 功能：处理用户注册请求
 * 方法：POST
 * 参数：username, email, password, confirm_password
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入认证类
require_once '../auth/Auth.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 检查注册功能是否启用
Auth::requireRegisterAccess();

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求'
    ]);
    exit;
}

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);

    // 如果不是JSON请求，尝试从POST获取
    if (!$input) {
        $input = $_POST;
    }

    $username = trim($input['username'] ?? '');
    $email = trim($input['email'] ?? '');
    $password = trim($input['password'] ?? '');
    $confirm_password = trim($input['confirm_password'] ?? '');
    $nickname = trim($input['nickname'] ?? '');
    $agree_terms = isset($input['agree_terms']) ? (bool)$input['agree_terms'] : false;
    $captchaSessionKey = trim($input['captcha_session_key'] ?? '');

    // 验证输入
    if (empty($username)) {
        throw new Exception('请输入用户名');
    }
    
    if (strlen($username) < 3) {
        throw new Exception('用户名至少需要3个字符');
    }
    
    if (empty($email)) {
        throw new Exception('请输入邮箱地址');
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('请输入有效的邮箱地址');
    }
    
    if (empty($password)) {
        throw new Exception('请输入密码');
    }
    
    // 验证密码强度 - 使用动态配置
    require_once '../auth/SystemConfig.php';
    $passwordValidation = SystemConfig::validatePasswordComplexity($password);
    if (!$passwordValidation['valid']) {
        throw new Exception(implode('；', $passwordValidation['errors']));
    }
    
    if ($password !== $confirm_password) {
        throw new Exception('两次输入的密码不一致');
    }
    
    if (!$agree_terms) {
        throw new Exception('请同意服务条款和隐私政策');
    }

    // 检查是否需要验证码
    require_once '../auth/SystemConfig.php';
    $needsCaptcha = SystemConfig::get('security', 'enable_register_captcha', '0') === '1';

    if ($needsCaptcha) {
        if (empty($captchaSessionKey)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => '请完成验证码验证',
                'error_type' => 'captcha_required',
                'timestamp' => time()
            ]);
            exit;
        }

        // 验证验证码是否有效
        $captchaValid = false;
        if (isset($_SESSION['captcha_verified_' . $captchaSessionKey])) {
            $captchaValid = true;
            // 验证码使用后清除
            unset($_SESSION['captcha_verified_' . $captchaSessionKey]);
        }

        if (!$captchaValid) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => '验证码错误或已过期',
                'error_type' => 'captcha_error',
                'timestamp' => time()
            ]);
            exit;
        }
    }

    // 验证用户名格式（只允许字母、数字、下划线）
    if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        throw new Exception('用户名只能包含字母、数字和下划线');
    }
    
    // 验证密码强度
    $password_strength = calculatePasswordStrength($password);
    if ($password_strength['score'] < 3) {
        throw new Exception('密码强度太弱，请使用更复杂的密码');
    }
    
    // 尝试注册
    $result = Auth::register($username, $email, $password, $nickname);
    
    if ($result['success']) {
        // 记录注册日志
        error_log("用户注册成功: {$username} ({$email}) - " . date('Y-m-d H:i:s'));

        // 设置session中的待验证邮箱（用于验证码验证）
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $_SESSION['pending_verification_email'] = $email;

        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'user_id' => $result['user_id'] ?? null,
            'email_sent' => $result['email_sent'] ?? false,
            'user_email' => $email,
            'timestamp' => time()
        ]);
    } else {
        // 记录注册失败日志
        error_log("用户注册失败: {$username} ({$email}) - {$result['message']} - " . date('Y-m-d H:i:s'));
        
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $result['message'],
            'timestamp' => time()
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => time()
    ]);
}

/**
 * 计算密码强度 - 使用动态配置
 */
function calculatePasswordStrength($password) {
    require_once '../auth/SystemConfig.php';
    $rules = SystemConfig::getPasswordComplexityRules();

    $score = 0;
    $checks = [
        'length' => strlen($password) >= $rules['min_length'],
        'lowercase' => preg_match('/[a-z]/', $password),
        'uppercase' => preg_match('/[A-Z]/', $password),
        'numbers' => preg_match('/\d/', $password),
        'symbols' => preg_match('/[^A-Za-z0-9]/', $password)
    ];

    // 基础长度检查
    if ($checks['length']) $score++;

    // 额外长度加分
    if (strlen($password) >= $rules['min_length'] + 2) $score++;
    if (strlen($password) >= 12) $score++;

    // 复杂度检查
    if ($checks['lowercase']) $score++;
    if ($checks['uppercase']) $score++;
    if ($checks['numbers']) $score++;
    if ($checks['symbols']) $score++;

    return [
        'score' => $score,
        'checks' => $checks,
        'rules' => $rules
    ];
}
?>
