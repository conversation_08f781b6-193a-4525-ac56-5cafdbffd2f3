<?php
/**
 * 👑 会员管理类
 * 
 * 功能：会员管理、分组管理、权限控制、积分管理
 */

require_once '../config/database.php';

class MemberManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 获取所有用户分组
     */
    public function getUserGroups($status = 'active') {
        $sql = "SELECT * FROM user_groups";
        $params = [];
        
        if ($status) {
            $sql .= " WHERE status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY sort_order ASC, member_level ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取单个用户分组
     */
    public function getUserGroup($groupKey) {
        return $this->db->fetch(
            "SELECT * FROM user_groups WHERE group_key = ?",
            [$groupKey]
        );
    }
    
    /**
     * 创建用户分组
     */
    public function createUserGroup($data) {
        $sql = "INSERT INTO user_groups (
            group_key, group_name, group_description, group_color, group_icon,
            is_member_group, member_level, permissions, features, limits, sort_order
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        return $this->db->insert($sql, [
            $data['group_key'],
            $data['group_name'],
            $data['group_description'] ?? '',
            $data['group_color'] ?? '#3b82f6',
            $data['group_icon'] ?? 'users',
            $data['is_member_group'] ?? 0,
            $data['member_level'] ?? 0,
            json_encode($data['permissions'] ?? []),
            json_encode($data['features'] ?? []),
            json_encode($data['limits'] ?? []),
            $data['sort_order'] ?? 0
        ]);
    }
    
    /**
     * 更新用户分组
     */
    public function updateUserGroup($groupKey, $data) {
        $sql = "UPDATE user_groups SET 
            group_name = ?, group_description = ?, group_color = ?, group_icon = ?,
            is_member_group = ?, member_level = ?, permissions = ?, features = ?, 
            limits = ?, sort_order = ?, updated_at = NOW()
            WHERE group_key = ?";
        
        return $this->db->update($sql, [
            $data['group_name'],
            $data['group_description'] ?? '',
            $data['group_color'] ?? '#3b82f6',
            $data['group_icon'] ?? 'users',
            $data['is_member_group'] ?? 0,
            $data['member_level'] ?? 0,
            json_encode($data['permissions'] ?? []),
            json_encode($data['features'] ?? []),
            json_encode($data['limits'] ?? []),
            $data['sort_order'] ?? 0,
            $groupKey
        ]);
    }
    
    /**
     * 删除用户分组
     */
    public function deleteUserGroup($groupKey) {
        // 检查是否有用户使用此分组
        $userCount = $this->db->fetch(
            "SELECT COUNT(*) as count FROM users WHERE user_group = ?",
            [$groupKey]
        )['count'];
        
        if ($userCount > 0) {
            throw new Exception("无法删除分组：还有 {$userCount} 个用户使用此分组");
        }
        
        return $this->db->delete("DELETE FROM user_groups WHERE group_key = ?", [$groupKey]);
    }
    
    /**
     * 获取用户详细信息（包含分组信息）
     */
    public function getUserWithGroup($userId) {
        return $this->db->fetch(
            "SELECT * FROM user_group_info WHERE id = ?",
            [$userId]
        );
    }
    
    /**
     * 获取用户列表（包含分组信息）
     */
    public function getUsersWithGroups($filters = [], $page = 1, $perPage = 15) {
        $where = [];
        $params = [];
        
        // 搜索条件
        if (!empty($filters['search'])) {
            $where[] = "(username LIKE ? OR email LIKE ? OR nickname LIKE ?)";
            $searchParam = "%{$filters['search']}%";
            $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
        }
        
        // 分组过滤
        if (!empty($filters['user_group'])) {
            $where[] = "user_group = ?";
            $params[] = $filters['user_group'];
        }
        
        // 会员类型过滤
        if (!empty($filters['member_type'])) {
            $where[] = "member_type = ?";
            $params[] = $filters['member_type'];
        }
        
        // 状态过滤
        if (!empty($filters['status'])) {
            $where[] = "status = ?";
            $params[] = $filters['status'];
        }
        
        // 会员状态过滤
        if (!empty($filters['member_status'])) {
            if ($filters['member_status'] === 'active') {
                $where[] = "(member_expire_date IS NULL OR member_expire_date > NOW())";
            } elseif ($filters['member_status'] === 'expired') {
                $where[] = "(member_expire_date IS NOT NULL AND member_expire_date <= NOW())";
            }
        }
        
        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
        
        // 获取总数
        $totalSql = "SELECT COUNT(*) as total FROM user_group_info {$whereClause}";
        $total = $this->db->fetch($totalSql, $params)['total'];
        
        // 获取数据
        $offset = ($page - 1) * $perPage;
        $dataSql = "SELECT * FROM user_group_info {$whereClause} 
                   ORDER BY created_at DESC LIMIT {$perPage} OFFSET {$offset}";
        $users = $this->db->fetchAll($dataSql, $params);
        
        return [
            'users' => $users,
            'total' => $total,
            'pages' => ceil($total / $perPage),
            'current_page' => $page
        ];
    }
    
    /**
     * 更新用户分组
     */
    public function updateUserGroup_User($userId, $groupKey, $operatorId = null) {
        // 获取用户当前信息
        $user = $this->getUserWithGroup($userId);
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        // 获取新分组信息
        $newGroup = $this->getUserGroup($groupKey);
        if (!$newGroup) {
            throw new Exception('分组不存在');
        }
        
        $oldGroup = $user['user_group'];
        $oldLevel = $user['member_level'];
        
        // 更新用户分组
        $sql = "UPDATE users SET 
            user_group = ?, 
            member_level = ?,
            member_type = CASE 
                WHEN ? = 1 THEN 
                    CASE 
                        WHEN ? = 1 THEN 'vip'
                        WHEN ? = 2 THEN 'premium'
                        ELSE 'regular'
                    END
                ELSE 'regular'
            END,
            updated_at = NOW()
            WHERE id = ?";
        
        $result = $this->db->update($sql, [
            $groupKey,
            $newGroup['member_level'],
            $newGroup['is_member_group'],
            $newGroup['member_level'],
            $newGroup['member_level'],
            $userId
        ]);
        
        if ($result) {
            // 记录操作日志
            $this->logMemberAction($userId, 'group_change', '管理员更改用户分组', [
                'old_group' => $oldGroup,
                'new_group' => $groupKey,
                'old_level' => $oldLevel,
                'new_level' => $newGroup['member_level'],
                'operator_id' => $operatorId
            ]);
        }
        
        return $result;
    }
    
    /**
     * 设置会员到期时间
     */
    public function setMemberExpiry($userId, $expireDate, $operatorId = null) {
        $sql = "UPDATE users SET 
            member_expire_date = ?,
            member_start_date = CASE 
                WHEN member_start_date IS NULL THEN NOW()
                ELSE member_start_date
            END,
            updated_at = NOW()
            WHERE id = ?";
        
        $result = $this->db->update($sql, [$expireDate, $userId]);
        
        if ($result) {
            $this->logMemberAction($userId, 'expiry_change', '设置会员到期时间', [
                'expire_date' => $expireDate,
                'operator_id' => $operatorId
            ]);
        }
        
        return $result;
    }
    
    /**
     * 调整用户积分
     */
    public function adjustUserPoints($userId, $pointsChange, $reason, $operatorId = null) {
        // 获取用户当前积分
        $user = $this->db->fetch("SELECT available_points, total_points FROM users WHERE id = ?", [$userId]);
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        $newAvailablePoints = $user['available_points'] + $pointsChange;
        $newTotalPoints = $pointsChange > 0 ? $user['total_points'] + $pointsChange : $user['total_points'];
        
        if ($newAvailablePoints < 0) {
            throw new Exception('积分不足');
        }
        
        // 更新用户积分
        $sql = "UPDATE users SET 
            available_points = ?,
            total_points = ?,
            updated_at = NOW()
            WHERE id = ?";
        
        $result = $this->db->update($sql, [$newAvailablePoints, $newTotalPoints, $userId]);
        
        if ($result) {
            // 记录积分变动
            $this->db->insert(
                "INSERT INTO points_history (user_id, points_change, points_balance, change_type, change_reason, operator_id) 
                 VALUES (?, ?, ?, ?, ?, ?)",
                [
                    $userId,
                    $pointsChange,
                    $newAvailablePoints,
                    $pointsChange > 0 ? 'earn' : 'spend',
                    $reason,
                    $operatorId
                ]
            );
            
            // 记录会员日志
            $this->logMemberAction($userId, 'points_change', $reason, [
                'points_change' => $pointsChange,
                'new_balance' => $newAvailablePoints,
                'operator_id' => $operatorId
            ]);
        }
        
        return $result;
    }
    
    /**
     * 记录会员操作日志
     */
    public function logMemberAction($userId, $actionType, $description, $extraData = []) {
        $sql = "INSERT INTO member_logs (
            user_id, action_type, action_description, old_group, new_group,
            old_level, new_level, points_change, operator_id, operator_name,
            ip_address, user_agent, extra_data
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        return $this->db->insert($sql, [
            $userId,
            $actionType,
            $description,
            $extraData['old_group'] ?? null,
            $extraData['new_group'] ?? null,
            $extraData['old_level'] ?? null,
            $extraData['new_level'] ?? null,
            $extraData['points_change'] ?? 0,
            $extraData['operator_id'] ?? null,
            $extraData['operator_name'] ?? null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null,
            json_encode($extraData)
        ]);
    }
    
    /**
     * 获取会员统计信息
     */
    public function getMemberStats() {
        $stats = [];
        
        // 总用户数
        $stats['total_users'] = $this->db->fetch("SELECT COUNT(*) as count FROM users")['count'];
        
        // 按分组统计
        $groupStats = $this->db->fetchAll(
            "SELECT ug.group_name, ug.group_color, COUNT(u.id) as user_count 
             FROM user_groups ug 
             LEFT JOIN users u ON ug.group_key = u.user_group 
             GROUP BY ug.id 
             ORDER BY ug.sort_order"
        );
        $stats['group_stats'] = $groupStats;
        
        // 会员类型统计
        $memberTypeStats = $this->db->fetchAll(
            "SELECT member_type, COUNT(*) as count FROM users GROUP BY member_type"
        );
        $stats['member_type_stats'] = $memberTypeStats;
        
        // 会员状态统计
        $memberStatusStats = $this->db->fetchAll(
            "SELECT 
                CASE 
                    WHEN member_expire_date IS NULL THEN 'permanent'
                    WHEN member_expire_date > NOW() THEN 'active'
                    ELSE 'expired'
                END as status,
                COUNT(*) as count
             FROM users 
             WHERE member_type != 'regular'
             GROUP BY status"
        );
        $stats['member_status_stats'] = $memberStatusStats;
        
        return $stats;
    }
    
    /**
     * 检查用户权限
     */
    public function checkUserPermission($userId, $permission) {
        $user = $this->getUserWithGroup($userId);
        if (!$user) {
            return false;
        }
        
        $permissions = json_decode($user['permissions'] ?? '{}', true);
        return isset($permissions[$permission]) && $permissions[$permission] === true;
    }
    
    /**
     * 检查用户功能权限
     */
    public function checkUserFeature($userId, $feature) {
        $user = $this->getUserWithGroup($userId);
        if (!$user) {
            return false;
        }
        
        $features = json_decode($user['features'] ?? '{}', true);
        return isset($features[$feature]) && $features[$feature] === true;
    }
    
    /**
     * 获取用户限制
     */
    public function getUserLimits($userId) {
        $user = $this->getUserWithGroup($userId);
        if (!$user) {
            return [];
        }
        
        return json_decode($user['limits'] ?? '{}', true);
    }
}
