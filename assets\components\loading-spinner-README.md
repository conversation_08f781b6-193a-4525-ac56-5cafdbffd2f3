# 加载动画组件 (LoadingSpinner)

一个功能丰富的加载动画组件，提供多种动画效果和灵活的配置选项。

## 🚀 特性

- ✅ **多种动画效果** - 7种不同的加载动画类型
- ✅ **灵活配置** - 丰富的配置选项
- ✅ **响应式设计** - 完美适配各种屏幕尺寸
- ✅ **暗色主题支持** - 自动适配主题切换
- ✅ **事件系统** - 支持监听显示/隐藏事件
- ✅ **无障碍支持** - 支持减少动画偏好设置
- ✅ **静态方法** - 提供快速调用的静态方法

## 🎨 动画类型

### 1. Spinner (旋转圆环)
```javascript
const loader = new LoadingSpinner({ type: 'spinner' });
```

### 2. Dots (脉冲圆点)
```javascript
const loader = new LoadingSpinner({ type: 'dots' });
```

### 3. Wave (波浪动画)
```javascript
const loader = new LoadingSpinner({ type: 'wave' });
```

### 4. <PERSON><PERSON><PERSON> (弹跳球)
```javascript
const loader = new LoadingSpinner({ type: 'bounce' });
```

### 5. Ring (渐变圆环)
```javascript
const loader = new LoadingSpinner({ type: 'ring' });
```

### 6. Pulse (脉冲圆环)
```javascript
const loader = new LoadingSpinner({ type: 'pulse' });
```

### 7. Progress (进度条)
```javascript
const loader = new LoadingSpinner({ type: 'progress' });
```

## 📦 安装使用

### 1. 引入文件

```html
<!-- CSS样式 -->
<link rel="stylesheet" href="assets/components/loading-spinner.css">

<!-- JavaScript -->
<script src="assets/components/loading-spinner.js"></script>
```

### 2. 基础使用

```javascript
// 创建实例
const loader = new LoadingSpinner();

// 显示加载动画
loader.show();

// 隐藏加载动画
loader.hide();
```

### 3. 快速调用

```javascript
// 快速显示
const loader = LoadingSpinner.show({
    type: 'dots',
    text: '正在加载...'
});

// 快速隐藏所有
LoadingSpinner.hideAll();
```

## 🎯 配置选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `type` | string | 'spinner' | 动画类型 |
| `text` | string | '加载中...' | 加载文本 |
| `subtext` | string | '' | 副文本 |
| `overlay` | boolean | true | 是否显示遮罩层 |
| `backdrop` | boolean | true | 是否显示背景模糊 |
| `autoHide` | boolean | false | 是否自动隐藏 |
| `duration` | number | 0 | 自动隐藏时间(ms) |
| `zIndex` | number | 9999 | z-index层级 |
| `className` | string | '' | 自定义CSS类名 |
| `clickToClose` | boolean | false | 点击遮罩关闭 |
| `onShow` | function | null | 显示回调 |
| `onHide` | function | null | 隐藏回调 |

## 🔧 API 方法

### `show(options)`
显示加载动画
```javascript
loader.show({
    type: 'wave',
    text: '数据加载中...',
    subtext: '请稍候'
});
```

### `hide()`
隐藏加载动画
```javascript
loader.hide();
```

### `toggle(options)`
切换显示状态
```javascript
loader.toggle({ type: 'bounce' });
```

### `updateText(text, subtext)`
更新加载文本
```javascript
loader.updateText('正在保存...', '请不要关闭页面');
```

### `getState()`
获取当前状态
```javascript
const state = loader.getState();
console.log(state.isVisible); // true/false
```

### `destroy()`
销毁组件
```javascript
loader.destroy();
```

## 📡 事件系统

### `loadingSpinner:loadingShow`
加载动画显示时触发
```javascript
document.addEventListener('loadingSpinner:loadingShow', function(e) {
    console.log('加载动画已显示', e.detail);
});
```

### `loadingSpinner:loadingHide`
加载动画隐藏时触发
```javascript
document.addEventListener('loadingSpinner:loadingHide', function(e) {
    console.log('加载动画已隐藏', e.detail);
});
```

## 💡 使用示例

### 基础示例
```javascript
// 创建加载器
const loader = new LoadingSpinner({
    type: 'dots',
    text: '加载中...'
});

// 显示
loader.show();

// 3秒后隐藏
setTimeout(() => {
    loader.hide();
}, 3000);
```

### 表单提交加载
```javascript
const form = document.getElementById('myForm');
const loader = new LoadingSpinner({
    type: 'spinner',
    text: '正在提交...',
    subtext: '请稍候'
});

form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // 显示加载动画
    loader.show();
    
    // 模拟异步请求
    fetch('/api/submit', {
        method: 'POST',
        body: new FormData(form)
    })
    .then(response => response.json())
    .then(data => {
        loader.hide();
        console.log('提交成功', data);
    })
    .catch(error => {
        loader.hide();
        console.error('提交失败', error);
    });
});
```

### 自动隐藏
```javascript
const loader = new LoadingSpinner({
    type: 'pulse',
    text: '操作成功！',
    autoHide: true,
    duration: 2000,
    onHide: () => {
        console.log('加载动画已自动隐藏');
    }
});

loader.show();
```

### 动态更新文本
```javascript
const loader = new LoadingSpinner({
    type: 'progress',
    text: '正在处理...'
});

loader.show();

// 模拟进度更新
let progress = 0;
const interval = setInterval(() => {
    progress += 20;
    loader.updateText(`正在处理... ${progress}%`);
    
    if (progress >= 100) {
        clearInterval(interval);
        loader.updateText('处理完成！');
        setTimeout(() => loader.hide(), 1000);
    }
}, 500);
```

### 静态方法使用
```javascript
// 快速显示不同类型的加载动画
LoadingSpinner.show({ type: 'wave', text: '加载中...' });

// 在某个操作完成后隐藏所有加载动画
LoadingSpinner.hideAll();
```

## 🎨 样式自定义

组件使用CSS变量，可以轻松自定义样式：

```css
:root {
    --primary-color: #3b82f6;
    --bg-primary: #ffffff;
    --text-primary: #1f2937;
    --text-muted: #6b7280;
    --border-light: #e5e7eb;
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 自定义加载容器样式 */
.loading-container {
    border-radius: 16px;
    padding: 2rem;
}

/* 自定义动画颜色 */
.loading-dot,
.loading-spinner,
.loading-wave-bar {
    background: #ff6b6b;
    border-color: #ff6b6b;
}
```

## 📱 响应式支持

组件自动适配不同屏幕尺寸：
- 桌面端：完整尺寸和动画
- 移动端：优化的尺寸和间距
- 平板端：自适应布局

## 🌙 暗色主题

组件自动适配暗色主题：
```css
[data-theme="dark"] .loading-overlay {
    background: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .loading-container {
    background: var(--bg-primary);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}
```

## ♿ 无障碍支持

组件支持用户的减少动画偏好设置：
```css
@media (prefers-reduced-motion: reduce) {
    .loading-overlay,
    .loading-container,
    /* 所有动画元素 */ {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
```

## 🔧 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📄 许可证

MIT License

---

**现代化PHP管理系统** - 加载动画组件
