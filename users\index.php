<?php
/**
 * 📊 现代化PHP管理系统 - 仪表盘
 * 
 * 功能：用户仪表盘主页
 * 设计：三段式布局（头部导航栏、主内容区域、页脚）
 * 特色：响应式设计、主题切换、权限控制
 */

// 引入认证中间件
require_once '../auth/auth.php';
require_once '../auth/SystemConfig.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 要求用户登录
Auth::requireLogin('../login.php');

// 获取当前用户信息
$current_user = Auth::getCurrentUser();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle('仪表盘'); ?></title>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- SEO和社交媒体标签 -->
    <meta name="description" content="现代化PHP管理系统仪表盘，提供完整的系统管理功能">
    <meta name="keywords" content="PHP, 管理系统, 仪表盘, 后台管理">
    <meta name="author" content="现代化PHP管理系统">
    
    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">

    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 仪表盘样式 -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- 仪表盘容器 -->
    <div class="dashboard-container">
        <!-- 头部导航栏 -->
        <?php include 'includes/header.php'; ?>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                        </svg>
                        仪表盘
                    </h1>
                    <p class="page-subtitle">欢迎回来，<?php echo htmlspecialchars($current_user['name']); ?>！</p>
                </div>
                
                <!-- 统计卡片 - 扁平化设计 -->
                <div class="stats-grid">
                    <div class="card-google card-blue">
                        <div class="card-content">
                            <div class="card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-info">
                                <div class="card-value" id="total-users"><?php echo number_format($stats['total_users'] ?? 1234); ?></div>
                                <div class="card-title">总用户数</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-google card-green">
                        <div class="card-content">
                            <div class="card-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="card-info">
                                <div class="card-value" id="active-users"><?php echo number_format($stats['active_users'] ?? 892); ?></div>
                                <div class="card-title">活跃用户</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-google card-orange">
                        <div class="card-content">
                            <div class="card-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="card-info">
                                <div class="card-value" id="new-registrations"><?php echo number_format($stats['new_registrations'] ?? 156); ?></div>
                                <div class="card-title">新注册</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-google card-red">
                        <div class="card-content">
                            <div class="card-icon">
                                <i class="fas fa-sign-in-alt"></i>
                            </div>
                            <div class="card-info">
                                <div class="card-value" id="login-sessions"><?php echo number_format($stats['login_sessions'] ?? 324); ?></div>
                                <div class="card-title">登录会话</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 内容区域 -->
                <div class="dashboard-content">
                    <div class="content-grid">
                        <!-- 快速操作 -->
                        <div class="content-card">
                            <div class="card-header">
                                <h2 class="card-title">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="3"/>
                                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                    </svg>
                                    快速操作
                                </h2>
                            </div>
                            <div class="card-content">
                                <div class="quick-actions">
                                    <a href="#" class="quick-action-btn">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                            <circle cx="8.5" cy="7" r="4"/>
                                            <line x1="20" y1="8" x2="20" y2="14"/>
                                            <line x1="23" y1="11" x2="17" y2="11"/>
                                        </svg>
                                        添加用户
                                    </a>
                                    <a href="#" class="quick-action-btn">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                            <polyline points="14,2 14,8 20,8"/>
                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                            <polyline points="10,9 9,9 8,9"/>
                                        </svg>
                                        生成报告
                                    </a>
                                    <a href="#" class="quick-action-btn">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="12" cy="12" r="3"/>
                                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                        </svg>
                                        系统设置
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 最近活动 -->
                        <div class="content-card">
                            <div class="card-header">
                                <h2 class="card-title">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    最近活动
                                </h2>
                            </div>
                            <div class="card-content">
                                <div class="activity-list">
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/>
                                                <polyline points="10,17 15,12 10,7"/>
                                                <line x1="15" y1="12" x2="3" y2="12"/>
                                            </svg>
                                        </div>
                                        <div class="activity-content">
                                            <p class="activity-text">用户 <?php echo htmlspecialchars($current_user['username']); ?> 登录系统</p>
                                            <span class="activity-time">刚刚</span>
                                        </div>
                                    </div>
                                    
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                <polyline points="14,2 14,8 20,8"/>
                                            </svg>
                                        </div>
                                        <div class="activity-content">
                                            <p class="activity-text">系统备份已完成</p>
                                            <span class="activity-time">2小时前</span>
                                        </div>
                                    </div>
                                    
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                                <circle cx="8.5" cy="7" r="4"/>
                                                <line x1="20" y1="8" x2="20" y2="14"/>
                                                <line x1="23" y1="11" x2="17" y2="11"/>
                                            </svg>
                                        </div>
                                        <div class="activity-content">
                                            <p class="activity-text">新用户注册</p>
                                            <span class="activity-time">5小时前</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- 页脚 -->
        <?php include 'includes/footer.php'; ?>
    </div>

    <!-- 仪表盘特定脚本 -->
    <script>
        /**
         * 仪表盘页面初始化
         */
        function initializeDashboard() {
            console.log('📊 仪表盘页面已加载');
            
            // 加载用户信息
            loadUserInfo();
            
            // 初始化统计数据
            initializeStats();
            
            // 初始化活动列表
            initializeActivityList();
        }
        
        // 加载用户信息
        async function loadUserInfo() {
            try {
                const response = await fetch('../api/user-info.api.php');
                const data = await response.json();
                
                if (data.success) {
                    console.log('用户信息加载成功:', data.user);
                    // 可以在这里更新页面上的用户信息显示
                } else {
                    console.error('用户信息加载失败:', data.message);
                }
            } catch (error) {
                console.error('用户信息加载错误:', error);
            }
        }
        
        // 数字动画函数
        function animateNumber(element, start, end, duration) {
            const startTime = performance.now();
            const startValue = parseInt(start);
            const endValue = parseInt(end);

            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 使用缓动函数
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.floor(startValue + (endValue - startValue) * easeOutQuart);

                element.textContent = currentValue.toLocaleString();

                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }

            requestAnimationFrame(update);
        }

        // 初始化统计数据
        function initializeStats() {
            const cards = document.querySelectorAll('.card-google');

            // 卡片入场动画
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px) scale(0.95)';

                setTimeout(() => {
                    card.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0) scale(1)';
                }, index * 150);
            });

            // 数字动画
            setTimeout(() => {
                const values = document.querySelectorAll('.card-value');
                values.forEach((value, index) => {
                    const finalValue = value.textContent.replace(/,/g, '');

                    setTimeout(() => {
                        animateNumber(value, 0, finalValue, 2000);
                    }, index * 200);
                });
            }, 800);

            // 趋势标签动画
            setTimeout(() => {
                const trends = document.querySelectorAll('.card-trend');
                trends.forEach((trend, index) => {
                    trend.style.opacity = '0';
                    trend.style.transform = 'translateX(-20px)';

                    setTimeout(() => {
                        trend.style.transition = 'all 0.5s ease';
                        trend.style.opacity = '1';
                        trend.style.transform = 'translateX(0)';
                    }, index * 100);
                });
            }, 2000);
        }
        
        // 初始化活动列表
        function initializeActivityList() {
            // 这里可以添加活动列表的加载和更新逻辑
            console.log('活动列表已初始化');
        }
        
        // 当页面加载完成时初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeDashboard);
        } else {
            initializeDashboard();
        }
    </script>
</body>
</html>
