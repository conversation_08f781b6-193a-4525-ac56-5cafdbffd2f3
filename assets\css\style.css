/* 🎨 现代化PHP管理系统 - 主样式文件 */

/* ===== CSS变量定义 ===== */
:root {
    /* 主品牌色 - 蓝色系 */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --primary-light: #dbeafe;
    --primary-dark: #1d4ed8;
    
    /* 功能色彩 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --secondary-color: #64748b;
    
    /* 背景色系 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    
    /* 文字色系 */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    
    /* 边框色系 */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* 间距系统 */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-12: 3rem;
    
    /* 圆角系统 */
    --radius-sm: 0.125rem;
    --radius: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    
    /* 过渡动画 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease;
    --transition-slow: all 0.5s ease;
}

/* 暗色主题 */
[data-theme="dark"] {
    --primary-color: #60a5fa;
    --bg-primary: #1e293b;
    --bg-secondary: #334155;
    --bg-tertiary: #475569;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #475569;
    --border-light: #334155;
}

/* ===== 基础重置和字体 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition);
}

/* ===== 容器系统 ===== */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-8);
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-4);
    }
}

/* ===== 字体大小系统 ===== */
.text-4xl { font-size: 2.25rem; font-weight: 700; }
.text-3xl { font-size: 1.875rem; font-weight: 600; }
.text-2xl { font-size: 1.5rem; font-weight: 600; }
.text-xl { font-size: 1.25rem; font-weight: 500; }
.text-lg { font-size: 1.125rem; font-weight: 500; }
.text-base { font-size: 1rem; font-weight: 400; }
.text-sm { font-size: 0.875rem; font-weight: 400; }
.text-xs { font-size: 0.75rem; font-weight: 400; }

/* 文字颜色 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

/* ===== 按钮组件 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: 12px;
    font-weight: 500;
    font-size: 0.875rem;
    transition: var(--transition);
    cursor: pointer;
    border: none;
    text-decoration: none;
    min-height: 44px;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 主要按钮 */
.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* 次要按钮 */
.btn-outline {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.btn-outline:hover:not(:disabled) {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* 警告按钮 */
.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background: #d97706;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* 危险按钮 */
.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* 按钮尺寸 */
.btn-sm { padding: var(--spacing-2) var(--spacing-4); font-size: 0.75rem; min-height: 36px; }
.btn-lg { padding: var(--spacing-4) var(--spacing-8); font-size: 1rem; min-height: 52px; }

/* ===== 卡片组件 ===== */
.card {
    background: var(--bg-primary);
    border-radius: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--border-light);
}

.card-body {
    padding: var(--spacing-6);
}

.card-footer {
    padding: var(--spacing-6);
    border-top: 1px solid var(--border-light);
    background: var(--bg-secondary);
}

/* ===== 表单组件 ===== */
.form-group {
    margin-bottom: var(--spacing-6);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition);
    min-height: 44px;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control::placeholder {
    color: var(--text-muted);
}

/* ===== 登录页面样式 ===== */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--bg-secondary) 100%);
}

.login-card {
    width: 100%;
    max-width: 400px;
    background: var(--bg-primary);
    border-radius: 24px;
    box-shadow: var(--shadow-2xl);
    border: 1px solid var(--border-light);
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
}

.login-header {
    text-align: center;
    padding: var(--spacing-8) var(--spacing-6) var(--spacing-6);
}

.login-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-2);
}

.login-subtitle {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.login-body {
    padding: 0 var(--spacing-6) var(--spacing-8);
}









/* ===== 动画效果 ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .login-card {
        margin: var(--spacing-4);
        border-radius: 20px;
    }
    
    .login-header {
        padding: var(--spacing-6) var(--spacing-4) var(--spacing-4);
    }
    
    .login-body {
        padding: 0 var(--spacing-4) var(--spacing-6);
    }
    
    .theme-toggle {
        top: var(--spacing-4);
        right: var(--spacing-4);
        width: 44px;
        height: 44px;
    }
}

/* ===== 复选框组件 ===== */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    cursor: pointer;
    user-select: none;
}

.checkbox-input {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    transition: var(--transition);
    position: relative;
    flex-shrink: 0;
}

.checkbox-input:checked + .checkbox-custom {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-input:checked + .checkbox-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: translate(-50%, -60%) rotate(45deg);
}

.checkbox-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* ===== 警告框组件 ===== */
.alert {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: 12px;
    font-size: 0.875rem;
    border: 1px solid;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: var(--danger-color);
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
}

.alert-info {
    background: rgba(6, 182, 212, 0.1);
    border-color: rgba(6, 182, 212, 0.2);
    color: var(--info-color);
}

/* ===== 登录页脚 ===== */
.login-footer {
    position: absolute;
    bottom: var(--spacing-6);
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

/* ===== 表单验证样式 ===== */
.field-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.field-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.field-error {
    margin-top: var(--spacing-1);
    font-size: 0.75rem;
    color: var(--danger-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.field-error::before {
    content: '⚠';
    font-size: 0.875rem;
}

/* ===== Toast 通知样式 ===== */
.toast-container {
    position: fixed;
    top: 80px; /* 导航栏高度64px + 16px间距 */
    right: var(--spacing-6);
    z-index: 9998;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    max-width: 400px;
}

.toast {
    background: var(--bg-primary);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    padding: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    transform: translateX(100%);
    transition: var(--transition);
    min-width: 300px;
}

.toast.show {
    transform: translateX(0);
}

.toast-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
}

.toast-success .toast-icon { color: var(--success-color); }
.toast-error .toast-icon { color: var(--danger-color); }
.toast-warning .toast-icon { color: var(--warning-color); }
.toast-info .toast-icon { color: var(--info-color); }

.toast-content {
    flex: 1;
}

.toast-message {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 500;
}

.toast-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: 4px;
    color: var(--text-muted);
    transition: var(--transition);
    flex-shrink: 0;
}

.toast-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* ===== 动画增强 ===== */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.page-loaded {
    animation: fadeIn 0.5s ease-out;
}

/* ===== 链接样式 ===== */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-hover);
}

.hover\:text-primary:hover {
    color: var(--primary-color);
}

/* ===== 间距工具类 ===== */
.mx-2 { margin-left: var(--spacing-2); margin-right: var(--spacing-2); }
.mt-2 { margin-top: var(--spacing-2); }

/* ===== 工具类 ===== */
.text-center { text-align: center; }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.w-full { width: 100%; }
.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.gap-2 { gap: var(--spacing-2); }
.gap-4 { gap: var(--spacing-4); }

/* ===== 注册页面特有样式 ===== */

/* 密码输入框包装器 */
.password-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-wrapper .form-control {
    padding-right: 48px;
}

/* 密码显示/隐藏切换按钮 */
.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

.password-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 密码强度指示器 */
.password-strength {
    margin-top: var(--spacing-2);
    display: none;
}

.password-strength-bar {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: var(--spacing-1);
}

.password-strength-fill {
    height: 100%;
    transition: var(--transition);
    border-radius: 2px;
}

.password-strength-fill.strength-weak {
    background: var(--danger-color);
}

.password-strength-fill.strength-fair {
    background: var(--warning-color);
}

.password-strength-fill.strength-good {
    background: var(--info-color);
}

.password-strength-fill.strength-strong {
    background: var(--success-color);
}

.password-strength-fill.strength-very-strong {
    background: linear-gradient(90deg, var(--success-color), #059669);
}

.password-strength-text {
    font-size: 0.75rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.password-strength-text span {
    font-weight: 500;
}

/* 表单错误状态 */
.form-control.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 注册页面特定的复选框样式 */
.checkbox-label a {
    color: var(--primary-color);
    font-weight: 500;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

/* 页面底部版权信息样式 */
.page-footer {
    position: absolute;
    bottom: var(--spacing-2);
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    width: 100%;
    max-width: 400px;
    padding: 0 var(--spacing-4);
}

/* ===== 气泡提示（Tooltip）样式 ===== */
[data-tooltip] {
    position: relative;
}

[data-tooltip]:before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 120%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: var(--bg-primary);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 400;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    max-width: 280px;
    white-space: normal;
    text-align: center;
    line-height: 1.4;
}

[data-tooltip]:after {
    content: '';
    position: absolute;
    bottom: 110%;
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: var(--text-primary);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
    opacity: 1;
    visibility: visible;
}

/* 暗色主题下的气泡提示 */
[data-theme="dark"] [data-tooltip]:before {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] [data-tooltip]:after {
    border-top-color: var(--bg-primary);
}

/* ===== 警告框样式 ===== */
.alert {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.5;
    border: 1px solid transparent;
}

.alert svg {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-color: rgba(239, 68, 68, 0.2);
}

.alert-success {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    border-color: rgba(34, 197, 94, 0.2);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border-color: rgba(245, 158, 11, 0.2);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
    border-color: rgba(59, 130, 246, 0.2);
}

/* 暗色主题下的警告框 */
[data-theme="dark"] .alert-error {
    background: rgba(239, 68, 68, 0.15);
    color: #fca5a5;
    border-color: rgba(239, 68, 68, 0.3);
}

[data-theme="dark"] .alert-success {
    background: rgba(34, 197, 94, 0.15);
    color: #86efac;
    border-color: rgba(34, 197, 94, 0.3);
}

[data-theme="dark"] .alert-warning {
    background: rgba(245, 158, 11, 0.15);
    color: #fbbf24;
    border-color: rgba(245, 158, 11, 0.3);
}

[data-theme="dark"] .alert-info {
    background: rgba(59, 130, 246, 0.15);
    color: #93c5fd;
    border-color: rgba(59, 130, 246, 0.3);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .password-toggle {
        right: 8px;
    }

    .password-input-wrapper .form-control {
        padding-right: 40px;
    }

    .page-footer {
        bottom: var(--spacing-1);
        padding: 0 var(--spacing-2);
    }

    [data-tooltip]:before {
        max-width: 240px;
        font-size: 0.7rem;
        padding: var(--spacing-1) var(--spacing-2);
    }
}

/* ===== 用户验证图标样式 ===== */
.user-name-row {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.verified-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: var(--success-color);
    border-radius: 50%;
    color: white;
    margin-left: 8px;
    flex-shrink: 0;
}

.verified-icon svg {
    width: 12px;
    height: 12px;
    stroke-width: 3;
}

/* ===== 统计卡片样式 ===== */
.stats-overview {
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: var(--bg-primary);
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-card-inner {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    position: relative;
}

.stat-icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.stat-primary .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-success .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.stat-warning .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.stat-danger .stat-icon {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.stat-description {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* 统计卡片右上角元素 - 重新设计 */
.stat-trend {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 18px;
    height: 18px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.stat-trend-up {
    background: rgba(16, 185, 129, 0.9);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.25);
}

.stat-percentage {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(16, 185, 129, 0.9);
    color: white;
    padding: 2px 6px;
    border-radius: 6px;
    font-size: 0.65rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.25);
}

.stat-pulse {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 6px;
    height: 6px;
    background: #f59e0b;
    border-radius: 50%;
    animation: pulse 2s infinite;
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
}

.stat-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(239, 68, 68, 0.9);
    color: white;
    padding: 2px 6px;
    border-radius: 6px;
    font-size: 0.65rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.25);
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
    }

    70% {
        transform: scale(1);
        box-shadow: 0 0 0 4px rgba(245, 158, 11, 0);
    }

    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
    }
}