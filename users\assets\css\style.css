/**
 * 📊 仪表盘样式文件
 * 
 * 功能：仪表盘页面的专用样式
 * 设计：现代化、响应式、主题支持
 */

/* ===== 仪表盘布局 ===== */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary);
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.content-wrapper {
    flex: 1;
    padding: var(--spacing-6);
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* ===== 页面标题 ===== */
.page-header {
    margin-bottom: var(--spacing-8);
}

.page-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-2) 0;
}

.page-title svg {
    color: var(--primary-color);
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: 1.125rem;
    margin: 0;
    font-weight: 400;
}

/* ===== 统计卡片网格 ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

/* ===== 扁平化统计卡片 ===== */
.card-google {
    background: var(--bg-primary);
    border-radius: 16px;
    padding: 0;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

[data-theme="dark"] .card-google {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
}

.card-google:hover {
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

[data-theme="dark"] .card-google:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.card-google:hover .card-value {
    color: var(--primary-color);
}

.card-google:hover .card-icon {
    transform: scale(1.05);
}

/* 用户管理页面专用卡片布局 */
.users-page .card-google .card-content {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
}

.users-page .card-google .card-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
}

.users-page .card-google .card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.users-page .card-google .card-title {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0;
    line-height: 1.2;
    white-space: nowrap;
    opacity: 0.9;
}

.users-page .card-google .card-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    text-align: right;
}

.users-page .card-google .card-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.1;
    letter-spacing: -0.02em;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all 0.3s ease;
    font-variant-numeric: tabular-nums;
    white-space: nowrap;
}

.users-page .card-google .card-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 400;
    margin: 0;
    line-height: 1.2;
    opacity: 0.7;
    white-space: nowrap;
}

/* 通用卡片布局（仪表盘等其他页面） */
.card-google .card-content {
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.card-google .card-icon {
    width: 56px;
    height: 56px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    color: white;
    flex-shrink: 0;
}

.card-google .card-info {
    flex: 1;
    min-width: 0;
}

.card-google .card-value {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    line-height: 1.1;
    letter-spacing: -0.02em;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all 0.3s ease;
    font-variant-numeric: tabular-nums;
}

.card-google .card-title {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0;
    line-height: 1.3;
    opacity: 0.85;
    text-transform: none;
    letter-spacing: 0.01em;
}

/* 数字动画效果 */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card-google .card-value.animate {
    animation: countUp 0.6s ease-out;
}

/* ===== 用户管理页面专用卡片样式 ===== */
.users-page .card-google {
    background: var(--bg-primary);
    border-radius: 16px;
    padding: 0;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.users-page .card-google:hover {
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

[data-theme="dark"] .users-page .card-google:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.users-page .card-google .card-content {
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
}

.users-page .card-google .card-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
}

.users-page .card-google .card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.users-page .card-google .card-title {
    font-size: 0.95rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0;
    white-space: nowrap;
    line-height: 1.2;
}

.users-page .card-google .card-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
    min-width: 0;
}

.users-page .card-google .card-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px 0;
    line-height: 1;
    letter-spacing: -0.02em;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all 0.3s ease;
    font-variant-numeric: tabular-nums;
    white-space: nowrap;
}

.users-page .card-google .card-description {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 400;
    margin: 0;
    opacity: 0.8;
    white-space: nowrap;
    line-height: 1.3;
}

.users-page .card-google::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 24px;
    right: 24px;
    height: 1px;
    background: var(--border-color);
    opacity: 0.5;
}

.users-page .card-google:hover .card-value {
    color: var(--primary-color);
}

.users-page .card-google:hover .card-icon {
    transform: scale(1.05);
}

/* 用户管理页面卡片颜色配置 */
.users-page .card-google.card-blue .card-icon {
    background: #4285f4;
}

.users-page .card-google.card-green .card-icon {
    background: #34a853;
}

.users-page .card-google.card-orange .card-icon {
    background: #ff9800;
}

.users-page .card-google.card-red .card-icon {
    background: #ea4335;
}

/* 卡片颜色配置 - 扁平化 */
.card-google.card-blue .card-icon {
    background: #4285f4;
}

.card-google.card-green .card-icon {
    background: #34a853;
}

.card-google.card-orange .card-icon {
    background: #ff9800;
}

.card-google.card-red .card-icon {
    background: #ea4335;
}

/* 保持兼容性的旧样式 */
.stat-icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-1) 0;
    line-height: 1;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
}

/* ===== 内容网格 ===== */
.dashboard-content {
    flex: 1;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-6);
}

/* ===== 内容卡片 ===== */
.content-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
    transition: var(--transition);
}

.content-card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--border-hover);
}

.card-header {
    padding: var(--spacing-6) var(--spacing-6) var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.card-title svg {
    color: var(--primary-color);
}

.card-content {
    padding: var(--spacing-6);
}

/* ===== 快速操作 ===== */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-4);
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
    text-align: center;
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.quick-action-btn svg {
    transition: var(--transition);
}

.quick-action-btn:hover svg {
    transform: scale(1.1);
}

/* ===== 活动列表 ===== */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    border-radius: 8px;
    transition: var(--transition);
}

.activity-item:hover {
    background: var(--bg-secondary);
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0 0 var(--spacing-1) 0;
}

.activity-time {
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 400;
}

/* ===== 个人资料页面样式 ===== */
.profile-content {
    margin-top: var(--spacing-6);
}

.profile-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-6);
    align-items: start;
}

.profile-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.profile-card:hover {
    box-shadow: var(--shadow-md);
}

/* 用户信息卡片 */
.user-info-card .card-content {
    padding: var(--spacing-6);
}

.user-avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: var(--spacing-6);
}

.user-avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-4);
    box-shadow: var(--shadow-lg);
}

.avatar-text-xl {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    text-transform: uppercase;
}

.user-basic-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-1) 0;
}

.user-username {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0 0 var(--spacing-3) 0;
}

.user-role-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-1) var(--spacing-3);
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.user-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3);
    background: var(--bg-secondary);
    border-radius: 8px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.stat-value {
    color: var(--text-primary);
    font-weight: 600;
}

.status-active {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--success-color) !important;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 编辑表单卡片 */
.edit-form-card .card-content {
    padding: var(--spacing-6);
}

.profile-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.form-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    padding-bottom: var(--spacing-2);
    border-bottom: 2px solid var(--border-color);
}

.section-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-group input {
    padding: var(--spacing-3) var(--spacing-4);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-group input:invalid {
    border-color: var(--error-color);
}

.form-actions {
    display: flex;
    gap: var(--spacing-3);
    padding-top: var(--spacing-4);
    border-top: 1px solid var(--border-color);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* 消息提示 */
.alert {
    display: flex;
    align-items: center;
    padding: var(--spacing-4);
    border-radius: 8px;
    margin-bottom: var(--spacing-6);
    border-left: 4px solid;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.alert-success {
    background: rgba(var(--success-rgb), 0.1);
    color: var(--success-color);
    border-left-color: var(--success-color);
}

.alert-error {
    background: rgba(var(--error-rgb), 0.1);
    color: var(--error-color);
    border-left-color: var(--error-color);
}

/* ===== 日志页面样式 ===== */
.logs-content {
    margin-top: var(--spacing-6);
}

.logs-grid {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: var(--spacing-6);
    align-items: start;
}

.logs-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.logs-card:hover {
    box-shadow: var(--shadow-md);
}

/* 筛选卡片 */
.filter-card .card-content {
    padding: var(--spacing-6);
}

.filter-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.filter-form .form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.filter-form label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-form input,
.filter-form select {
    padding: var(--spacing-3) var(--spacing-4);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.9rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: var(--transition);
}

.filter-form input:focus,
.filter-form select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.filter-form .form-actions {
    display: flex;
    gap: var(--spacing-2);
}

.btn-sm {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: 0.8rem;
}

.stats-section {
    padding-top: var(--spacing-4);
    border-top: 1px solid var(--border-color);
}

.stats-section .section-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-3) 0;
}

.stats-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.stats-list .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2);
    background: var(--bg-secondary);
    border-radius: 6px;
}

.stats-list .stat-label {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.stats-list .stat-value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.8rem;
}

/* 日志列表卡片 */
.logs-list-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.card-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.record-count {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.logs-table-container {
    overflow-x: auto;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.logs-table th {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    padding: var(--spacing-3) var(--spacing-4);
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
}

.logs-table td {
    padding: var(--spacing-3) var(--spacing-4);
    border-bottom: 1px solid var(--border-color);
    vertical-align: top;
}

.log-row:hover {
    background: var(--bg-secondary);
}

.time-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.time-info .date {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.85rem;
}

.time-info .time {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.user-info .username {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.85rem;
}

.user-info .email {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-success {
    background: rgba(var(--success-rgb), 0.1);
    color: var(--success-color);
}

.status-failed {
    background: rgba(var(--error-rgb), 0.1);
    color: var(--error-color);
}

.status-blocked {
    background: rgba(var(--warning-rgb), 0.1);
    color: var(--warning-color);
}

.type-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.btn-details {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.btn-details:hover {
    background: var(--primary-color);
    color: white;
}

.no-details {
    color: var(--text-tertiary);
    font-style: italic;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-8);
    text-align: center;
}

.empty-state svg {
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-4);
}

.empty-state h3 {
    color: var(--text-primary);
    margin: 0 0 var(--spacing-2) 0;
    font-size: 1.1rem;
}

.empty-state p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

/* 分页 */
.pagination-container {
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--border-color);
}

.pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    background: var(--bg-secondary);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: white;
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: var(--bg-primary);
    border-radius: 12px;
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.modal-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-6);
    max-height: 60vh;
    overflow-y: auto;
}

/* 详情内容样式 */
.details-content {
    font-size: 0.9rem;
}

.details-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.detail-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4);
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.detail-row:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
}

.detail-label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
    min-width: 80px;
    flex-shrink: 0;
}

.detail-value {
    color: var(--text-secondary);
    line-height: 1.4;
    text-align: right;
    flex: 1;
    margin-left: var(--spacing-4);
}

.null-value {
    color: var(--text-tertiary);
    font-style: italic;
}

.boolean-value {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.boolean-value.true {
    background: rgba(var(--success-rgb), 0.15);
    color: var(--success-color);
    border: 1px solid rgba(var(--success-rgb), 0.3);
}

.boolean-value.false {
    background: rgba(var(--error-rgb), 0.15);
    color: var(--error-color);
    border: 1px solid rgba(var(--error-rgb), 0.3);
}

.email-value {
    color: var(--primary-color);
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    background: rgba(var(--primary-rgb), 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.text-value {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

.json-value {
    background: var(--bg-tertiary);
    padding: var(--spacing-3);
    border-radius: 6px;
    font-size: 0.8rem;
    line-height: 1.4;
    color: var(--text-primary);
    margin: var(--spacing-2) 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    border: 1px solid var(--border-color);
}

.error-message {
    color: var(--error-color);
    background: rgba(var(--error-rgb), 0.1);
    padding: var(--spacing-4);
    border-radius: 8px;
    border-left: 3px solid var(--error-color);
    font-weight: 500;
}











/* 页面加载动画 */
.page-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease;
}

[data-theme="dark"] .page-loading-overlay {
    background: rgba(0, 0, 0, 0.9);
}

.loading-spinner {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    animation: spin 1s linear infinite;
}

.loading-spinner svg {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}



@keyframes searchHighlight {
    0% { background: var(--bg-primary); }
    50% { background: rgba(66, 133, 244, 0.1); }
    100% { background: var(--bg-primary); }
}

/* 键盘快捷键提示 - 显示在搜索框下方 */
.keyboard-shortcut-hint {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-8px);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 0.75rem;
    color: var(--text-secondary);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 1000;
    white-space: nowrap;
    pointer-events: none;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.keyboard-shortcut-hint.show {
    opacity: 1;
    transform: translateX(-50%) translateY(6px);
}

/* 添加小箭头指向搜索框 */
.keyboard-shortcut-hint::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid var(--bg-primary);
    filter: drop-shadow(0 -1px 1px rgba(0, 0, 0, 0.1));
}



.keyboard-shortcut-hint kbd {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 3px 7px;
    font-size: 0.7rem;
    font-family: monospace;
    color: var(--text-primary);
    font-weight: 500;
    margin: 0 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 快捷键提示的动画效果 */
.keyboard-shortcut-hint {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(12px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(6px);
    }
}



/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
    .content-wrapper {
        padding: var(--spacing-4);
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: var(--spacing-4);
    }

    .card-google .card-value {
        font-size: 3rem;
    }

    .card-google .card-icon {
        width: 56px;
        height: 56px;
        font-size: 1.75rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    /* 个人资料页面响应式 */
    .profile-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .user-avatar-large {
        width: 100px;
        height: 100px;
    }

    .avatar-text-xl {
        font-size: 2rem;
    }

    /* 日志页面响应式 */
    .logs-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .logs-table {
        font-size: 0.8rem;
    }

    .logs-table th,
    .logs-table td {
        padding: var(--spacing-2) var(--spacing-3);
    }
}

@media (max-width: 768px) {
    .page-title {
        font-size: 1.75rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .card-google {
        border-radius: 20px;
    }

    .card-google .card-content {
        padding: var(--spacing-5);
    }

    .card-google .card-value {
        font-size: 2.5rem;
    }

    .card-google .card-icon {
        width: 48px;
        height: 48px;
        font-size: 1.5rem;
        border-radius: 16px;
    }

    .card-google .card-header {
        margin-bottom: var(--spacing-4);
    }



    .keyboard-shortcut-hint {
        display: none;
    }

    /* 保持兼容性 */
    .stat-card {
        padding: var(--spacing-4);
    }

    .stat-icon {
        width: 48px;
        height: 48px;
    }

    .stat-number {
        font-size: 2rem;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .content-wrapper {
        padding: var(--spacing-3);
    }

    .page-header {
        margin-bottom: var(--spacing-6);
    }

    .stats-grid {
        margin-bottom: var(--spacing-6);
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-3);
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .card-header,
    .card-content {
        padding: var(--spacing-4);
    }

    /* 个人资料移动端优化 */
    .user-avatar-large {
        width: 80px;
        height: 80px;
    }

    .avatar-text-xl {
        font-size: 1.5rem;
    }

    .user-basic-info h3 {
        font-size: 1.25rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }

    .user-stats {
        gap: var(--spacing-3);
    }

    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-1);
    }

    /* 日志页面移动端优化 */
    .logs-table-container {
        overflow-x: scroll;
        -webkit-overflow-scrolling: touch;
    }

    .logs-table {
        min-width: 600px;
        font-size: 0.75rem;
    }

    .logs-table th,
    .logs-table td {
        padding: var(--spacing-2);
        white-space: nowrap;
    }

    .time-info,
    .user-info {
        min-width: 80px;
    }

    .pagination {
        flex-direction: column;
        gap: var(--spacing-3);
    }

    .modal-content {
        width: 95%;
        margin: var(--spacing-4);
    }

    .modal-body {
        padding: var(--spacing-4);
    }

    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
        padding: var(--spacing-3);
    }

    .detail-label {
        font-size: 0.8rem;
        min-width: auto;
    }

    .detail-value {
        text-align: left;
        margin-left: 0;
        width: 100%;
    }

    .json-value {
        font-size: 0.75rem;
        padding: var(--spacing-2);
    }
}

/* ===== 动画效果 ===== */
.stats-grid {
    animation: fadeInUp 0.6s ease-out;
}

.content-grid {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 统计数字动画 */
.stat-number {
    animation: countUp 1s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===== 暗色主题优化 ===== */
[data-theme="dark"] .stat-card {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .stat-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .content-card {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .content-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .quick-action-btn {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .activity-item:hover {
    background: var(--bg-secondary);
}

[data-theme="dark"] .activity-icon {
    background: var(--bg-secondary);
}

/* ===== 用户管理样式 ===== */

/* 工具栏 */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
    padding: var(--spacing-4);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    flex: 1;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

/* 搜索框 */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 300px;
}

.search-box svg {
    position: absolute;
    left: var(--spacing-3);
    color: var(--text-secondary);
    z-index: 1;
}

.search-box input {
    width: 100%;
    padding: var(--spacing-2) var(--spacing-3) var(--spacing-2) 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* 筛选器 */
.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.filter-select {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    min-width: 120px;
    cursor: pointer;
    transition: all 0.2s ease;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right var(--spacing-2) center;
    background-size: 14px;
    padding-right: 2rem;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

.filter-select:hover {
    border-color: var(--primary-color);
}

/* 用户表格容器 */
.users-table-container {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.table-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.table-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* 表格样式 */
.table-responsive {
    overflow-x: auto;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th {
    padding: var(--spacing-4) var(--spacing-6);
    text-align: left;
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.875rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.users-table td {
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.users-table tbody tr:hover {
    background: var(--bg-hover);
}

/* 用户信息 - 卡片式布局 */
.user-profile-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    min-width: 280px;
}

.user-profile-card:hover {
    background: rgba(var(--primary-rgb), 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar-wrapper {
    position: relative;
    flex-shrink: 0;
}

.user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
}

.user-profile-card:hover .user-avatar {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark, #4f46e5));
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-info-wrapper {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-name-row {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    height: 28px;
    flex-wrap: wrap;
}

.user-display-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    line-height: 1;
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 28px;
    flex-shrink: 0;
}

.user-handle {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 0.2rem 0.5rem;
    border-radius: var(--radius-sm);
    font-weight: 500;
    font-size: 0.75rem;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 22px;
    line-height: 1;
    flex-shrink: 0;
}

.verified-icon {
    display: inline-flex;
    align-items: center;
    color: var(--success-color);
    opacity: 0.9;
    transition: all 0.2s ease;
    height: 28px;
    flex-shrink: 0;
}

.verified-icon:hover {
    opacity: 1;
}

.user-details-row {
    display: flex;
    align-items: center;
    margin-top: 0.25rem;
}

.user-contact {
    color: var(--text-secondary);
    font-size: 0.85rem;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    opacity: 0.8;
    display: flex;
    align-items: center;
    height: 20px;
    line-height: 1;
    position: relative;
}

.user-contact::before {
    content: "✉";
    color: var(--text-secondary);
    margin-right: var(--spacing-1);
    font-size: 0.75rem;
    opacity: 0.6;
    display: flex;
    align-items: center;
    height: 100%;
}

/* 用户卡片悬停效果增强 */
.user-profile-card:hover .user-display-name {
    color: var(--primary-color);
}

.user-profile-card:hover .user-handle {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.user-profile-card:hover .user-contact {
    opacity: 1;
    color: var(--text-primary);
}

.user-profile-card:hover .verified-icon {
    color: var(--success-color);
    opacity: 1;
    transform: scale(1.1);
}

/* 角色和状态徽章 */
.role-badge,
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.role-badge.role-admin {
    background: rgba(var(--error-rgb), 0.1);
    color: var(--error-color);
}

.role-badge.role-user {
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
}

.role-badge.role-guest {
    background: rgba(var(--text-secondary-rgb), 0.1);
    color: var(--text-secondary);
}

.status-badge.status-active {
    background: rgba(var(--success-rgb), 0.1);
    color: var(--success-color);
}

.status-badge.status-inactive {
    background: rgba(var(--warning-rgb), 0.1);
    color: var(--warning-color);
}

.status-badge.status-banned {
    background: rgba(var(--error-rgb), 0.1);
    color: var(--error-color);
}

/* 登录信息 */
.login-info {
    font-size: 0.875rem;
}

.login-time {
    color: var(--text-primary);
    margin-bottom: 2px;
}

.login-ip {
    color: var(--text-secondary);
    font-family: var(--font-mono);
}

.date-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-md);
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.btn-icon.success:hover {
    background: rgba(var(--success-rgb), 0.1);
    color: var(--success-color);
}

.btn-icon.warning:hover {
    background: rgba(var(--warning-rgb), 0.1);
    color: var(--warning-color);
}

.btn-icon.error:hover {
    background: rgba(var(--error-rgb), 0.1);
    color: var(--error-color);
}

/* 分页 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-4);
    padding: var(--spacing-4);
    border-top: 1px solid var(--border-color);
}

.pagination-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    color: var(--text-primary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.pagination-btn:hover {
    background: var(--bg-hover);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-12) var(--spacing-6);
    text-align: center;
}

.empty-state svg {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-4);
    opacity: 0.5;
}

.empty-state h3 {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 var(--spacing-2) 0;
}

.empty-state p {
    color: var(--text-secondary);
    margin: 0;
}

/* 用户表单样式 */
.user-form {
    max-width: none;
}

.user-form .form-section {
    margin-bottom: var(--spacing-6);
}

.user-form .section-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-4) 0;
    padding-bottom: var(--spacing-2);
    border-bottom: 1px solid var(--border-color);
}

.user-form .form-group {
    margin-bottom: var(--spacing-4);
}

.user-form .form-group label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.user-form .form-group input,
.user-form .form-group select {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.user-form .form-group select {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right var(--spacing-3) center;
    background-size: 16px;
    padding-right: 2.5rem;
    cursor: pointer;
}

.user-form .form-group input:focus,
.user-form .form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    background: var(--bg-primary);
}

.user-form .form-group input:hover,
.user-form .form-group select:hover {
    border-color: var(--primary-color);
}

.user-form .form-group select:focus {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

.user-form .form-help {
    display: block;
    margin-top: var(--spacing-1);
    color: var(--text-secondary);
    font-size: 0.75rem;
}

/* 验证码输入框样式 */
.verify-code-input {
    display: flex;
    gap: var(--spacing-3);
    align-items: center;
}

.verify-code-input input {
    flex: 1;
    min-width: 0;
}

.verify-code-input .btn {
    flex-shrink: 0;
    white-space: nowrap;
    min-width: 120px;
    font-size: 0.875rem;
    padding: var(--spacing-3) var(--spacing-4);
}

.verify-code-input .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.verify-code-input .btn.loading {
    position: relative;
    color: transparent;
}

.verify-code-input .btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@media (max-width: 768px) {
    .verify-code-input {
        flex-direction: column;
        align-items: stretch;
    }

    .verify-code-input .btn {
        min-width: auto;
        width: 100%;
    }
}

/* 用户管理响应式设计 */
@media (max-width: 1024px) {
    .toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-3);
    }

    .toolbar-left {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filter-group {
        flex-wrap: wrap;
    }

    .filter-select {
        min-width: auto;
        flex: 1;
    }
}

@media (max-width: 768px) {
    .users-table-container {
        border-radius: var(--radius-md);
        margin: 0 var(--spacing-2);
    }

    .table-responsive {
        margin: 0;
        border-radius: var(--radius-md);
        overflow: hidden;
    }

    .users-table {
        font-size: 0.875rem;
    }

    .users-table th,
    .users-table td {
        padding: var(--spacing-3) var(--spacing-4);
    }

    .user-profile-card {
        gap: var(--spacing-2);
        min-width: 240px;
        padding: var(--spacing-2) var(--spacing-3);
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-width: 2px;
    }

    .avatar-placeholder {
        font-size: 1rem;
    }

    .user-name-row {
        gap: var(--spacing-1);
        height: 26px;
    }

    .user-display-name {
        font-size: 0.9rem;
        max-width: 100px;
        height: 26px;
    }

    .user-handle {
        font-size: 0.7rem;
        padding: 0.15rem 0.4rem;
        height: 20px;
    }

    .user-contact {
        font-size: 0.8rem;
        max-width: 140px;
        height: 18px;
    }

    .user-contact::before {
        margin-right: var(--spacing-1);
    }

    .action-buttons {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .btn-icon {
        width: 28px;
        height: 28px;
    }

    .pagination {
        flex-direction: column;
        gap: var(--spacing-2);
    }

    .pagination-btn {
        padding: var(--spacing-3) var(--spacing-4);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .table-responsive {
        overflow-x: scroll;
        -webkit-overflow-scrolling: touch;
    }

    .users-table {
        min-width: 600px;
        font-size: 0.75rem;
    }

    .users-table th,
    .users-table td {
        padding: var(--spacing-2) var(--spacing-2);
        white-space: nowrap;
    }

    .users-table td:first-child {
        padding-left: var(--spacing-3);
    }

    .users-table td:last-child {
        padding-right: var(--spacing-3);
    }

    .user-profile-card {
        min-width: 200px;
        gap: var(--spacing-2);
        padding: var(--spacing-3);
        margin: 0;
        border-radius: var(--radius-md);
    }

    .user-avatar {
        width: 36px;
        height: 36px;
        border-width: 2px;
    }

    .avatar-placeholder {
        font-size: 0.9rem;
    }

    .user-info-wrapper {
        overflow: hidden;
    }

    .user-name-row {
        flex-wrap: wrap;
        gap: var(--spacing-1);
        height: auto;
    }

    .user-display-name {
        font-size: 0.85rem;
        max-width: 80px;
        height: 22px;
    }

    .user-handle {
        font-size: 0.65rem;
        padding: 0.1rem 0.3rem;
        height: 18px;
    }

    .user-contact {
        font-size: 0.75rem;
        max-width: 120px;
        height: 18px;
        margin-top: 0.1rem;
    }

    .user-contact::before {
        display: none;
    }

    .role-badge,
    .status-badge {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
    }

    .login-info {
        font-size: 0.75rem;
        min-width: 100px;
    }

    .date-info {
        font-size: 0.75rem;
    }

    .action-buttons {
        min-width: 80px;
        justify-content: center;
    }

    .btn-icon {
        width: 24px;
        height: 24px;
    }

    .btn-icon svg {
        width: 14px;
        height: 14px;
    }
}

/* 暗色主题下拉选择框优化 */
[data-theme="dark"] .filter-select {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%239ca3af' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

[data-theme="dark"] .filter-select:focus {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

[data-theme="dark"] .user-form .form-group select {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%239ca3af' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

[data-theme="dark"] .user-form .form-group select:focus {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

/* 选择框选项样式优化 */
.user-form .form-group select option {
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--bg-primary);
    color: var(--text-primary);
    border: none;
    font-size: 0.875rem;
}

.filter-select option {
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--bg-primary);
    color: var(--text-primary);
    border: none;
    font-size: 0.875rem;
}

/* 选择框禁用状态 */
.user-form .form-group select:disabled,
.filter-select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%239ca3af' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

/* 选择框错误状态 */
.user-form .form-group select.error,
.filter-select.error {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(var(--error-rgb), 0.1);
}

/* 选择框成功状态 */
.user-form .form-group select.success,
.filter-select.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(var(--success-rgb), 0.1);
}

/* 选择框动画效果 */
.user-form .form-group select,
.filter-select {
    transform: translateY(0);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-form .form-group select:focus,
.filter-select:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.15), 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* 选择框加载状态 */
.user-form .form-group select.loading,
.filter-select.loading {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M21 12a9 9 0 11-6.219-8.56'/%3e%3c/svg%3e");
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 选择框响应式优化 */
@media (max-width: 768px) {
    .user-form .form-group select,
    .filter-select {
        font-size: 16px; /* 防止iOS缩放 */
        padding: var(--spacing-3) var(--spacing-4);
    }

    .user-form .form-group select {
        background-size: 14px;
        padding-right: 2.25rem;
    }

    .filter-select {
        background-size: 12px;
        padding-right: 1.75rem;
    }
}






