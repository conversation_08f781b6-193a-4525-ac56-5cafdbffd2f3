/**
 * 🚀 现代化PHP管理系统 - 统一组件加载器
 *
 * 功能特性：
 * - 自动加载所有组件CSS和JS文件
 * - 全局持久化存储管理
 * - 统一的组件API调用
 * - 主题切换系统
 * - Toast通知系统
 * - 加载动画系统
 * - 设置面板系统
 *
 * 使用方法：只需在页面中引入这一个文件即可使用所有功能
 * <script src="assets/js/main.js"></script>
 *
 * <AUTHOR>
 * @version 2.0.0
 */

// ===== 全局配置 =====
const CONFIG = {
    // 基础路径配置 - 动态检测
    get basePath() {
        const currentPath = window.location.pathname;
        // 如果在users目录下，需要调整路径
        if (currentPath.includes('/users/')) {
            return '../assets/';
        }
        return 'assets/';
    },

    // 组件文件配置
    components: {
        css: [
            'css/variables.css',
            'css/style.css',
            'css/toast-styles.css',
            'components/settings-panel.css',
            'components/theme-switcher.css',
            'components/loading-spinner.css',
            'components/modal.css',
            'components/captcha.css'
        ],
        js: [
            'components/loading-spinner.js',
            'components/modal.js',
            'components/settings-panel.js',
            'components/theme-switcher.js',
            'components/captcha.js'
        ]
    },

    // 存储键配置
    storage: {
        theme: 'app_theme',
        toastStyle: 'app_toast_style',
        loadingStyle: 'app_loading_style',
        modalPreferences: 'app_modal_preferences',
        settings: 'app_settings'
    },

    // 默认值配置
    defaults: {
        theme: 'light',
        toastStyle: 'modern',
        loadingStyle: 'spinner'
    }
};

// ===== 组件加载器 =====
class ComponentLoader {
    constructor() {
        this.loadedCSS = new Set();
        this.loadedJS = new Set();
        this.loadPromises = new Map();
    }

    /**
     * 加载CSS文件
     */
    async loadCSS(path) {
        const fullPath = CONFIG.basePath + path;

        if (this.loadedCSS.has(fullPath)) {
            return Promise.resolve();
        }

        if (this.loadPromises.has(fullPath)) {
            return this.loadPromises.get(fullPath);
        }

        const promise = new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = fullPath;

            link.onload = () => {
                this.loadedCSS.add(fullPath);
                resolve();
            };

            link.onerror = () => {
                reject(new Error(`Failed to load CSS: ${fullPath}`));
            };

            document.head.appendChild(link);
        });

        this.loadPromises.set(fullPath, promise);
        return promise;
    }

    /**
     * 加载JS文件
     */
    async loadJS(path) {
        const fullPath = CONFIG.basePath + path;

        if (this.loadedJS.has(fullPath)) {
            return Promise.resolve();
        }

        if (this.loadPromises.has(fullPath)) {
            return this.loadPromises.get(fullPath);
        }

        const promise = new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = fullPath;
            script.async = true;

            script.onload = () => {
                this.loadedJS.add(fullPath);
                resolve();
            };

            script.onerror = () => {
                reject(new Error(`Failed to load JS: ${fullPath}`));
            };

            document.head.appendChild(script);
        });

        this.loadPromises.set(fullPath, promise);
        return promise;
    }

    /**
     * 加载所有组件文件
     */
    async loadAllComponents() {
        try {
            // 并行加载所有CSS文件
            const cssPromises = CONFIG.components.css.map(path => this.loadCSS(path));
            await Promise.all(cssPromises);

            // 并行加载所有JS文件
            const jsPromises = CONFIG.components.js.map(path => this.loadJS(path));
            await Promise.all(jsPromises);

            console.log('✅ 所有组件文件加载完成');
            return true;
        } catch (error) {
            console.error('❌ 组件文件加载失败:', error);
            return false;
        }
    }
}

// ===== 全局存储管理器 =====
class StorageManager {
    constructor() {
        this.cache = new Map();
        this.loadAllSettings();
    }

    /**
     * 加载所有设置到缓存
     */
    loadAllSettings() {
        Object.values(CONFIG.storage).forEach(key => {
            const value = localStorage.getItem(key);
            if (value) {
                try {
                    this.cache.set(key, JSON.parse(value));
                } catch {
                    this.cache.set(key, value);
                }
            }
        });
    }

    /**
     * 获取设置值
     */
    get(key, defaultValue = null) {
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }
        return defaultValue;
    }

    /**
     * 设置值
     */
    set(key, value) {
        this.cache.set(key, value);
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch {
            localStorage.setItem(key, value);
        }
    }

    /**
     * 删除设置
     */
    remove(key) {
        this.cache.delete(key);
        localStorage.removeItem(key);
    }

    /**
     * 获取主题设置
     */
    getTheme() {
        return this.get(CONFIG.storage.theme, CONFIG.defaults.theme);
    }

    /**
     * 设置主题
     */
    setTheme(theme) {
        this.set(CONFIG.storage.theme, theme);
        document.documentElement.setAttribute('data-theme', theme);
    }

    /**
     * 获取Toast样式
     */
    getToastStyle() {
        return this.get(CONFIG.storage.toastStyle, CONFIG.defaults.toastStyle);
    }

    /**
     * 设置Toast样式
     */
    setToastStyle(style) {
        this.set(CONFIG.storage.toastStyle, style);
        // 直接设置ToastManager的样式，避免递归调用
        if (window.toastManager) {
            window.toastManager.defaultStyle = style;
        }
    }

    /**
     * 获取加载动画样式
     */
    getLoadingStyle() {
        return this.get(CONFIG.storage.loadingStyle, CONFIG.defaults.loadingStyle);
    }

    /**
     * 设置加载动画样式
     */
    setLoadingStyle(style) {
        this.set(CONFIG.storage.loadingStyle, style);
    }
}

// ===== 表单验证系统 =====
class FormValidator {
    constructor(form) {
        this.form = form;
        this.rules = {};
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // 实时验证
        this.form.addEventListener('input', (e) => {
            if (e.target.classList.contains('form-control')) {
                this.validateField(e.target);
            }
        });

        // 表单提交验证
        this.form.addEventListener('submit', (e) => {
            if (!this.validateForm()) {
                e.preventDefault();
            }
        });
    }

    addRule(fieldName, rule) {
        this.rules[fieldName] = rule;
    }

    validateField(field) {
        const fieldName = field.name;
        const value = field.value.trim();
        const rule = this.rules[fieldName];

        if (!rule) return true;

        let isValid = true;
        let errorMessage = '';

        // 必填验证
        if (rule.required && !value) {
            isValid = false;
            errorMessage = rule.requiredMessage || '此字段为必填项';
        }

        // 最小长度验证
        if (isValid && rule.minLength && value.length < rule.minLength) {
            isValid = false;
            errorMessage = rule.minLengthMessage || `最少需要${rule.minLength}个字符`;
        }

        // 邮箱验证
        if (isValid && rule.email && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = rule.emailMessage || '请输入有效的邮箱地址';
            }
        }

        // 自定义验证
        if (isValid && rule.custom && value) {
            const customResult = rule.custom(value);
            if (customResult !== true) {
                isValid = false;
                errorMessage = customResult;
            }
        }

        this.showFieldValidation(field, isValid, errorMessage);
        return isValid;
    }

    validateForm() {
        let isFormValid = true;
        const fields = this.form.querySelectorAll('.form-control');
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isFormValid = false;
            }
        });

        return isFormValid;
    }

    showFieldValidation(field, isValid, message) {
        // 移除之前的错误信息
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        // 更新字段样式
        field.classList.remove('field-valid', 'field-invalid');
        
        if (field.value.trim()) {
            if (isValid) {
                field.classList.add('field-valid');
            } else {
                field.classList.add('field-invalid');
                
                // 显示错误信息
                const errorElement = document.createElement('div');
                errorElement.className = 'field-error';
                errorElement.textContent = message;
                field.parentNode.appendChild(errorElement);
                
                // 添加摇摆动画
                field.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    field.style.animation = '';
                }, 500);
            }
        }
    }
}

// ===== Toast 通知系统 =====
class ToastManager {
    constructor() {
        this.container = this.createContainer();
        this.toasts = [];
        this.defaultStyle = window.storageManager ?
            window.storageManager.getToastStyle() : 'modern';
    }

    createContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container';
        document.body.appendChild(container);
        return container;
    }

    /**
     * 设置默认样式
     */
    setDefaultStyle(style) {
        this.defaultStyle = style;
        // 直接保存到存储，避免递归调用
        if (window.storageManager) {
            window.storageManager.set(CONFIG.storage.toastStyle, style);
        }
        console.log(`🎨 Toast默认样式已设置为: ${style}`);
    }

    show(message, type = 'info', duration = 4000, customStyle = null) {
        const toast = this.createToast(message, type, customStyle);
        this.container.appendChild(toast);
        this.toasts.push(toast);

        // 显示动画
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            this.hide(toast);
        }, duration);

        return toast;
    }

    createToast(message, type, customStyle = null) {
        const toast = document.createElement('div');
        const style = customStyle || this.defaultStyle;
        toast.className = `toast toast-${type} toast-style-${style}`;

        const icon = this.getIcon(type);
        toast.innerHTML = `
            <div class="toast-icon">${icon}</div>
            <div class="toast-content">
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" aria-label="关闭">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        `;

        // 绑定关闭事件
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => {
            this.hide(toast);
        });

        return toast;
    }

    getIcon(type) {
        const icons = {
            success: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="20,6 9,17 4,12"></polyline>
            </svg>`,
            error: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="15" y1="9" x2="9" y2="15"></line>
                <line x1="9" y1="9" x2="15" y2="15"></line>
            </svg>`,
            warning: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                <line x1="12" y1="9" x2="12" y2="13"></line>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>`,
            info: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>`
        };
        return icons[type] || icons.info;
    }

    hide(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
            const index = this.toasts.indexOf(toast);
            if (index > -1) {
                this.toasts.splice(index, 1);
            }
        }, 300);
    }
}

// ===== 工具函数 =====
const Utils = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day);
    }
};

// ===== 统一应用管理器 =====
class AppManager {
    constructor() {
        this.componentLoader = new ComponentLoader();
        this.storageManager = new StorageManager();
        this.toastManager = null;
        this.modalManager = null;
        this.settingsPanel = null;
        this.initialized = false;
    }

    /**
     * 初始化应用
     */
    async init() {
        if (this.initialized) {
            return;
        }

        try {
            console.log('🚀 开始初始化现代化PHP管理系统...');

            // 0. 显示页面加载动画
            this.showPageLoading();

            // 1. 应用保存的主题
            this.applyTheme();

            // 2. 加载所有组件文件
            await this.componentLoader.loadAllComponents();

            // 3. 初始化核心组件
            this.initCoreComponents();

            // 4. 等待其他组件加载完成
            await this.waitForComponents();

            // 5. 初始化扩展组件
            this.initExtendedComponents();

            // 6. 初始化表单验证
            this.initFormValidation();

            // 7. 添加页面加载完成标记
            document.body.classList.add('page-loaded');

            this.initialized = true;
            console.log('✅ 现代化PHP管理系统初始化完成！');

            // 8. 隐藏页面加载动画
            setTimeout(() => {
                this.hidePageLoading();
            }, 300);

            // 显示欢迎消息
            setTimeout(() => {
                this.showToast('系统已成功加载！', 'success');
            }, 800);

        } catch (error) {
            console.error('❌ 系统初始化失败:', error);
            this.hidePageLoading();
            this.showToast('系统初始化失败，请刷新页面重试', 'error');
        }
    }

    /**
     * 应用主题
     */
    applyTheme() {
        const theme = this.storageManager.getTheme();
        document.documentElement.setAttribute('data-theme', theme);
    }

    /**
     * 显示页面加载动画
     */
    showPageLoading() {
        // 检查是否启用了页面加载动画
        const showLoading = this.storageManager.get('showPageLoading', true);
        if (!showLoading) return;

        // 获取用户设置的加载动画样式
        const loadingStyle = this.storageManager.getLoadingStyle();

        // 创建加载动画覆盖层
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'page-loading-overlay';
        loadingOverlay.style.cssText = `
            position: fixed;
            inset: 0;
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(8px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 1;
            visibility: visible;
            transition: all 0.3s ease;
        `;

        const loadingContent = document.createElement('div');
        loadingContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            padding: 2rem;
            background: var(--bg-primary, #ffffff);
            border-radius: 20px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        `;

        // 根据用户设置的样式创建不同的加载动画
        const spinner = this.createLoadingSpinner(loadingStyle);

        const text = document.createElement('p');
        text.textContent = '页面加载中...';
        text.style.cssText = `
            margin: 0;
            color: var(--text-primary, #1f2937);
            font-weight: 500;
        `;

        // 添加必要的CSS动画
        this.addLoadingAnimationStyles();

        loadingContent.appendChild(spinner);
        loadingContent.appendChild(text);
        loadingOverlay.appendChild(loadingContent);
        document.body.appendChild(loadingOverlay);

        // 保存引用以便后续隐藏
        this.pageLoadingOverlay = loadingOverlay;
    }

    /**
     * 创建加载动画元素
     */
    createLoadingSpinner(style) {
        const container = document.createElement('div');
        container.style.cssText = `
            width: 40px;
            height: 40px;
            position: relative;
        `;

        switch (style) {
            case 'spinner':
                container.innerHTML = `
                    <div style="
                        width: 100%;
                        height: 100%;
                        border: 3px solid var(--border-color, #e5e7eb);
                        border-top: 3px solid var(--primary-color, #3b82f6);
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    "></div>
                `;
                break;

            case 'dots':
                container.innerHTML = `
                    <div style="
                        display: flex;
                        gap: 4px;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                    ">
                        <div style="
                            width: 8px;
                            height: 8px;
                            background: var(--primary-color, #3b82f6);
                            border-radius: 50%;
                            animation: dots-bounce 1.4s ease-in-out infinite both;
                            animation-delay: -0.32s;
                        "></div>
                        <div style="
                            width: 8px;
                            height: 8px;
                            background: var(--primary-color, #3b82f6);
                            border-radius: 50%;
                            animation: dots-bounce 1.4s ease-in-out infinite both;
                            animation-delay: -0.16s;
                        "></div>
                        <div style="
                            width: 8px;
                            height: 8px;
                            background: var(--primary-color, #3b82f6);
                            border-radius: 50%;
                            animation: dots-bounce 1.4s ease-in-out infinite both;
                        "></div>
                    </div>
                `;
                break;

            case 'wave':
                container.innerHTML = `
                    <div style="
                        display: flex;
                        gap: 2px;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                    ">
                        <div style="
                            width: 4px;
                            height: 20px;
                            background: var(--primary-color, #3b82f6);
                            border-radius: 2px;
                            animation: wave-scale 1.2s ease-in-out infinite;
                            animation-delay: 0s;
                        "></div>
                        <div style="
                            width: 4px;
                            height: 20px;
                            background: var(--primary-color, #3b82f6);
                            border-radius: 2px;
                            animation: wave-scale 1.2s ease-in-out infinite;
                            animation-delay: 0.1s;
                        "></div>
                        <div style="
                            width: 4px;
                            height: 20px;
                            background: var(--primary-color, #3b82f6);
                            border-radius: 2px;
                            animation: wave-scale 1.2s ease-in-out infinite;
                            animation-delay: 0.2s;
                        "></div>
                        <div style="
                            width: 4px;
                            height: 20px;
                            background: var(--primary-color, #3b82f6);
                            border-radius: 2px;
                            animation: wave-scale 1.2s ease-in-out infinite;
                            animation-delay: 0.3s;
                        "></div>
                        <div style="
                            width: 4px;
                            height: 20px;
                            background: var(--primary-color, #3b82f6);
                            border-radius: 2px;
                            animation: wave-scale 1.2s ease-in-out infinite;
                            animation-delay: 0.4s;
                        "></div>
                    </div>
                `;
                break;

            case 'bounce':
                container.innerHTML = `
                    <div style="
                        display: flex;
                        gap: 6px;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                    ">
                        <div style="
                            width: 10px;
                            height: 10px;
                            border-radius: 50%;
                            background: var(--primary-color, #3b82f6);
                            animation: bounce-ball 1.4s ease-in-out infinite both;
                            animation-delay: -0.32s;
                        "></div>
                        <div style="
                            width: 10px;
                            height: 10px;
                            border-radius: 50%;
                            background: var(--primary-color, #3b82f6);
                            animation: bounce-ball 1.4s ease-in-out infinite both;
                            animation-delay: -0.16s;
                        "></div>
                        <div style="
                            width: 10px;
                            height: 10px;
                            border-radius: 50%;
                            background: var(--primary-color, #3b82f6);
                            animation: bounce-ball 1.4s ease-in-out infinite both;
                            animation-delay: 0s;
                        "></div>
                    </div>
                `;
                break;

            case 'ring':
                container.innerHTML = `
                    <div style="
                        width: 100%;
                        height: 100%;
                        border: 3px solid transparent;
                        border-top: 3px solid var(--primary-color, #3b82f6);
                        border-right: 3px solid var(--primary-color, #3b82f6);
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    "></div>
                `;
                break;

            case 'pulse':
                container.innerHTML = `
                    <div style="
                        width: 20px;
                        height: 20px;
                        background: var(--primary-color, #3b82f6);
                        border-radius: 50%;
                        animation: pulse-scale 1.5s ease-in-out infinite;
                        margin: 10px auto;
                    "></div>
                `;
                break;

            default: // 默认使用spinner
                container.innerHTML = `
                    <div style="
                        width: 100%;
                        height: 100%;
                        border: 3px solid var(--border-color, #e5e7eb);
                        border-top: 3px solid var(--primary-color, #3b82f6);
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    "></div>
                `;
        }

        return container;
    }

    /**
     * 添加加载动画CSS样式
     */
    addLoadingAnimationStyles() {
        // 检查是否已经添加过样式
        if (document.getElementById('page-loading-styles')) return;

        const style = document.createElement('style');
        style.id = 'page-loading-styles';
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            @keyframes dots-bounce {
                0%, 80%, 100% {
                    transform: scale(0);
                }
                40% {
                    transform: scale(1);
                }
            }

            @keyframes wave-scale {
                0%, 40%, 100% {
                    transform: scaleY(0.4);
                }
                20% {
                    transform: scaleY(1);
                }
            }

            @keyframes bounce-ball {
                0%, 80%, 100% {
                    transform: scale(0) translateY(0);
                }
                40% {
                    transform: scale(1) translateY(-20px);
                }
            }

            @keyframes pulse-scale {
                0% {
                    transform: scale(0);
                    opacity: 1;
                }
                100% {
                    transform: scale(1);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 隐藏页面加载动画
     */
    hidePageLoading() {
        if (this.pageLoadingOverlay) {
            this.pageLoadingOverlay.style.opacity = '0';
            this.pageLoadingOverlay.style.visibility = 'hidden';

            setTimeout(() => {
                if (this.pageLoadingOverlay && this.pageLoadingOverlay.parentNode) {
                    this.pageLoadingOverlay.parentNode.removeChild(this.pageLoadingOverlay);
                }
                this.pageLoadingOverlay = null;
            }, 300);
        }
    }

    /**
     * 初始化核心组件
     */
    initCoreComponents() {
        // 初始化Toast管理器
        this.toastManager = new ToastManager();
        window.toastManager = this.toastManager;

        // 初始化模态弹窗管理器
        if (window.ModalComponent) {
            this.modalManager = new ModalComponent({
                storageKey: CONFIG.storage.modalPreferences,
                autoInit: true
            });
            window.modalManager = this.modalManager;
        }

        // 初始化加载动画管理器
        if (window.LoadingSpinner) {
            this.loadingManager = window.LoadingSpinner;
            window.loadingManager = this.loadingManager;

            // 设置默认加载动画样式
            const loadingStyle = this.storageManager.getLoadingStyle();
            window.LoadingSpinner.defaultType = loadingStyle;
        }
    }

    /**
     * 等待组件加载完成
     */
    async waitForComponents() {
        // 等待设置面板组件加载
        let attempts = 0;
        const maxAttempts = 50; // 5秒超时

        while (attempts < maxAttempts) {
            if (window.SettingsPanel) {
                break;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }
    }

    /**
     * 初始化扩展组件
     */
    initExtendedComponents() {
        // 初始化主题切换器
        if (window.ThemeSwitcher) {
            this.themeSwitcher = new ThemeSwitcher({
                position: 'bottom-right',
                showToast: true,
                autoInit: true
            });
            window.themeSwitcher = this.themeSwitcher;
        }

        // 初始化设置面板
        if (window.SettingsPanel) {
            this.settingsPanel = new SettingsPanel({
                autoInit: true,
                storageManager: this.storageManager,
                toastManager: this.toastManager
            });
            window.settingsPanel = this.settingsPanel;
        }
    }

    /**
     * 初始化表单验证
     */
    initFormValidation() {
        const forms = document.querySelectorAll('form[data-validate]');
        forms.forEach(form => {
            const validator = new FormValidator(form);

            // 为登录表单添加验证规则
            if (form.id === 'loginForm') {
                validator.addRule('username', {
                    required: true,
                    requiredMessage: '请输入用户名',
                    minLength: 3,
                    minLengthMessage: '用户名至少需要3个字符'
                });

                validator.addRule('password', {
                    required: true,
                    requiredMessage: '请输入密码',
                    minLength: 6,
                    minLengthMessage: '密码至少需要6个字符'
                });
            }

            // 为找回密码表单添加验证规则
            if (form.id === 'forgotPasswordForm') {
                validator.addRule('email', {
                    required: true,
                    requiredMessage: '请输入邮箱地址',
                    email: true,
                    emailMessage: '请输入有效的邮箱地址'
                });
            }

            // 为重置密码表单添加验证规则
            if (form.id === 'resetPasswordForm') {
                validator.addRule('password', {
                    required: true,
                    requiredMessage: '请输入新密码',
                    minLength: 6,
                    minLengthMessage: '密码至少需要6个字符'
                });

                validator.addRule('confirm_password', {
                    required: true,
                    requiredMessage: '请确认新密码',
                    custom: (value) => {
                        const password = form.querySelector('#password').value;
                        return value === password || '两次输入的密码不一致';
                    }
                });
            }
        });
    }

    /**
     * 显示Toast通知
     */
    showToast(message, type = 'info', duration = 4000) {
        if (this.toastManager) {
            return this.toastManager.show(message, type, duration);
        }
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const currentTheme = this.storageManager.getTheme();
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.storageManager.setTheme(newTheme);
        this.showToast(`已切换到${newTheme === 'dark' ? '暗色' : '亮色'}主题`, 'info');
    }

    /**
     * 获取存储管理器
     */
    getStorageManager() {
        return this.storageManager;
    }

    /**
     * 获取Toast管理器
     */
    getToastManager() {
        return this.toastManager;
    }

    /**
     * 获取设置面板
     */
    getSettingsPanel() {
        return this.settingsPanel;
    }
}

// ===== 全局初始化 =====
// 创建全局应用管理器实例
window.appManager = new AppManager();
window.storageManager = window.appManager.getStorageManager();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.appManager.init();
});

// ===== 全局API导出 =====
window.Utils = Utils;

// 便捷的全局函数
window.showToast = function(message, type = 'info', duration = 4000) {
    return window.appManager.showToast(message, type, duration);
};

window.showLoading = function(options = {}) {
    if (window.LoadingSpinner) {
        return window.LoadingSpinner.show(options);
    } else {
        console.warn('加载动画组件尚未初始化');
        return null;
    }
};

window.hideLoading = function(instance = null) {
    if (instance && typeof instance.hide === 'function') {
        return instance.hide();
    } else if (window.LoadingSpinner && typeof window.LoadingSpinner.hideAll === 'function') {
        return window.LoadingSpinner.hideAll();
    } else {
        console.warn('加载动画组件尚未初始化');
    }
};

window.toggleTheme = function() {
    return window.appManager.toggleTheme();
};

window.getSettings = function(key, defaultValue = null) {
    return window.storageManager.get(key, defaultValue);
};

window.setSettings = function(key, value) {
    return window.storageManager.set(key, value);
};

// 模态弹窗便捷函数
window.showModal = {
    info: function(title, content, options = {}) {
        if (window.modalManager) {
            return window.modalManager.info(title, content, options);
        } else {
            console.warn('模态弹窗管理器尚未初始化');
            return Promise.resolve();
        }
    },
    success: function(title, content, options = {}) {
        if (window.modalManager) {
            return window.modalManager.success(title, content, options);
        } else {
            console.warn('模态弹窗管理器尚未初始化');
            return Promise.resolve();
        }
    },
    warning: function(title, content, options = {}) {
        if (window.modalManager) {
            return window.modalManager.warning(title, content, options);
        } else {
            console.warn('模态弹窗管理器尚未初始化');
            return Promise.resolve();
        }
    },
    error: function(title, content, options = {}) {
        if (window.modalManager) {
            return window.modalManager.error(title, content, options);
        } else {
            console.warn('模态弹窗管理器尚未初始化');
            return Promise.resolve();
        }
    },
    confirm: function(title, content, options = {}) {
        if (window.modalManager) {
            return window.modalManager.confirm(title, content, options);
        } else {
            console.warn('模态弹窗管理器尚未初始化');
            return Promise.resolve();
        }
    },
    custom: function(options = {}) {
        if (window.modalManager) {
            return window.modalManager.custom(options);
        } else {
            console.warn('模态弹窗管理器尚未初始化');
            return Promise.resolve();
        }
    }
};
