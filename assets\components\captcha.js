/**
 * 🔐 验证码组件
 * 
 * 功能：提供各种类型的验证码生成和验证
 * 支持：数学计算、字母数字组合、滑块验证
 */

class CaptchaComponent {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        this.options = {
            type: 'math', // math, alphanumeric, slider
            difficulty: 'medium', // easy, medium, hard
            onSuccess: null,
            onError: null,
            ...options
        };
        
        this.sessionKey = null;
        this.isVerified = false;
        
        this.init();
    }
    
    /**
     * 初始化验证码
     */
    async init() {
        if (!this.container) {
            console.error('验证码容器不存在');
            return;
        }
        
        this.container.className = 'captcha-container';
        await this.generate();
    }
    
    /**
     * 生成验证码
     */
    async generate() {
        try {
            const response = await fetch('../api/captcha.api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'generate',
                    type: this.options.type,
                    difficulty: this.options.difficulty
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.sessionKey = result.session_key;
                this.isVerified = false;
                this.render(result);
            } else {
                throw new Error(result.message || '生成验证码失败');
            }
        } catch (error) {
            console.error('生成验证码错误:', error);
            this.showError('生成验证码失败，请刷新页面重试');
        }
    }
    
    /**
     * 渲染验证码界面
     */
    render(data) {
        let html = '';
        
        switch (this.options.type) {
            case 'math':
                html = this.renderMathCaptcha(data);
                break;
            case 'alphanumeric':
                html = this.renderAlphanumericCaptcha(data);
                break;
            case 'slider':
                html = this.renderSliderCaptcha(data);
                break;
        }
        
        this.container.innerHTML = html;
        this.bindEvents();
    }
    
    /**
     * 渲染数学计算验证码
     */
    renderMathCaptcha(data) {
        return `
            <div class="captcha-math">
                <div class="captcha-question">
                    <span class="math-expression">${data.question}</span>
                </div>
                <div class="captcha-input">
                    <input type="number" class="captcha-answer" placeholder="请输入答案" required>
                    <button type="button" class="captcha-refresh" onclick="this.closest('.captcha-container').captchaInstance.refresh()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="1,4 1,10 7,10"/>
                            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                        </svg>
                    </button>
                </div>
            </div>
        `;
    }
    
    /**
     * 渲染字母数字验证码
     */
    renderAlphanumericCaptcha(data) {
        return `
            <div class="captcha-alphanumeric">
                <div class="captcha-image">
                    <div class="code-display">${data.question}</div>
                </div>
                <div class="captcha-input">
                    <input type="text" class="captcha-answer" placeholder="请输入验证码" maxlength="6" required>
                    <button type="button" class="captcha-refresh" onclick="this.closest('.captcha-container').captchaInstance.refresh()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="1,4 1,10 7,10"/>
                            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                        </svg>
                    </button>
                </div>
            </div>
        `;
    }
    
    /**
     * 渲染滑块验证码
     */
    renderSliderCaptcha(data) {
        return `
            <div class="captcha-slider">
                <div class="slider-track">
                    <div class="slider-bg">
                        <span class="slider-text">拖动滑块完成验证</span>
                    </div>
                    <div class="slider-thumb" draggable="false">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="9,18 15,12 9,6"/>
                        </svg>
                    </div>
                </div>
                <button type="button" class="captcha-refresh" onclick="this.closest('.captcha-container').captchaInstance.refresh()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="1,4 1,10 7,10"/>
                        <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                    </svg>
                    重新验证
                </button>
            </div>
        `;
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 保存实例引用到容器
        this.container.captchaInstance = this;
        
        const answerInput = this.container.querySelector('.captcha-answer');
        if (answerInput) {
            answerInput.addEventListener('input', () => {
                if (this.options.type === 'math' || this.options.type === 'alphanumeric') {
                    this.autoVerify();
                }
            });
            
            answerInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.verify();
                }
            });
        }
        
        // 滑块事件
        const sliderThumb = this.container.querySelector('.slider-thumb');
        if (sliderThumb) {
            this.initSliderEvents(sliderThumb);
        }
    }
    
    /**
     * 初始化滑块事件
     */
    initSliderEvents(thumb) {
        let isDragging = false;
        let startX = 0;
        let currentX = 0;
        const track = thumb.parentElement;
        const trackWidth = track.offsetWidth - thumb.offsetWidth;
        
        const onMouseDown = (e) => {
            isDragging = true;
            startX = e.clientX - thumb.offsetLeft;
            thumb.style.transition = 'none';
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        };
        
        const onMouseMove = (e) => {
            if (!isDragging) return;
            
            currentX = e.clientX - startX;
            currentX = Math.max(0, Math.min(currentX, trackWidth));
            thumb.style.left = currentX + 'px';
            
            // 检查是否完成
            if (currentX >= trackWidth * 0.9) {
                this.completeSlider();
            }
        };
        
        const onMouseUp = () => {
            isDragging = false;
            thumb.style.transition = 'left 0.3s ease';
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
            
            // 如果没有完成，回弹
            if (currentX < trackWidth * 0.9) {
                thumb.style.left = '0px';
            }
        };
        
        thumb.addEventListener('mousedown', onMouseDown);
        
        // 触摸事件
        thumb.addEventListener('touchstart', (e) => {
            const touch = e.touches[0];
            onMouseDown({ clientX: touch.clientX });
        });
        
        thumb.addEventListener('touchmove', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            onMouseMove({ clientX: touch.clientX });
        });
        
        thumb.addEventListener('touchend', onMouseUp);
    }
    
    /**
     * 完成滑块验证
     */
    async completeSlider() {
        const track = this.container.querySelector('.slider-track');
        track.classList.add('success');
        
        const bg = track.querySelector('.slider-bg');
        bg.innerHTML = '<span class="slider-text">✓ 验证成功</span>';
        
        this.isVerified = true;
        
        if (this.options.onSuccess) {
            this.options.onSuccess();
        }
    }
    
    /**
     * 自动验证（输入完成后）
     */
    autoVerify() {
        const answerInput = this.container.querySelector('.captcha-answer');
        const answer = answerInput.value.trim();
        
        // 根据类型判断是否输入完成
        let shouldVerify = false;
        if (this.options.type === 'math' && answer && !isNaN(answer)) {
            shouldVerify = true;
        } else if (this.options.type === 'alphanumeric' && answer.length >= 4) {
            shouldVerify = true;
        }
        
        if (shouldVerify) {
            setTimeout(() => this.verify(), 500); // 延迟验证，避免频繁请求
        }
    }
    
    /**
     * 验证答案
     */
    async verify() {
        if (this.options.type === 'slider') {
            return this.isVerified;
        }
        
        const answerInput = this.container.querySelector('.captcha-answer');
        const answer = answerInput.value.trim();
        
        if (!answer) {
            this.showError('请输入验证码');
            return false;
        }
        
        try {
            const response = await fetch('../api/captcha.api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'verify',
                    answer: answer,
                    session_key: this.sessionKey
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.isVerified = true;
                this.showSuccess('验证成功');
                if (this.options.onSuccess) {
                    this.options.onSuccess();
                }
                return true;
            } else {
                this.showError(result.message || '验证失败');
                this.refresh();
                return false;
            }
        } catch (error) {
            console.error('验证错误:', error);
            this.showError('验证失败，请重试');
            return false;
        }
    }
    
    /**
     * 刷新验证码
     */
    async refresh() {
        await this.generate();
    }
    
    /**
     * 显示成功消息
     */
    showSuccess(message) {
        this.container.classList.add('success');
        this.container.classList.remove('error');
    }
    
    /**
     * 显示错误消息
     */
    showError(message) {
        this.container.classList.add('error');
        this.container.classList.remove('success');
        
        if (this.options.onError) {
            this.options.onError(message);
        }
    }
    
    /**
     * 获取验证状态
     */
    isValid() {
        return this.isVerified;
    }
    
    /**
     * 重置验证码
     */
    reset() {
        this.isVerified = false;
        this.sessionKey = null;
        this.container.classList.remove('success', 'error');
        this.refresh();
    }
}

// 全局注册
window.CaptchaComponent = CaptchaComponent;
