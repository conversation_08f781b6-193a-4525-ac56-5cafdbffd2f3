<?php
/**
 * 🔐 现代化PHP管理系统 - 认证中间件
 *
 * 功能：统一的权限验证、登录验证、身份验证系统
 * 特色：会话管理、权限控制、安全验证、数据库集成
 */

// 引入数据库配置
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/SystemConfig.php';

// 确保会话已启动
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

class Auth {

    // 用户角色常量
    const ROLE_ADMIN = 'admin';
    const ROLE_USER = 'user';
    const ROLE_GUEST = 'guest';

    // 会话键名
    const SESSION_USER_ID = 'user_id';
    const SESSION_USERNAME = 'username';
    const SESSION_USER_ROLE = 'user_role';
    const SESSION_LOGIN_TIME = 'login_time';
    const SESSION_LAST_ACTIVITY = 'last_activity';

    // 会话超时时间（秒）
    const SESSION_TIMEOUT = 7200; // 2小时

    // 数据库实例
    private static $db = null;

    /**
     * 获取数据库实例
     */
    private static function getDB() {
        if (self::$db === null) {
            self::$db = Database::getInstance();
        }
        return self::$db;
    }

    /**
     * 记录登录日志
     */
    private static function logLogin($user_id, $username, $email, $status, $failure_reason = null) {
        try {
            $db = self::getDB();
            $ip = self::getClientIP();
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $device_info = self::parseUserAgent($user_agent);
            $location = self::getLocationByIP($ip);

            $db->insert(
                "INSERT INTO login_logs (user_id, username, email, ip_address, user_agent, device_info, location, login_status, failure_reason, session_id)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                [$user_id, $username, $email, $ip, $user_agent, $device_info, $location, $status, $failure_reason, session_id()]
            );
        } catch (Exception $e) {
            error_log("登录日志记录失败: " . $e->getMessage());
        }
    }

    /**
     * 记录操作日志
     */
    public static function logOperation($operation_type, $operation_desc, $operation_data = null) {
        try {
            $db = self::getDB();
            $user = self::getCurrentUser();
            $ip = self::getClientIP();
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $request_url = $_SERVER['REQUEST_URI'] ?? '';
            $request_method = $_SERVER['REQUEST_METHOD'] ?? '';

            $db->insert(
                "INSERT INTO operation_logs (user_id, username, operation_type, operation_desc, operation_data, ip_address, user_agent, request_url, request_method)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    $user['id'] ?? null,
                    $user['username'] ?? null,
                    $operation_type,
                    $operation_desc,
                    $operation_data ? json_encode($operation_data) : null,
                    $ip,
                    $user_agent,
                    $request_url,
                    $request_method
                ]
            );
        } catch (Exception $e) {
            error_log("操作日志记录失败: " . $e->getMessage());
        }
    }

    /**
     * 检查用户是否已登录
     */
    public static function isLoggedIn() {
        return isset($_SESSION[self::SESSION_USER_ID]) &&
               isset($_SESSION[self::SESSION_USERNAME]) &&
               isset($_SESSION[self::SESSION_USER_ROLE]) &&
               self::isSessionValid();
    }

    /**
     * 验证会话是否有效
     */
    public static function isSessionValid() {
        // 检查会话是否超时
        if (isset($_SESSION[self::SESSION_LAST_ACTIVITY])) {
            $inactive_time = time() - $_SESSION[self::SESSION_LAST_ACTIVITY];

            // 根据记住我状态确定超时时间
            $timeout = self::getSessionTimeout();

            if ($inactive_time > $timeout) {
                self::logout();
                return false;
            }
        }

        // 如果启用了记住我，检查记住我是否过期
        if (self::isRemembered()) {
            $rememberExpires = self::getRememberExpires();
            if ($rememberExpires && time() > $rememberExpires) {
                self::logout();
                return false;
            }
        }

        // 更新最后活动时间
        $_SESSION[self::SESSION_LAST_ACTIVITY] = time();
        return true;
    }

    /**
     * 获取会话超时时间
     */
    public static function getSessionTimeout() {
        // 如果启用了记住我，使用记住我的超时时间
        if (self::isRemembered()) {
            return SystemConfig::getRememberMeDuration();
        }

        // 否则使用普通会话超时时间
        return SystemConfig::getSessionTimeout();
    }

    /**
     * 用户登录
     */
    public static function login($username, $password) {
        try {
            $db = self::getDB();

            // 查询用户信息
            $user = $db->fetch(
                "SELECT id, username, email, password, nickname, role, user_group, status, email_verified
                 FROM users WHERE (username = ? OR email = ?) AND status = 'active'",
                [$username, $username]
            );

            if (!$user) {
                self::logLogin(null, $username, null, 'failed', '用户不存在');
                return [
                    'success' => false,
                    'message' => '用户名或密码错误'
                ];
            }

            // 验证密码
            if (!password_verify($password, $user['password'])) {
                self::logLogin($user['id'], $user['username'], $user['email'], 'failed', '密码错误');
                return [
                    'success' => false,
                    'message' => '用户名或密码错误'
                ];
            }

            // 检查邮箱是否验证
            if (!$user['email_verified']) {
                self::logLogin($user['id'], $user['username'], $user['email'], 'failed', '邮箱未验证');
                return [
                    'success' => false,
                    'message' => '请先验证邮箱后再登录'
                ];
            }

            // 更新用户登录信息
            $ip = self::getClientIP();
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $device_info = self::parseUserAgent($user_agent);
            $location = self::getLocationByIP($ip);

            $db->update(
                "UPDATE users SET
                 last_login_time = NOW(),
                 last_login_ip = ?,
                 last_login_device = ?,
                 last_login_location = ?,
                 login_count = login_count + 1
                 WHERE id = ?",
                [$ip, $device_info, $location, $user['id']]
            );

            // 设置会话信息
            $_SESSION[self::SESSION_USER_ID] = $user['id'];
            $_SESSION[self::SESSION_USERNAME] = $user['username'];
            $_SESSION[self::SESSION_USER_ROLE] = $user['role'];
            $_SESSION[self::SESSION_LOGIN_TIME] = time();
            $_SESSION[self::SESSION_LAST_ACTIVITY] = time();

            // 存储用户详细信息
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_name'] = $user['nickname'] ?: $user['username'];
            $_SESSION['user_group'] = $user['user_group'];

            // 记录成功登录日志
            self::logLogin($user['id'], $user['username'], $user['email'], 'success');

            // 记录操作日志
            self::logOperation('login', '用户登录', [
                'username' => $user['username'],
                'ip' => $ip,
                'device' => $device_info
            ]);

            return [
                'success' => true,
                'message' => '登录成功',
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'name' => $user['nickname'] ?: $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'group' => $user['user_group']
                ]
            ];

        } catch (Exception $e) {
            error_log("登录错误: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '系统错误，请稍后重试'
            ];
        }
    }

    /**
     * 用户注册
     */
    public static function register($username, $email, $password, $nickname = null) {
        try {
            $db = self::getDB();

            // 检查用户名是否已存在
            $existing_user = $db->fetch(
                "SELECT id FROM users WHERE username = ?",
                [$username]
            );

            if ($existing_user) {
                return [
                    'success' => false,
                    'message' => '用户名已存在'
                ];
            }

            // 检查邮箱是否已存在
            $existing_email = $db->fetch(
                "SELECT id FROM users WHERE email = ?",
                [$email]
            );

            if ($existing_email) {
                return [
                    'success' => false,
                    'message' => '邮箱已被注册'
                ];
            }

            // 密码加密
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // 生成邮箱验证令牌
            $email_verify_token = bin2hex(random_bytes(32));

            // 插入新用户
            $user_id = $db->insert(
                "INSERT INTO users (username, email, password, nickname, email_verify_token, created_at)
                 VALUES (?, ?, ?, ?, ?, NOW())",
                [$username, $email, $hashed_password, $nickname ?: $username, $email_verify_token]
            );

            // 分配默认用户分组
            try {
                require_once __DIR__ . '/../users/includes/MembershipManager.php';
                MembershipManager::assignDefaultGroup($user_id);
            } catch (Exception $e) {
                error_log("分配默认分组失败: " . $e->getMessage());
                // 不影响注册流程，只记录错误
            }

            // 记录操作日志
            self::logOperation('register', '用户注册', [
                'username' => $username,
                'email' => $email,
                'ip' => self::getClientIP()
            ]);

            // 发送邮箱验证邮件
            $email_sent = false;
            $email_message = '';
            try {
                $email_sent = self::sendRegistrationEmail($email, $email_verify_token);
                $email_message = '注册成功，验证邮件已发送，请查收邮箱';
            } catch (Exception $e) {
                error_log("发送注册验证邮件失败: " . $e->getMessage());
                $email_message = '注册成功，但验证邮件发送失败，请稍后重新发送';
            }

            return [
                'success' => true,
                'message' => $email_message,
                'user_id' => $user_id,
                'email_sent' => $email_sent
            ];

        } catch (Exception $e) {
            error_log("注册错误: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '系统错误，请稍后重试'
            ];
        }
    }

    /**
     * 用户登出
     */
    public static function logout() {
        // 清除所有会话数据
        $_SESSION = [];

        // 删除会话cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }

        // 销毁会话
        session_destroy();

        return [
            'success' => true,
            'message' => '已安全退出'
        ];
    }

    /**
     * 设置记住我功能
     */
    public static function setRememberMe() {
        // 获取记住我持续时间（从系统配置）
        $rememberDuration = SystemConfig::getRememberMeDuration();

        // 设置会话标记
        $_SESSION['remember_me'] = true;
        $_SESSION['remember_expires'] = time() + $rememberDuration;

        // 设置更长的会话cookie过期时间
        $cookieParams = session_get_cookie_params();
        setcookie(
            session_name(),
            session_id(),
            time() + $rememberDuration,
            $cookieParams['path'],
            $cookieParams['domain'],
            $cookieParams['secure'],
            $cookieParams['httponly']
        );

        // 注意：会话已启动后无法修改ini设置，这里只设置cookie即可

        return true;
    }

    /**
     * 检查记住我状态
     */
    public static function isRemembered() {
        return isset($_SESSION['remember_me']) && $_SESSION['remember_me'] === true;
    }

    /**
     * 获取记住我过期时间
     */
    public static function getRememberExpires() {
        return $_SESSION['remember_expires'] ?? null;
    }

    /**
     * 获取当前用户信息
     */
    public static function getCurrentUser() {
        if (!self::isLoggedIn()) {
            return null;
        }

        return [
            'id' => $_SESSION[self::SESSION_USER_ID],
            'username' => $_SESSION[self::SESSION_USERNAME],
            'name' => $_SESSION['user_name'] ?? $_SESSION[self::SESSION_USERNAME],
            'email' => $_SESSION['user_email'] ?? '',
            'role' => $_SESSION[self::SESSION_USER_ROLE],
            'login_time' => $_SESSION[self::SESSION_LOGIN_TIME],
            'last_activity' => $_SESSION[self::SESSION_LAST_ACTIVITY]
        ];
    }

    /**
     * 检查用户权限
     */
    public static function hasRole($required_role) {
        if (!self::isLoggedIn()) {
            return false;
        }

        $user_role = $_SESSION[self::SESSION_USER_ROLE];

        // 管理员拥有所有权限
        if ($user_role === self::ROLE_ADMIN) {
            return true;
        }

        return $user_role === $required_role;
    }

    /**
     * 要求用户登录（中间件）
     */
    public static function requireLogin($redirect_url = '/login.php') {
        if (!self::isLoggedIn()) {
            // 如果是AJAX请求，返回JSON
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'message' => '请先登录',
                    'redirect' => $redirect_url
                ]);
                exit;
            }

            // 普通请求重定向到登录页面
            header('Location: ' . $redirect_url);
            exit;
        }
    }

    /**
     * 要求特定权限（中间件）
     */
    public static function requireRole($required_role, $redirect_url = '/login.php') {
        self::requireLogin($redirect_url);

        if (!self::hasRole($required_role)) {
            // 如果是AJAX请求，返回JSON
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                http_response_code(403);
                echo json_encode([
                    'success' => false,
                    'message' => '权限不足'
                ]);
                exit;
            }

            // 普通请求显示错误页面或重定向
            http_response_code(403);
            echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>权限不足</title>
</head>
<body>
    <h1>权限不足</h1>
    <p>您没有访问此页面的权限。</p>
    <a href="' . $redirect_url . '">返回登录</a>
</body>
</html>';
            exit;
        }
    }

    /**
     * 发送密码重置邮件
     */
    public static function sendPasswordResetEmail($email) {
        try {
            $db = self::getDB();

            // 检查用户是否存在
            $user = $db->fetch(
                "SELECT id, username FROM users WHERE email = ? AND status = 'active'",
                [$email]
            );

            if (!$user) {
                return [
                    'success' => false,
                    'message' => '该邮箱未注册'
                ];
            }

            // 生成重置令牌
            $token = bin2hex(random_bytes(32));
            $expires_at = date('Y-m-d H:i:s', time() + 3600); // 1小时后过期

            // 保存重置令牌
            $db->insert(
                "INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?)",
                [$email, $token, $expires_at]
            );

            // 记录操作日志
            self::logOperation('password_reset_request', '请求密码重置', [
                'email' => $email,
                'ip' => self::getClientIP()
            ]);

            // 发送重置邮件
            $email_sent = false;
            try {
                $email_sent = self::sendPasswordResetEmailAPI($email, $token);
            } catch (Exception $e) {
                error_log("发送密码重置邮件失败: " . $e->getMessage());
            }

            return [
                'success' => true,
                'message' => '密码重置邮件已发送，请查收',
                'token' => $token // 仅用于测试，生产环境不应返回
            ];

        } catch (Exception $e) {
            error_log("密码重置请求错误: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '系统错误，请稍后重试'
            ];
        }
    }

    /**
     * 密码重置令牌验证
     */
    public static function validateResetToken($token) {
        try {
            $db = self::getDB();

            $reset_data = $db->fetch(
                "SELECT email, expires_at, used FROM password_resets
                 WHERE token = ? AND used = 0 AND expires_at > NOW()",
                [$token]
            );

            if ($reset_data) {
                return [
                    'valid' => true,
                    'email' => $reset_data['email']
                ];
            }

            return [
                'valid' => false,
                'message' => '令牌无效或已过期'
            ];

        } catch (Exception $e) {
            error_log("令牌验证错误: " . $e->getMessage());
            return [
                'valid' => false,
                'message' => '系统错误'
            ];
        }
    }

    /**
     * 重置密码
     */
    public static function resetPassword($token, $new_password) {
        try {
            $db = self::getDB();

            $token_validation = self::validateResetToken($token);

            if (!$token_validation['valid']) {
                return [
                    'success' => false,
                    'message' => $token_validation['message']
                ];
            }

            $email = $token_validation['email'];

            // 开始事务
            $db->beginTransaction();

            // 更新用户密码
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $db->update(
                "UPDATE users SET password = ?, updated_at = NOW() WHERE email = ?",
                [$hashed_password, $email]
            );

            // 标记令牌为已使用
            $db->update(
                "UPDATE password_resets SET used = 1 WHERE token = ?",
                [$token]
            );

            // 提交事务
            $db->commit();

            // 记录操作日志
            self::logOperation('password_reset', '密码重置成功', [
                'email' => $email,
                'ip' => self::getClientIP()
            ]);

            return [
                'success' => true,
                'message' => '密码重置成功'
            ];

        } catch (Exception $e) {
            $db->rollback();
            error_log("密码重置错误: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '系统错误，请稍后重试'
            ];
        }
    }

    /**
     * 生成CSRF令牌
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * 验证CSRF令牌
     */
    public static function validateCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * 获取客户端IP地址
     */
    private static function getClientIP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * 解析用户代理字符串
     */
    private static function parseUserAgent($user_agent) {
        if (empty($user_agent)) {
            return 'Unknown Device';
        }

        // 简单的设备检测
        if (preg_match('/Mobile|Android|iPhone|iPad/', $user_agent)) {
            if (preg_match('/iPhone/', $user_agent)) {
                return 'iPhone';
            } elseif (preg_match('/iPad/', $user_agent)) {
                return 'iPad';
            } elseif (preg_match('/Android/', $user_agent)) {
                return 'Android Device';
            } else {
                return 'Mobile Device';
            }
        } elseif (preg_match('/Windows/', $user_agent)) {
            return 'Windows PC';
        } elseif (preg_match('/Macintosh/', $user_agent)) {
            return 'Mac';
        } elseif (preg_match('/Linux/', $user_agent)) {
            return 'Linux PC';
        }

        return 'Desktop';
    }

    /**
     * 根据IP获取地理位置（简化版）
     */
    private static function getLocationByIP($ip) {
        // 这里可以集成第三方IP地理位置服务
        // 目前返回简单的本地/外网判断
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            return '外网访问';
        } else {
            return '本地网络';
        }
    }

    /**
     * 检查网站是否开放访问
     */
    public static function isSiteAccessible() {
        require_once __DIR__ . '/SystemConfig.php';

        // 获取网站开关设置
        $siteEnabled = SystemConfig::get('security', 'enable_login', '1');

        // 如果网站关闭，检查当前用户是否为管理员
        if ($siteEnabled !== '1') {
            if (!self::isLoggedIn()) {
                return false;
            }

            $currentUser = self::getCurrentUser();
            return $currentUser && $currentUser['role'] === self::ROLE_ADMIN;
        }

        return true;
    }

    /**
     * 检查并强制网站访问权限
     */
    public static function requireSiteAccess() {
        if (!self::isSiteAccessible()) {
            // 如果是AJAX请求
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                http_response_code(503);
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => '网站暂时关闭，仅允许管理员访问'
                ]);
                exit;
            }

            // 显示维护页面
            self::showMaintenancePage();
            exit;
        }
    }

    /**
     * 检查注册功能是否启用
     */
    public static function isRegisterEnabled() {
        require_once __DIR__ . '/SystemConfig.php';
        return SystemConfig::get('security', 'enable_register', '1') === '1';
    }

    /**
     * 检查密码找回功能是否启用
     */
    public static function isPasswordResetEnabled() {
        require_once __DIR__ . '/SystemConfig.php';
        return SystemConfig::get('security', 'enable_password_reset', '1') === '1';
    }

    /**
     * 检查并强制注册功能权限
     */
    public static function requireRegisterAccess() {
        if (!self::isRegisterEnabled()) {
            // 如果是AJAX请求
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                http_response_code(403);
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => '注册功能已关闭'
                ]);
                exit;
            }

            // 重定向到登录页面
            header('Location: /login.php?error=register_disabled');
            exit;
        }
    }

    /**
     * 检查并强制密码找回功能权限
     */
    public static function requirePasswordResetAccess() {
        if (!self::isPasswordResetEnabled()) {
            // 如果是AJAX请求
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                http_response_code(403);
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => '密码找回功能已关闭'
                ]);
                exit;
            }

            // 重定向到登录页面
            header('Location: /login.php?error=reset_disabled');
            exit;
        }
    }

    /**
     * 显示维护页面
     */
    private static function showMaintenancePage() {
        http_response_code(503);
        ?>
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>网站维护中</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    margin: 0;
                    padding: 0;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .maintenance-container {
                    text-align: center;
                    background: white;
                    padding: 60px 40px;
                    border-radius: 20px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    max-width: 500px;
                    margin: 20px;
                }
                .maintenance-icon {
                    font-size: 80px;
                    margin-bottom: 30px;
                }
                .maintenance-title {
                    font-size: 32px;
                    font-weight: 700;
                    color: #2d3748;
                    margin-bottom: 20px;
                }
                .maintenance-message {
                    font-size: 18px;
                    color: #718096;
                    line-height: 1.6;
                    margin-bottom: 30px;
                }
                .admin-login {
                    margin-top: 30px;
                    padding-top: 30px;
                    border-top: 1px solid #e2e8f0;
                }
                .admin-login a {
                    color: #667eea;
                    text-decoration: none;
                    font-weight: 500;
                }
                .admin-login a:hover {
                    text-decoration: underline;
                }
            </style>
        </head>
        <body>
            <div class="maintenance-container">
                <div class="maintenance-icon">🔧</div>
                <h1 class="maintenance-title">网站维护中</h1>
                <p class="maintenance-message">
                    我们正在对网站进行维护升级，以提供更好的服务体验。<br>
                    预计很快就会恢复正常，感谢您的耐心等待。
                </p>
                <div class="admin-login">
                    <a href="/login.php">管理员登录</a>
                </div>
            </div>
        </body>
        </html>
        <?php
    }

    /**
     * 发送注册验证邮件
     */
    private static function sendRegistrationEmail($email, $verifyToken) {
        // 直接调用邮件发送逻辑，避免HTTP请求死锁
        return self::sendEmailDirect('registration', $email, $verifyToken);
    }

    /**
     * 直接发送邮件（避免HTTP请求死锁）
     */
    private static function sendEmailDirect($type, $email, $token) {
        // 引入EmailService类
        require_once __DIR__ . '/EmailService.php';

        try {
            switch ($type) {
                case 'registration':
                    EmailService::sendRegistrationEmail($email, $token);
                    break;
                case 'password_reset':
                    EmailService::sendPasswordResetEmail($email, $token);
                    break;
                default:
                    throw new Exception('未知的邮件类型');
            }
            return true;
        } catch (Exception $e) {
            throw new Exception('邮件发送失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送密码重置邮件
     */
    private static function sendPasswordResetEmailAPI($email, $resetToken) {
        // 直接调用邮件发送逻辑，避免HTTP请求死锁
        return self::sendEmailDirect('password_reset', $email, $resetToken);
    }

    /**
     * 发送验证码邮件
     */
    public static function sendVerifyCodeEmail($email, $code, $actionType = '验证', $expireMinutes = 5) {
        // 引入EmailService类
        require_once __DIR__ . '/EmailService.php';

        try {
            EmailService::sendVerifyCodeEmail($email, $code, $actionType, $expireMinutes);
            return true;
        } catch (Exception $e) {
            throw new Exception('邮件发送失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送密码重置验证码
     */
    public static function sendPasswordResetVerifyCode($email) {
        try {
            $db = self::getDB();

            // 检查用户是否存在
            $user = $db->fetch("SELECT id, email FROM users WHERE email = ?", [$email]);

            if (!$user) {
                return [
                    'success' => false,
                    'message' => '该邮箱地址未注册'
                ];
            }

            // 生成6位数验证码
            $code = sprintf('%06d', mt_rand(0, 999999));
            $expires_at = date('Y-m-d H:i:s', time() + 300); // 5分钟后过期

            error_log("调试 - 生成验证码: {$code}, 过期时间: {$expires_at}, 邮箱: {$email}");

            // 删除该邮箱的旧验证码
            $deleted = $db->delete("DELETE FROM password_reset_codes WHERE email = ?", [$email]);
            error_log("调试 - 删除旧验证码数量: {$deleted}");

            // 保存新验证码
            $inserted = $db->insert(
                "INSERT INTO password_reset_codes (email, code, expires_at, created_at) VALUES (?, ?, ?, NOW())",
                [$email, $code, $expires_at]
            );
            error_log("调试 - 插入新验证码结果: " . ($inserted ? '成功' : '失败'));

            // 发送验证码邮件
            $email_sent = false;
            try {
                self::sendVerifyCodeEmail($email, $code, '密码重置', 5);
                $email_sent = true;
            } catch (Exception $e) {
                error_log("发送密码重置验证码邮件失败: " . $e->getMessage());
            }

            // 记录操作日志
            self::logOperation('password_reset_code_request', '请求密码重置验证码', [
                'email' => $email,
                'ip' => self::getClientIP()
            ]);

            return [
                'success' => true,
                'message' => '验证码已发送到您的邮箱，请查收',
                'email_sent' => $email_sent
            ];

        } catch (Exception $e) {
            error_log("发送密码重置验证码错误: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '发送验证码失败，请稍后重试'
            ];
        }
    }

    /**
     * 验证密码重置验证码
     */
    public static function verifyPasswordResetCode($email, $code) {
        try {
            $db = self::getDB();

            // 调试：查看数据库中的所有验证码记录
            $allCodes = $db->fetchAll("SELECT email, code, expires_at, used, created_at FROM password_reset_codes WHERE email = ?", [$email]);
            error_log("调试 - 邮箱 {$email} 的所有验证码记录: " . json_encode($allCodes));
            error_log("调试 - 当前时间: " . date('Y-m-d H:i:s'));
            error_log("调试 - 要验证的验证码: {$code}");

            // 查找有效的验证码
            $codeData = $db->fetch(
                "SELECT email, code, expires_at FROM password_reset_codes
                 WHERE email = ? AND code = ? AND expires_at > NOW() AND used = 0",
                [$email, $code]
            );

            error_log("调试 - 查询结果: " . json_encode($codeData));

            if (!$codeData) {
                return [
                    'success' => false,
                    'message' => '验证码无效或已过期'
                ];
            }

            // 标记验证码为已使用
            $db->update(
                "UPDATE password_reset_codes SET used = 1 WHERE email = ? AND code = ?",
                [$email, $code]
            );

            // 生成密码重置令牌
            $token = bin2hex(random_bytes(32));
            $expires_at = date('Y-m-d H:i:s', time() + 3600); // 1小时后过期

            // 删除该邮箱的旧重置令牌
            $db->delete("DELETE FROM password_resets WHERE email = ?", [$email]);

            // 保存重置令牌
            $db->insert(
                "INSERT INTO password_resets (email, token, expires_at, created_at) VALUES (?, ?, ?, NOW())",
                [$email, $token, $expires_at]
            );

            // 发送重置邮件
            $email_sent = false;
            try {
                $email_sent = self::sendPasswordResetEmailAPI($email, $token);
            } catch (Exception $e) {
                error_log("发送密码重置邮件失败: " . $e->getMessage());
            }

            // 记录操作日志
            self::logOperation('password_reset_code_verified', '密码重置验证码验证成功', [
                'email' => $email,
                'ip' => self::getClientIP()
            ]);

            return [
                'success' => true,
                'message' => '验证成功！密码重置链接已发送到您的邮箱',
                'email_sent' => $email_sent
            ];

        } catch (Exception $e) {
            error_log("验证密码重置验证码错误: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '验证失败，请稍后重试'
            ];
        }
    }

    /**
     * 通过令牌验证邮箱
     */
    public static function verifyEmailByToken($token) {
        try {
            $db = self::getDB();

            // 首先检查是否存在这个令牌（不管验证状态）
            $tokenUser = $db->fetch("SELECT id, email, email_verified, created_at FROM users WHERE email_verify_token = ?", [$token]);

            if ($tokenUser) {
                if ($tokenUser['email_verified'] == 1) {
                    // 情况3：用户已经验证过了，但令牌还没清空
                    return [
                        'success' => true,
                        'message' => '您的邮箱已经验证过了！',
                        'status' => 'already_verified'
                    ];
                } else {
                    // 情况2：令牌有效，用户未验证，执行验证
                    $user = $tokenUser;
                }
            } else {
                // 情况1：令牌不存在，可能已过期或无效
                return [
                    'success' => false,
                    'message' => '验证令牌无效或已过期',
                    'status' => 'invalid_token'
                ];
            }

            // 更新用户邮箱验证状态（保留令牌以便后续识别已验证状态）
            $affected = $db->update("UPDATE users SET email_verified = 1, updated_at = NOW() WHERE id = ?", [$user['id']]);

            if ($affected > 0) {
                // 记录操作日志
                self::logOperation('email_verified', '邮箱验证成功', [
                    'user_id' => $user['id'],
                    'email' => $user['email'],
                    'ip' => self::getClientIP()
                ]);

                return [
                    'success' => true,
                    'message' => '邮箱验证成功！您现在可以正常登录了。',
                    'status' => 'just_verified'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '验证状态更新失败'
                ];
            }

        } catch (Exception $e) {
            error_log("邮箱验证错误: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '验证过程中发生错误，请稍后重试'
            ];
        }
    }

    /**
     * 重新发送验证邮件
     */
    public static function resendVerificationEmail($email) {
        try {
            $db = self::getDB();

            // 查找用户
            $user = $db->fetch("SELECT id, email, email_verified, email_verify_token FROM users WHERE email = ?", [$email]);

            if (!$user) {
                return [
                    'success' => false,
                    'message' => '用户不存在'
                ];
            }

            if ($user['email_verified']) {
                return [
                    'success' => false,
                    'message' => '邮箱已经验证过了'
                ];
            }

            // 生成新的验证令牌
            $newToken = bin2hex(random_bytes(32));

            // 更新验证令牌
            $affected = $db->update("UPDATE users SET email_verify_token = ?, updated_at = NOW() WHERE id = ?", [$newToken, $user['id']]);

            if ($affected > 0) {
                // 发送验证邮件
                try {
                    self::sendRegistrationEmail($email, $newToken);

                    return [
                        'success' => true,
                        'message' => '验证邮件已重新发送，请查收'
                    ];
                } catch (Exception $e) {
                    error_log("重发验证邮件失败: " . $e->getMessage());
                    return [
                        'success' => false,
                        'message' => '邮件发送失败，请稍后重试'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => '令牌更新失败'
                ];
            }

        } catch (Exception $e) {
            error_log("重发验证邮件错误: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '操作失败，请稍后重试'
            ];
        }
    }
}

// 自动加载函数，方便在其他文件中使用
function auth() {
    return new Auth();
}
?>
