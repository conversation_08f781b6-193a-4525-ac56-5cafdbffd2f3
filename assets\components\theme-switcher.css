/* ===== 主题切换组件样式 ===== */

/* 主题切换系统 */
.theme-switcher {
    position: fixed;
    z-index: 1000;
}

/* 确保在所有位置都可见 */
.theme-switcher-top-right {
    top: 1.5rem;
    right: 1.5rem;
}

.theme-switcher-top-left {
    top: 1.5rem;
    left: 1.5rem;
}

.theme-switcher-bottom-right {
    bottom: 5.5rem;  /* 设置面板按钮上方，留出间距 */
    right: 1.5rem;
}

.theme-switcher-bottom-left {
    bottom: 1.5rem;
    left: 1.5rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .theme-switcher-top-right {
        top: 5rem;
        right: 1rem;
    }

    .theme-switcher-top-left {
        top: 5rem;
        left: 1rem;
    }

    .theme-switcher-bottom-right {
        bottom: 5.5rem;
        right: 1rem;
    }

    .theme-switcher-bottom-left {
        bottom: 5.5rem;
        left: 1rem;
    }

    /* 在小屏幕上稍微缩小按钮 */
    .theme-toggle {
        width: 44px;
        height: 44px;
    }

    .theme-icon {
        width: 16px;
        height: 16px;
    }
}

/* 超小屏幕进一步调整 */
@media (max-width: 480px) {
    .theme-switcher-top-right {
        top: 6rem;
        right: 0.75rem;
    }

    .theme-switcher-top-left {
        top: 6rem;
        left: 0.75rem;
    }

    .theme-switcher-bottom-right {
        bottom: 5.5rem;
        right: 0.75rem;
    }

    .theme-switcher-bottom-left {
        bottom: 5.5rem;
        left: 0.75rem;
    }

    .theme-toggle {
        width: 40px;
        height: 40px;
    }

    .theme-icon {
        width: 14px;
        height: 14px;
    }
}

.theme-toggle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #e5e7eb);
    box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition, all 0.3s ease);
    backdrop-filter: blur(10px);
}

.theme-toggle:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
    border-color: var(--primary-color, #3b82f6);
}

.theme-toggle:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* 主题图标 */
.theme-icon {
    width: 18px;
    height: 18px;
    position: relative;
    transition: transform 0.3s ease;
}

.theme-icon svg {
    position: absolute;
    inset: 0;
    color: var(--text-secondary, #6b7280);
    transition: all 0.3s ease;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.theme-toggle:hover .theme-icon svg {
    color: var(--primary-color, #3b82f6);
}

/* 图标切换 */
.moon-icon {
    opacity: 1;
    transform: scale(1);
}

.sun-icon {
    opacity: 0;
    transform: scale(0.5);
}

[data-theme="dark"] .moon-icon {
    opacity: 0;
    transform: scale(0.5);
}

[data-theme="dark"] .sun-icon {
    opacity: 1;
    transform: scale(1);
}

/* ===== 主题下拉菜单 ===== */
.theme-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--spacing-2);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    box-shadow: var(--shadow-2xl);
    backdrop-filter: blur(20px);
    min-width: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    z-index: 1001;  /* 确保下拉菜单在最上层 */
}

/* 底部位置的下拉菜单向上展开 */
.theme-switcher-bottom-right .theme-dropdown,
.theme-switcher-bottom-left .theme-dropdown {
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: var(--spacing-2);
    transform: translateY(10px) scale(0.95);
}

.theme-switcher-bottom-right .theme-dropdown.show,
.theme-switcher-bottom-left .theme-dropdown.show {
    transform: translateY(0) scale(1);
}

/* 移动端下拉菜单适配 */
@media (max-width: 768px) {
    .theme-dropdown {
        min-width: 260px;
        right: -10px;
    }
}

@media (max-width: 480px) {
    .theme-dropdown {
        min-width: 240px;
        right: -20px;
        max-width: calc(100vw - 2rem);
    }

    .theme-dropdown::before {
        right: 30px;
    }

    /* 移动端底部位置的箭头 */
    .theme-switcher-bottom-right .theme-dropdown::before,
    .theme-switcher-bottom-left .theme-dropdown::before {
        right: 30px;
    }
}

.theme-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.theme-dropdown::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 16px;
    height: 16px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
    border-radius: 2px;
}

/* 底部位置的箭头指向下方 */
.theme-switcher-bottom-right .theme-dropdown::before,
.theme-switcher-bottom-left .theme-dropdown::before {
    top: auto;
    bottom: -8px;
    border-bottom: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
}

.theme-dropdown-header {
    padding: var(--spacing-4) var(--spacing-4) var(--spacing-2);
    border-bottom: 1px solid var(--border-light);
}

.theme-dropdown-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.theme-dropdown-body {
    padding: var(--spacing-2);
}

.theme-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-3);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.theme-option:hover {
    background: var(--bg-tertiary);
}

.theme-option.active {
    background: var(--primary-light);
    color: var(--primary-color);
}

/* 自动切换选项特殊样式 */
.theme-option[data-action="auto"] {
    cursor: default;
}

.theme-option[data-action="auto"]:hover {
    background: var(--bg-tertiary);
}

.theme-option-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.theme-option.active .theme-option-icon {
    color: var(--primary-color);
}

.theme-option-content {
    flex: 1;
}

.theme-option-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0 0 2px 0;
}

.theme-option-desc {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin: 0;
}

.theme-option-toggle {
    width: 40px;
    height: 20px;
    background: var(--border-color);
    border-radius: 10px;
    position: relative;
    transition: background-color 0.3s ease;
    cursor: pointer;
    border: none;
}

.theme-option-toggle.active {
    background: var(--primary-color);
}

.theme-option-toggle::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.theme-option-toggle.active::after {
    transform: translateX(20px);
}

/* ===== 时间设置弹窗 ===== */
.time-modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(15, 23, 42, 0.6);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.time-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.time-modal {
    background: var(--bg-primary);
    border-radius: 20px;
    box-shadow: var(--shadow-2xl);
    max-width: 400px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.time-modal-overlay.show .time-modal {
    transform: scale(1);
}

.time-modal-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.time-modal-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.time-modal-close {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--bg-tertiary);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-muted);
}

.time-modal-close:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.time-modal-body {
    padding: var(--spacing-6);
}

.time-setting-group {
    margin-bottom: var(--spacing-6);
}

.time-setting-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-2);
}

.time-input-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-2);
    transition: var(--transition);
}

.time-input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.time-input {
    background: none;
    border: none;
    outline: none;
    font-size: 1rem;
    color: var(--text-primary);
    text-align: center;
    width: 60px;
}

.time-separator {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-secondary);
}

.time-icon {
    width: 20px;
    height: 20px;
    color: var(--text-muted);
}

.time-modal-footer {
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--border-light);
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--border-color);
}
