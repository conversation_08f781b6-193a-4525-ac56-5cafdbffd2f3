<?php
/**
 * 🔐 现代化PHP管理系统 - 登录页面
 *
 * 功能：用户登录界面
 * 设计：采用现代化UI设计语言
 * 特色：响应式布局、主题切换、表单验证
 */

session_start();

// 引入系统配置
require_once 'auth/SystemConfig.php';
require_once 'auth/auth.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 检查用户是否已经登录，如果已登录则跳转到仪表盘
if (Auth::isLoggedIn()) {
    header('Location: users/index.php');
    exit;
}

// 处理登录逻辑
$error_message = '';
$success_message = '';

// 处理URL参数中的错误信息
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'register_disabled':
            $error_message = '注册功能已关闭';
            break;
        case 'reset_disabled':
            $error_message = '密码找回功能已关闭';
            break;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    
    // 基础验证
    if (empty($username)) {
        $error_message = '请输入用户名';
    } elseif (empty($password)) {
        $error_message = '请输入密码';
    } else {
        // 引入认证类
        require_once 'auth/auth.php';

        // 使用Auth类进行登录验证
        $login_result = Auth::login($username, $password);

        if ($login_result['success']) {
            $success_message = '登录成功！正在跳转...';
            // 跳转到仪表板
            header('Location: users/index.php');
            exit();
        } else {
            $error_message = $login_result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle('登录'); ?></title>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- 只需要引入一个JS文件，所有样式和组件都会自动加载 -->
    
    <!-- SEO和社交媒体标签 -->
    <meta name="description" content="<?php echo SystemConfig::getSiteDescription(); ?>登录页面，采用最新设计语言和用户体验">
    <meta name="keywords" content="PHP, 管理系统, 登录, 现代化设计">
    <meta name="author" content="<?php echo SystemConfig::getSiteName(); ?>">
    
    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔐</text></svg>">
</head>
<body>
    <!-- 登录容器 -->
    <div class="login-container">
        <!-- 登录卡片 -->
        <div class="login-card">
            <!-- 登录头部 -->
            <div class="login-header">
                <h1 class="login-title"><?php echo SystemConfig::getSiteName(); ?></h1>
                <p class="login-subtitle"><?php echo SystemConfig::getSiteDescription(); ?></p>
            </div>
            
            <!-- 登录表单 -->
            <div class="login-body">
                <form id="loginForm" method="POST" action="" data-validate>
                    <!-- 用户名输入框 -->
                    <div class="form-group">
                        <label for="username" class="form-label">用户名</label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            class="form-control" 
                            placeholder="请输入用户名"
                            value="<?php echo htmlspecialchars($username ?? ''); ?>"
                            autocomplete="username"
                            required
                        >
                    </div>
                    
                    <!-- 密码输入框 -->
                    <div class="form-group">
                        <label for="password" class="form-label">密码</label>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control" 
                            placeholder="请输入密码"
                            autocomplete="current-password"
                            required
                        >
                    </div>
                    
                    <!-- 记住我选项 -->
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="remember" class="checkbox-input">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-text">记住我</span>
                        </label>
                        <div class="text-xs text-muted mt-1 ml-6">
                            勾选后30天内无需重新登录
                        </div>
                    </div>
                    
                    <!-- 错误信息显示 -->
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-error mb-4">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="15" y1="9" x2="9" y2="15"></line>
                                <line x1="9" y1="9" x2="15" y2="15"></line>
                            </svg>
                            <span><?php echo htmlspecialchars($error_message); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <!-- 成功信息显示 -->
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success mb-4">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="20,6 9,17 4,12"></polyline>
                            </svg>
                            <span><?php echo htmlspecialchars($success_message); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <!-- 登录按钮 -->
                    <button type="submit" class="btn btn-primary w-full mb-4">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                            <polyline points="10,17 15,12 10,7"></polyline>
                            <line x1="15" y1="12" x2="3" y2="12"></line>
                        </svg>
                        登录系统
                    </button>
                    
                    <!-- 其他操作链接 -->
                    <div class="text-center">
                        <?php if (Auth::isPasswordResetEnabled()): ?>
                            <a href="forgot-password.php" class="text-sm text-muted hover:text-primary">忘记密码？</a>
                        <?php endif; ?>

                        <?php if (Auth::isPasswordResetEnabled() && Auth::isRegisterEnabled()): ?>
                            <span class="text-muted mx-2">|</span>
                        <?php endif; ?>

                        <?php if (Auth::isRegisterEnabled()): ?>
                            <a href="register.php" class="text-sm text-muted hover:text-primary">注册账户</a>
                        <?php endif; ?>

                        <?php if (!Auth::isPasswordResetEnabled() && !Auth::isRegisterEnabled()): ?>
                            <span class="text-sm text-muted">如需帮助，请联系管理员</span>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 页脚信息 -->
        <div class="page-footer">
            <p class="text-center text-sm text-muted">
                © <?php echo date('Y'); ?> <?php echo SystemConfig::getSiteName(); ?>. 保留所有权利.
            </p>
            <p class="text-center text-xs text-muted mt-2">
                演示账户：admin / 123456
            </p>
        </div>
    </div>

    <!-- 引入页脚 -->
    <?php include 'includes/footer.php'; ?>

    <!-- 页面特定脚本 -->
    <script>
        /**
         * 页面特定的初始化逻辑
         */
        function initializeLoginPage() {
            // 显示服务器端消息
            <?php if (!empty($error_message)): ?>
                showToast('<?php echo addslashes($error_message); ?>', 'error');
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                showToast('<?php echo addslashes($success_message); ?>', 'success');
                // 成功登录后延迟跳转
                setTimeout(() => {
                    window.location.href = 'users/index.php';
                }, 2000);
            <?php endif; ?>

            // 添加登录表单提交处理
            const loginForm = document.getElementById('loginForm');
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            let captchaSessionKey = null; // 存储验证码会话密钥

            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault(); // 阻止默认提交

                // 获取表单数据
                const formData = new FormData(loginForm);
                const username = formData.get('username');
                const password = formData.get('password');

                // 基础验证
                if (!username || !password) {
                    showToast('请填写用户名和密码', 'error');
                    return;
                }

                try {
                    // 检查是否需要验证码
                    const needsCaptcha = await checkCaptchaRequired();

                    if (needsCaptcha) {
                        // 显示验证码模态弹窗
                        const captchaResult = await showCaptchaModal();
                        if (!captchaResult.success) {
                            return; // 用户取消或验证失败
                        }
                        captchaSessionKey = captchaResult.sessionKey;
                    }

                    // 获取记住我选项
                    const rememberMe = document.querySelector('input[name="remember"]').checked;

                    // 执行登录
                    await performLogin(username, password, captchaSessionKey, rememberMe);

                } catch (error) {
                    console.error('登录过程出错:', error);
                    showToast(error.message || '登录失败，请重试', 'error');
                    resetSubmitButton();
                }
            });

            // 检查是否需要验证码
            async function checkCaptchaRequired() {
                try {
                    const response = await fetch('auth/SystemConfig.php?action=get_setting&key=security.enable_login_captcha');
                    const data = await response.json();
                    return data.success && data.value === '1';
                } catch (error) {
                    console.error('检查验证码设置失败:', error);
                    return false; // 默认不需要验证码
                }
            }

            // 显示验证码模态弹窗
            async function showCaptchaModal() {
                try {
                    // 获取验证码设置
                    const captchaType = await getCaptchaSetting('captcha_type', 'math');
                    const captchaDifficulty = await getCaptchaSetting('captcha_difficulty', 'medium');

                    // 生成验证码
                    const captchaResponse = await fetch('api/captcha.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'generate',
                            type: captchaType,
                            difficulty: captchaDifficulty
                        })
                    });

                    const captchaData = await captchaResponse.json();
                    if (!captchaData.success) {
                        throw new Error(captchaData.message || '生成验证码失败');
                    }

                    // 保存验证码数据到全局变量
                    window.currentCaptchaData = captchaData;

                    // 创建验证码内容
                    const captchaContent = createCaptchaContent(captchaData);

                    // 显示模态弹窗
                    return new Promise((resolve) => {
                        showModal.custom({
                            title: '🔐 安全验证',
                            content: captchaContent,
                            buttons: [
                                { text: '取消', type: 'secondary', value: 'cancel' },
                                { text: '验证', type: 'primary', value: 'verify' }
                            ],
                            closeOnBackdrop: false,
                            closeOnEscape: false,
                            onShow: function() {
                                // 如果是滑块验证码，初始化滑块功能
                                if (captchaData.type === 'slider') {
                                    initSliderCaptcha(captchaData.target_position || 75);
                                }

                                // 自动聚焦到输入框
                                const answerInput = document.querySelector('#captcha-answer');
                                if (answerInput && answerInput.type !== 'hidden') {
                                    setTimeout(() => answerInput.focus(), 100);
                                }

                                // 添加回车键监听
                                if (answerInput && answerInput.type !== 'hidden') {
                                    answerInput.addEventListener('keypress', function(e) {
                                        if (e.key === 'Enter') {
                                            e.preventDefault();
                                            document.querySelector('.modal-btn-primary').click();
                                        }
                                    });
                                }

                                // 绑定刷新验证码按钮事件
                                setTimeout(() => {
                                    const refreshBtn = document.querySelector('.refresh-captcha-btn');
                                    if (refreshBtn) {
                                        console.log('找到刷新按钮，绑定事件');
                                        refreshBtn.onclick = async function(e) {
                                            e.preventDefault();
                                            e.stopPropagation();

                                            console.log('刷新按钮被点击');

                                            if (this.disabled) {
                                                console.log('按钮已禁用，忽略点击');
                                                return;
                                            }

                                            this.disabled = true;
                                            const originalHTML = this.innerHTML;
                                            this.innerHTML = `
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 4px;">
                                                    <path d="M21 12a9 9 0 11-6.219-8.56"/>
                                                </svg>
                                                <span>刷新中...</span>
                                            `;

                                            try {
                                                console.log('开始刷新验证码');
                                                const container = document.querySelector('.captcha-container');
                                                if (container) {
                                                    await regenerateCaptchaContent(container);
                                                    console.log('验证码刷新完成');

                                                    // 重新聚焦输入框
                                                    setTimeout(() => {
                                                        const newAnswerInput = document.querySelector('#captcha-answer');
                                                        if (newAnswerInput && newAnswerInput.type !== 'hidden') {
                                                            newAnswerInput.focus();
                                                        }
                                                    }, 100);
                                                }
                                            } catch (error) {
                                                console.error('刷新验证码失败:', error);
                                                this.disabled = false;
                                                this.innerHTML = originalHTML;
                                            }
                                        };
                                    } else {
                                        console.log('未找到刷新按钮');
                                    }
                                }, 200);
                            }
                        }).then(async (result) => {
                            if (result === 'cancel') {
                                resolve({ success: false });
                                return;
                            }

                            if (result === 'verify') {
                                // 获取用户输入的答案
                                const answerInput = document.querySelector('#captcha-answer');
                                const answer = answerInput ? answerInput.value.trim() : '';

                                if (!answer) {
                                    showToast('请输入验证码', 'warning');
                                    // 重新显示验证码弹窗
                                    const retryResult = await showCaptchaModal();
                                    resolve(retryResult);
                                    return;
                                }

                                try {
                                    // 验证答案
                                    const verifyResponse = await fetch('api/captcha.api.php', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                        },
                                        body: JSON.stringify({
                                            action: 'verify',
                                            answer: answer,
                                            session_key: (window.currentCaptchaData || captchaData).session_key
                                        })
                                    });

                                    const verifyData = await verifyResponse.json();
                                    if (!verifyData.success) {
                                        showToast(verifyData.message || '验证码错误', 'error');
                                        // 重新显示验证码弹窗
                                        const retryResult = await showCaptchaModal();
                                        resolve(retryResult);
                                        return;
                                    }

                                    // 验证成功，直接继续登录流程，不显示成功提示
                                    resolve({
                                        success: true,
                                        sessionKey: (window.currentCaptchaData || captchaData).session_key
                                    });

                                } catch (error) {
                                    console.error('验证码验证失败:', error);
                                    showToast('验证失败，请重试', 'error');
                                    // 重新显示验证码弹窗
                                    const retryResult = await showCaptchaModal();
                                    resolve(retryResult);
                                }
                            }
                        });
                    });

                } catch (error) {
                    console.error('验证码模态弹窗错误:', error);
                    showToast(error.message || '验证码验证失败', 'error');
                    return { success: false };
                }
            }

            // 刷新当前验证码
            async function refreshCurrentCaptcha() {
                // 查找当前显示的验证码元素
                const captchaContainer = document.querySelector('.captcha-container');
                if (captchaContainer) {
                    // 重新生成验证码内容
                    await regenerateCaptchaContent(captchaContainer);
                }
            }

            // 全局刷新验证码函数
            window.refreshCaptchaFunction = async function(btn) {
                if (btn.disabled) return;

                btn.disabled = true;
                const originalHTML = btn.innerHTML;
                btn.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 4px;">
                        <path d="M21 12a9 9 0 11-6.219-8.56"/>
                    </svg>
                    <span>刷新中...</span>
                `;

                try {
                    const container = document.querySelector('.captcha-container');
                    if (container) {
                        await regenerateCaptchaContent(container);

                        // 重新聚焦输入框
                        setTimeout(() => {
                            const newAnswerInput = document.querySelector('#captcha-answer');
                            if (newAnswerInput && newAnswerInput.type !== 'hidden') {
                                newAnswerInput.focus();
                            }
                        }, 100);
                    }
                } catch (error) {
                    console.error('刷新验证码失败:', error);
                    btn.disabled = false;
                    btn.innerHTML = originalHTML;
                }
            };

            // 重新生成验证码内容
            async function regenerateCaptchaContent(container) {
                try {
                    // 获取验证码设置
                    const captchaType = await getCaptchaSetting('captcha_type', 'math');
                    const captchaDifficulty = await getCaptchaSetting('captcha_difficulty', 'medium');

                    // 生成新的验证码
                    const captchaResponse = await fetch('api/captcha.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'generate',
                            type: captchaType,
                            difficulty: captchaDifficulty
                        })
                    });

                    const captchaData = await captchaResponse.json();
                    if (captchaData.success) {
                        // 更新验证码内容
                        const newContent = createCaptchaContent(captchaData);
                        container.innerHTML = newContent;

                        // 更新全局的验证码数据
                        window.currentCaptchaData = captchaData;

                        // 重新启用刷新按钮
                        setTimeout(() => {
                            const refreshBtn = container.querySelector('.refresh-captcha-btn');
                            if (refreshBtn) {
                                refreshBtn.disabled = false;
                            }
                        }, 100);
                    }
                } catch (error) {
                    console.error('刷新验证码失败:', error);
                }
            }

            // 获取验证码设置
            async function getCaptchaSetting(key, defaultValue) {
                try {
                    const response = await fetch(`auth/SystemConfig.php?action=get_setting&key=security.${key}`);
                    const data = await response.json();
                    return data.success ? data.value : defaultValue;
                } catch (error) {
                    return defaultValue;
                }
            }

            // 创建验证码内容
            function createCaptchaContent(captchaData) {
                let content = `
                    <div class="captcha-container" style="text-align: center; padding: 1.5rem;">
                        <p style="margin-bottom: 1.5rem; color: var(--text-secondary); font-size: 14px;">为了您的账户安全，请完成以下验证：</p>
                `;

                if (captchaData.type === 'math') {
                    content += `
                        <div class="math-captcha" style="margin-bottom: 1.5rem;">
                            <div style="font-size: 28px; font-weight: 600; color: var(--text-primary); margin-bottom: 1rem; font-family: 'Courier New', monospace;">
                                ${captchaData.question}
                            </div>
                            <input type="text" id="captcha-answer" placeholder="请输入答案"
                                   style="width: 200px; padding: 12px 16px; border: 2px solid var(--border-color); border-radius: 8px; text-align: center; font-size: 16px; background: var(--bg-secondary); color: var(--text-primary); transition: all 0.2s ease;"
                                   autocomplete="off">
                        </div>
                    `;
                } else if (captchaData.type === 'alphanumeric') {
                    content += `
                        <div class="alphanumeric-captcha" style="margin-bottom: 1.5rem;">
                            <div style="font-size: 24px; font-weight: 600; color: var(--text-primary); margin-bottom: 1rem; letter-spacing: 4px; font-family: 'Courier New', monospace; background: var(--bg-tertiary); padding: 12px 20px; border-radius: 8px; border: 2px solid var(--border-color);">
                                ${captchaData.question}
                            </div>
                            <input type="text" id="captcha-answer" placeholder="请输入验证码"
                                   style="width: 200px; padding: 12px 16px; border: 2px solid var(--border-color); border-radius: 8px; text-align: center; font-size: 16px; letter-spacing: 2px; background: var(--bg-secondary); color: var(--text-primary); transition: all 0.2s ease;"
                                   autocomplete="off">
                        </div>
                    `;
                } else if (captchaData.type === 'slider') {
                    content += `
                        <div class="slider-captcha" style="margin-bottom: 1.5rem;">
                            <p style="margin-bottom: 1rem; color: var(--text-secondary);">${captchaData.question}</p>
                            <div style="width: 300px; height: 44px; background: var(--bg-tertiary); border: 2px solid var(--border-color); border-radius: 22px; position: relative; margin: 0 auto; overflow: hidden;">
                                <div id="slider-track" style="width: 100%; height: 100%; border-radius: 20px; position: relative; cursor: pointer;">
                                    <div id="slider-thumb" style="width: 40px; height: 40px; background: var(--primary-color); border-radius: 50%; position: absolute; top: 2px; left: 2px; cursor: grab; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; box-shadow: 0 2px 8px rgba(0,0,0,0.2); transition: all 0.2s ease;">
                                        →
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="captcha-answer" value="">
                        </div>
                    `;
                }

                content += `
                        <div style="margin-top: 1rem; text-align: center;">
                            <button type="button" class="refresh-captcha-btn" id="refresh-captcha-btn"
                                    style="background: #f8f9fa; border: 1px solid #dee2e6; color: #6c757d; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px; transition: all 0.2s ease; margin-bottom: 1rem; user-select: none;"
                                    onmouseover="this.style.background='#e9ecef'"
                                    onmouseout="this.style.background='#f8f9fa'"
                                    onclick="window.refreshCaptchaFunction(this)"
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 4px; pointer-events: none;">
                                    <polyline points="1 4 1 10 7 10"/>
                                    <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                                </svg>
                                <span style="pointer-events: none;">刷新验证码</span>
                            </button>
                        </div>
                        <p style="font-size: 12px; color: var(--text-tertiary); opacity: 0.8;">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 4px;">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                            验证码有效期5分钟，请及时完成验证
                        </p>
                    </div>
                    <style>
                        #captcha-answer:focus {
                            border-color: var(--primary-color) !important;
                            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1) !important;
                            outline: none !important;
                        }
                        #captcha-answer:hover {
                            border-color: var(--primary-color) !important;
                        }
                        #slider-thumb:hover {
                            transform: scale(1.05);
                            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                        }
                        #slider-thumb:active {
                            transform: scale(0.95);
                        }
                    </style>
                `;

                return content;
            }

            // 执行登录
            async function performLogin(username, password, captchaSessionKey, rememberMe = false) {
                setSubmitButtonLoading(true);

                try {
                    const loginData = {
                        username: username,
                        password: password,
                        remember: rememberMe
                    };

                    if (captchaSessionKey) {
                        loginData.captcha_session_key = captchaSessionKey;
                    }

                    const response = await fetch('api/login.api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(loginData)
                    });

                    // 检查响应状态
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const responseText = await response.text();
                    let data;

                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        console.error('JSON解析失败:', parseError);
                        console.error('响应内容:', responseText);
                        throw new Error('服务器响应格式错误');
                    }

                    if (data.success) {
                        // 显示登录成功提示，包含记住我状态
                        const rememberText = rememberMe ? '（已启用记住我，30天内免登录）' : '';
                        showToast(`登录成功，正在跳转...${rememberText}`, 'success');
                        setTimeout(() => {
                            window.location.href = 'users/index.php';
                        }, 1500);
                    } else {
                        // 根据错误类型进行不同处理
                        const errorType = data.error_type || 'unknown_error';

                        if (data.error_code === 'EMAIL_NOT_VERIFIED') {
                            showEmailVerificationModal(data.email);
                        } else if (errorType === 'password_error') {
                            // 密码错误：显示错误信息，关闭弹窗，刷新页面
                            showToast(data.message || '密码错误', 'error');
                            setTimeout(() => {
                                // 关闭可能存在的验证码弹窗
                                const captchaModal = document.querySelector('.captcha-modal');
                                if (captchaModal) {
                                    captchaModal.remove();
                                }
                                // 刷新页面
                                window.location.reload();
                            }, 2000);
                        } else if (errorType === 'user_not_found' || errorType === 'credentials_error') {
                            // 用户不存在：显示错误信息，关闭弹窗，刷新页面
                            showToast(data.message || '用户不存在或已被禁用', 'error');
                            setTimeout(() => {
                                // 关闭可能存在的验证码弹窗
                                const captchaModal = document.querySelector('.captcha-modal');
                                if (captchaModal) {
                                    captchaModal.remove();
                                }
                                // 刷新页面
                                window.location.reload();
                            }, 2000);
                        } else if (errorType === 'captcha_error' || errorType === 'captcha_required') {
                            // 验证码错误：显示错误信息，刷新验证码
                            showToast(data.message || '验证码错误', 'error');
                            // 刷新验证码
                            refreshCurrentCaptcha();
                            resetSubmitButton();
                        } else {
                            // 其他错误：显示错误信息
                            showToast(data.message || '登录失败', 'error');
                            resetSubmitButton();
                        }
                    }

                } catch (error) {
                    console.error('登录请求失败:', error);
                    showToast('网络错误，请重试', 'error');
                    resetSubmitButton();
                }
            }

            // 设置提交按钮加载状态
            function setSubmitButtonLoading(loading) {
                if (loading) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = `
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="loading-spinner">
                            <path d="M21 12a9 9 0 11-6.219-8.56"/>
                        </svg>
                        登录中...
                    `;
                } else {
                    resetSubmitButton();
                }
            }

            // 重置提交按钮
            function resetSubmitButton() {
                submitBtn.disabled = false;
                submitBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                        <polyline points="10,17 15,12 10,7"></polyline>
                        <line x1="15" y1="12" x2="3" y2="12"></line>
                    </svg>
                    登录系统
                `;
            }

            // 初始化滑块验证码
            function initSliderCaptcha(targetPosition) {
                const slider = document.getElementById('slider-thumb');
                const track = document.getElementById('slider-track');
                const answerInput = document.getElementById('captcha-answer');

                if (!slider || !track || !answerInput) return;

                let isDragging = false;
                let startX = 0;
                let currentX = 0;

                // 鼠标事件
                slider.addEventListener('mousedown', startDrag);
                document.addEventListener('mousemove', drag);
                document.addEventListener('mouseup', endDrag);

                // 触摸事件（移动端支持）
                slider.addEventListener('touchstart', startDrag);
                document.addEventListener('touchmove', drag);
                document.addEventListener('touchend', endDrag);

                function startDrag(e) {
                    isDragging = true;
                    startX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
                    slider.style.cursor = 'grabbing';
                    e.preventDefault();
                }

                function drag(e) {
                    if (!isDragging) return;

                    currentX = (e.type === 'mousemove' ? e.clientX : e.touches[0].clientX) - startX;
                    const trackWidth = track.offsetWidth - slider.offsetWidth;
                    const newPosition = Math.max(0, Math.min(currentX, trackWidth));

                    slider.style.left = newPosition + 'px';

                    // 计算完成百分比
                    const percentage = (newPosition / trackWidth) * 100;

                    // 检查是否接近目标位置（允许5%的误差）
                    if (Math.abs(percentage - targetPosition) <= 5) {
                        slider.style.background = 'var(--success-color)';
                        slider.innerHTML = '✓';
                        slider.style.boxShadow = '0 4px 12px rgba(var(--success-rgb), 0.4)';
                        track.style.background = 'linear-gradient(90deg, var(--success-color) 0%, var(--bg-tertiary) 100%)';
                        answerInput.value = 'success';
                    } else {
                        slider.style.background = 'var(--primary-color)';
                        slider.innerHTML = '→';
                        slider.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                        track.style.background = 'var(--bg-tertiary)';
                        answerInput.value = '';
                    }

                    e.preventDefault();
                }

                function endDrag() {
                    if (!isDragging) return;
                    isDragging = false;
                    slider.style.cursor = 'grab';
                }
            }

            // 键盘快捷键：Enter键快速登录
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && (e.target.tagName === 'INPUT')) {
                    e.preventDefault();
                    loginForm.requestSubmit();
                }
            });

            console.log('🔐 登录页面特定功能已初始化完成');
        }

        // 显示邮箱验证模态弹窗
        function showEmailVerificationModal(email) {
            const content = `
                <div style="text-align: center; padding: 1.5rem;">
                    <div style="font-size: 3rem; margin-bottom: 1rem;">📧</div>
                    <h3 style="color: var(--text-primary); margin-bottom: 1rem;">邮箱验证</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 1.5rem; line-height: 1.6;">
                        您的邮箱 <strong>${email}</strong> 尚未验证。<br>
                        请先验证邮箱后再登录。
                    </p>
                    <div style="margin-bottom: 1.5rem;">
                        <button type="button" class="btn btn-primary" onclick="resendVerificationEmail('${email}')">
                            重新发送验证邮件
                        </button>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <button type="button" class="btn btn-secondary" onclick="goToVerifyPage()">
                            前往验证页面
                        </button>
                    </div>
                    <p style="font-size: 12px; color: var(--text-tertiary); opacity: 0.8;">
                        如果您已经验证了邮箱，请稍等几分钟后重试登录
                    </p>
                </div>
            `;

            showModal.custom({
                title: '⚠️ 邮箱未验证',
                content: content,
                buttons: [
                    { text: '关闭', type: 'secondary', value: 'close' }
                ],
                closeOnBackdrop: true,
                closeOnEscape: true
            });
        }

        // 重新发送验证邮件
        async function resendVerificationEmail(email) {
            try {
                // 设置session中的待验证邮箱
                const setEmailResponse = await fetch('api/verify-email.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'set_pending_email',
                        email: email
                    })
                });

                // 重新发送邮件
                const response = await fetch('api/verify-email.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'resend_email'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showToast('验证邮件已重新发送，请查收', 'success');
                } else {
                    showToast(data.message || '发送失败，请稍后重试', 'error');
                }

            } catch (error) {
                console.error('重发邮件错误:', error);
                showToast('网络错误，请稍后重试', 'error');
            }
        }

        // 前往验证页面
        function goToVerifyPage() {
            window.location.href = 'verify-email.php';
        }

        // 等待统一组件系统初始化完成后执行页面特定逻辑
        document.addEventListener('DOMContentLoaded', function() {
            // 检查系统是否已初始化
            const checkSystemReady = () => {
                if (window.appManager && window.appManager.initialized) {
                    // 系统已就绪，执行页面特定初始化
                    initializeLoginPage();
                    console.log('🔐 登录页面已完全初始化');
                } else {
                    // 系统尚未就绪，继续等待
                    setTimeout(checkSystemReady, 100);
                }
            };

            checkSystemReady();
        });
    </script>
</body>
</html>
