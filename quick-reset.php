<?php
/**
 * 超级简单的密码重置工具
 * 无任何环境限制
 */

$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        require_once 'config/database.php';
        
        $username = $_POST['username'] ?? 'admin';
        $password = $_POST['password'] ?? '';
        
        if (empty($password)) {
            throw new Exception('请输入新密码');
        }
        
        $db = Database::getInstance();
        $hash = password_hash($password, PASSWORD_DEFAULT);
        
        $result = $db->update(
            "UPDATE users SET password = ? WHERE username = ?",
            [$hash, $username]
        );
        
        if ($result > 0) {
            $message = "✅ 成功！用户 '$username' 的密码已重置为 '$password'";
        } else {
            $message = "❌ 失败：用户不存在或更新失败";
        }
        
    } catch (Exception $e) {
        $message = "❌ 错误: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>快速密码重置</title>
    <style>
        body { font-family: Arial; max-width: 400px; margin: 50px auto; padding: 20px; }
        .box { background: #f9f9f9; padding: 20px; border-radius: 8px; }
        input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .message { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="box">
        <h2>🔑 快速密码重置</h2>
        
        <?php if ($message): ?>
        <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>
        
        <form method="post">
            <label>用户名:</label>
            <input type="text" name="username" value="admin" required>
            
            <label>新密码:</label>
            <input type="text" name="password" placeholder="输入新密码" required>
            
            <button type="submit">重置密码</button>
        </form>
        
        <p style="font-size: 12px; color: #666; text-align: center; margin-top: 20px;">
            默认用户名: admin | 使用完毕请删除此文件
        </p>
        
        <?php if (strpos($message, '✅') !== false): ?>
        <p style="text-align: center;">
            <a href="login.php" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">前往登录</a>
        </p>
        <?php endif; ?>
    </div>
</body>
</html>
