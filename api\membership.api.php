<?php
/**
 * 🎯 会员管理API接口
 * 
 * 功能：处理会员升级、续费、历史查询等操作
 * 权限：仅管理员可访问
 */

// 开启输出缓冲，防止意外输出污染JSON响应
ob_start();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

session_start();

// 引入必要的文件
require_once '../auth/auth.php';
require_once '../users/includes/MembershipManager.php';

try {
    // 检查用户是否已登录且为管理员
    if (!Auth::isLoggedIn()) {
        throw new Exception('请先登录');
    }
    
    if (!Auth::hasRole(Auth::ROLE_ADMIN)) {
        throw new Exception('权限不足');
    }
    
    $current_user = Auth::getCurrentUser();
    
    // 获取请求方法和数据
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'POST') {
        // POST请求 - 处理操作
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'upgrade':
                $result = handleUpgrade($input, $current_user['id']);
                break;
                
            case 'renew':
                $result = handleRenewal($input, $current_user['id']);
                break;
                
            default:
                throw new Exception('无效的操作');
        }
        
    } elseif ($method === 'GET') {
        // GET请求 - 查询数据
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'history':
                $result = getUserHistory($_GET['user_id'] ?? 0);
                break;
                
            case 'stats':
                $result = getMembershipStats();
                break;
                
            default:
                throw new Exception('无效的查询');
        }
        
    } else {
        throw new Exception('不支持的请求方法');
    }
    
    // 清理输出缓冲区，防止警告或其他输出污染JSON
    ob_clean();
    echo json_encode($result);
    
} catch (Exception $e) {
    http_response_code(400);
    // 清理输出缓冲区，防止警告或其他输出污染JSON
    ob_clean();
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => time()
    ]);
}

/**
 * 处理用户升级
 */
function handleUpgrade($input, $operatorId) {
    $userId = intval($input['user_id'] ?? 0);
    $targetGroup = trim($input['target_group'] ?? '');
    $duration = $input['duration'] ? intval($input['duration']) : null;
    $reason = trim($input['reason'] ?? '');
    
    if (!$userId) {
        throw new Exception('用户ID不能为空');
    }
    
    if (!$targetGroup) {
        throw new Exception('目标分组不能为空');
    }
    
    // 验证目标分组是否存在
    $group = MembershipManager::getGroupByKey($targetGroup);
    if (!$group) {
        throw new Exception('目标分组不存在');
    }
    
    // 执行升级
    $membershipId = MembershipManager::upgradeUser(
        $userId, 
        $targetGroup, 
        $duration, 
        $operatorId, 
        $reason ?: '管理员手动升级'
    );
    
    // 记录操作日志
    Auth::logOperation('membership_upgrade', '升级用户会员', [
        'user_id' => $userId,
        'target_group' => $targetGroup,
        'duration' => $duration,
        'reason' => $reason,
        'membership_id' => $membershipId
    ]);
    
    return [
        'success' => true,
        'message' => '用户升级成功',
        'membership_id' => $membershipId,
        'timestamp' => time()
    ];
}

/**
 * 处理会员续费
 */
function handleRenewal($input, $operatorId) {
    $userId = intval($input['user_id'] ?? 0);
    $duration = intval($input['duration'] ?? 0);
    $reason = trim($input['reason'] ?? '');
    
    if (!$userId) {
        throw new Exception('用户ID不能为空');
    }
    
    if (!$duration || $duration <= 0) {
        throw new Exception('续费时长必须大于0');
    }
    
    // 执行续费
    $result = MembershipManager::renewMembership(
        $userId, 
        $duration, 
        $operatorId, 
        $reason ?: '管理员手动续费'
    );
    
    // 记录操作日志
    Auth::logOperation('membership_renew', '续费用户会员', [
        'user_id' => $userId,
        'duration' => $duration,
        'reason' => $reason
    ]);
    
    return [
        'success' => true,
        'message' => '会员续费成功',
        'timestamp' => time()
    ];
}

/**
 * 获取用户历史记录
 */
function getUserHistory($userId) {
    $userId = intval($userId);
    
    if (!$userId) {
        throw new Exception('用户ID不能为空');
    }
    
    $history = MembershipManager::getUserMembershipHistory($userId, 20);
    
    return [
        'success' => true,
        'history' => $history,
        'timestamp' => time()
    ];
}

/**
 * 获取会员统计信息
 */
function getMembershipStats() {
    $stats = MembershipManager::getMembershipStats();
    
    return [
        'success' => true,
        'stats' => $stats,
        'timestamp' => time()
    ];
}
?>
