<?php
/**
 * 🔧 系统设置 API 接口
 * 
 * 功能：处理系统设置的保存和获取
 */

header('Content-Type: application/json; charset=utf-8');

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../auth/auth.php';

// 检查管理员权限
if (!Auth::isLoggedIn() || !Auth::hasRole(Auth::ROLE_ADMIN)) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => '权限不足'
    ]);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

try {
    // 记录请求日志
    error_log("Settings API 请求 - Action: {$action}, Input: " . json_encode($input));
    
    switch ($action) {
        case 'save':
            $result = saveSettings($input['settings'] ?? []);
            break;
            
        case 'get':
            $result = getSettings();
            break;
            
        default:
            throw new Exception('无效的操作');
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    error_log("Settings API 错误: " . $e->getMessage() . " - 堆栈: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 保存系统设置
 */
function saveSettings($settings) {
    global $pdo;
    
    if (empty($settings)) {
        throw new Exception('设置数据不能为空');
    }
    
    $pdo->beginTransaction();
    
    try {
        foreach ($settings as $category => $categorySettings) {
            foreach ($categorySettings as $key => $value) {
                // 检查设置是否已存在
                $stmt = $pdo->prepare("SELECT id FROM settings WHERE category = ? AND setting_key = ?");
                $stmt->execute([$category, $key]);
                
                if ($stmt->fetch()) {
                    // 更新现有设置
                    $stmt = $pdo->prepare("UPDATE settings SET setting_value = ?, updated_at = NOW() WHERE category = ? AND setting_key = ?");
                    $stmt->execute([$value, $category, $key]);
                } else {
                    // 插入新设置
                    $stmt = $pdo->prepare("INSERT INTO settings (category, setting_key, setting_value, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
                    $stmt->execute([$category, $key, $value]);
                }
            }
        }
        
        $pdo->commit();
        
        return [
            'success' => true,
            'message' => '设置保存成功'
        ];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw new Exception('保存设置失败：' . $e->getMessage());
    }
}

/**
 * 获取系统设置
 */
function getSettings() {
    global $pdo;
    
    $stmt = $pdo->query("SELECT * FROM settings ORDER BY category, setting_key");
    $all_settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 按分类组织设置
    $settings = [];
    foreach ($all_settings as $setting) {
        $settings[$setting['category']][$setting['setting_key']] = $setting['setting_value'];
    }
    
    return [
        'success' => true,
        'settings' => $settings
    ];
}
?>
