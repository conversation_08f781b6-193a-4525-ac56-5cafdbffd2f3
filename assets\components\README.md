# 主题切换组件 (ThemeSwitcher)

一个功能丰富的主题切换组件，支持手动切换、自动切换和时间设置。

## 🚀 特性

- ✅ **手动主题切换** - 一键切换亮色/暗色主题
- ✅ **自动时间切换** - 根据设定时间自动切换主题
- ✅ **自定义时间设置** - 可自定义亮色和暗色主题的切换时间
- ✅ **气泡式下拉菜单** - 现代化的交互设计
- ✅ **数据持久化** - 设置自动保存到localStorage
- ✅ **事件系统** - 支持监听主题切换事件
- ✅ **响应式设计** - 完美适配各种屏幕尺寸
- ✅ **键盘快捷键** - 支持Ctrl+Shift+T快速切换

## 📦 安装使用

### 1. 引入文件

```html
<!-- CSS样式 -->
<link rel="stylesheet" href="assets/components/theme-switcher.css">

<!-- JavaScript -->
<script src="assets/components/theme-switcher.js"></script>
```

### 2. 初始化组件

```javascript
// 基础初始化
const themeSwitcher = new ThemeSwitcher();

// 自定义配置
const themeSwitcher = new ThemeSwitcher({
    position: 'top-right',  // 位置：top-right, top-left, bottom-right, bottom-left
    showToast: true,        // 是否显示Toast提示
    autoInit: true          // 是否自动初始化
});
```

## 🎯 API 文档

### 构造函数选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `position` | string | 'top-right' | 组件位置 |
| `showToast` | boolean | true | 是否显示Toast提示 |
| `autoInit` | boolean | true | 是否自动初始化 |

### 方法

#### `init()`
初始化组件

#### `toggleTheme()`
手动切换主题

#### `setTheme(theme)`
设置指定主题
- `theme`: 'light' | 'dark'

#### `setAutoMode(enabled, lightTime?, darkTime?)`
设置自动模式
- `enabled`: boolean - 是否启用自动模式
- `lightTime`: string - 亮色主题开始时间 (如: '06:00')
- `darkTime`: string - 暗色主题开始时间 (如: '18:00')

#### `getState()`
获取当前状态
```javascript
const state = themeSwitcher.getState();
// 返回: { theme, autoMode, lightTime, darkTime, isAutoRunning }
```

#### `destroy()`
销毁组件

### 事件

组件会触发以下自定义事件：

#### `themeSwitcher:themeChanged`
主题切换时触发
```javascript
document.addEventListener('themeSwitcher:themeChanged', function(e) {
    console.log('新主题:', e.detail.theme);
    console.log('时间戳:', e.detail.timestamp);
});
```

#### `themeSwitcher:autoModeChanged`
自动模式状态改变时触发
```javascript
document.addEventListener('themeSwitcher:autoModeChanged', function(e) {
    console.log('自动模式:', e.detail.autoMode);
    console.log('亮色时间:', e.detail.lightTime);
    console.log('暗色时间:', e.detail.darkTime);
});
```

#### `themeSwitcher:timeSettingsChanged`
时间设置改变时触发
```javascript
document.addEventListener('themeSwitcher:timeSettingsChanged', function(e) {
    console.log('新的时间设置:', e.detail);
});
```

## 💡 使用示例

### 基础使用
```javascript
// 初始化组件
const themeSwitcher = new ThemeSwitcher();

// 监听主题切换
document.addEventListener('themeSwitcher:themeChanged', function(e) {
    console.log('主题已切换到:', e.detail.theme);
});
```

### 编程控制
```javascript
// 设置为暗色主题
themeSwitcher.setTheme('dark');

// 启用自动模式，早上7点切换到亮色，晚上8点切换到暗色
themeSwitcher.setAutoMode(true, '07:00', '20:00');

// 获取当前状态
const state = themeSwitcher.getState();
console.log('当前主题:', state.theme);
```

### 与Toast系统集成
```javascript
// 如果页面有Toast管理器，组件会自动使用
window.toastManager = new ToastManager();

// 组件会自动显示切换提示
const themeSwitcher = new ThemeSwitcher({
    showToast: true
});
```

## 🎨 样式自定义

组件使用CSS变量，可以轻松自定义样式：

```css
:root {
    --primary-color: #3b82f6;
    --bg-primary: #ffffff;
    --text-primary: #1f2937;
    /* 更多变量... */
}
```

## 📱 响应式支持

组件自动适配不同屏幕尺寸：
- 桌面端：完整功能和动画
- 移动端：优化的触摸体验
- 平板端：自适应布局

## 🔧 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**现代化PHP管理系统** - 主题切换组件
