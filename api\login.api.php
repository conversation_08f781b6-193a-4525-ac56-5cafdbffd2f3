<?php
/**
 * 🔐 登录API接口
 *
 * 功能：处理用户登录请求
 * 方法：POST
 * 参数：username, password
 */

// 开启输出缓冲，防止意外输出污染JSON响应
ob_start();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入认证类
require_once '../auth/Auth.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求'
    ]);
    exit;
}

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 如果不是JSON请求，尝试从POST获取
    if (!$input) {
        $input = $_POST;
    }
    
    $username = trim($input['username'] ?? '');
    $password = trim($input['password'] ?? '');
    $remember = isset($input['remember']) ? (bool)$input['remember'] : false;
    $captchaSessionKey = trim($input['captcha_session_key'] ?? '');

    // 验证输入
    if (empty($username)) {
        throw new Exception('请输入用户名');
    }

    if (empty($password)) {
        throw new Exception('请输入密码');
    }

    // 验证用户名长度
    if (strlen($username) < 3) {
        throw new Exception('用户名至少需要3个字符');
    }

    // 验证密码长度
    if (strlen($password) < 6) {
        throw new Exception('密码至少需要6个字符');
    }

    // 检查是否需要验证码
    require_once '../auth/SystemConfig.php';
    $needsCaptcha = SystemConfig::get('security', 'enable_login_captcha', '0') === '1';

    if ($needsCaptcha) {
        if (empty($captchaSessionKey)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => '请完成验证码验证',
                'error_type' => 'captcha_required',
                'timestamp' => time()
            ]);
            exit;
        }

        // 验证验证码是否有效
        $captchaValid = false;
        if (isset($_SESSION['captcha_verified_' . $captchaSessionKey])) {
            $captchaValid = true;
            // 验证码使用后清除
            unset($_SESSION['captcha_verified_' . $captchaSessionKey]);
        }

        if (!$captchaValid) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => '验证码错误或已过期',
                'error_type' => 'captcha_error',
                'timestamp' => time()
            ]);
            exit;
        }
    }

    // 尝试登录
    $result = Auth::login($username, $password);
    
    if ($result['success']) {
        // 如果选择了记住我，设置更长的会话时间和cookie
        if ($remember) {
            // 使用Auth类的记住我方法
            Auth::setRememberMe();
        } else {
            // 如果没有选择记住我，标记为普通会话
            $_SESSION['remember_me'] = false;
        }

        $response = [
            'success' => true,
            'message' => $result['message'],
            'user' => $result['user'],
            'redirect' => '/users/',
            'timestamp' => time()
        ];

        // 清理输出缓冲区，防止警告或其他输出污染JSON
        ob_clean();
        echo json_encode($response);

        // 记录登录日志（在响应输出后）
        error_log("用户登录成功: {$username} (记住我: " . ($remember ? '是' : '否') . ") - " . date('Y-m-d H:i:s'));
    } else {
        // 根据错误类型返回不同的响应
        $errorType = $result['error_type'] ?? 'unknown_error';

        http_response_code(401);
        $response = [
            'success' => false,
            'message' => $result['message'],
            'error_type' => $errorType,
            'timestamp' => time()
        ];

        // 清理输出缓冲区，防止警告或其他输出污染JSON
        ob_clean();
        echo json_encode($response);

        // 记录登录失败日志（在响应输出后）
        error_log("用户登录失败: {$username} - " . date('Y-m-d H:i:s'));
    }
    
} catch (Exception $e) {
    http_response_code(400);
    // 清理输出缓冲区，防止警告或其他输出污染JSON
    ob_clean();
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => time()
    ]);
}
?>
