<?php
/**
 * 👤 个人资料API接口
 * 
 * 功能：处理个人资料更新请求
 * 方法：POST
 * 参数：nickname, email, current_password, new_password, confirm_password
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求'
    ]);
    exit;
}

// 引入认证类
require_once '../auth/Auth.php';

// 检查用户是否已登录
if (!Auth::isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
}

try {
    // 获取当前用户信息
    $current_user = Auth::getCurrentUser();
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 如果不是JSON请求，尝试从POST获取
    if (!$input) {
        $input = $_POST;
    }
    
    $nickname = trim($input['nickname'] ?? '');
    $email = trim($input['email'] ?? '');
    $current_password = $input['current_password'] ?? '';
    $new_password = $input['new_password'] ?? '';
    $confirm_password = $input['confirm_password'] ?? '';
    
    // 验证输入
    if (empty($email)) {
        throw new Exception('请输入邮箱地址');
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('请输入有效的邮箱地址');
    }
    
    // 引入数据库配置
    require_once '../config/database.php';
    $db = Database::getInstance();
    
    // 验证当前密码（如果要修改密码）
    if (!empty($new_password)) {
        if (empty($current_password)) {
            throw new Exception('修改密码时必须输入当前密码');
        }
        
        $user = $db->fetch("SELECT password FROM users WHERE id = ?", [$current_user['id']]);
        if (!password_verify($current_password, $user['password'])) {
            throw new Exception('当前密码不正确');
        }
        
        if (strlen($new_password) < 6) {
            throw new Exception('新密码至少需要6个字符');
        }
        
        if ($new_password !== $confirm_password) {
            throw new Exception('两次输入的新密码不一致');
        }
    }
    
    // 检查邮箱是否被其他用户使用
    if ($email !== $current_user['email']) {
        $existing = $db->fetch("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $current_user['id']]);
        if ($existing) {
            throw new Exception('该邮箱已被其他用户使用');
        }
    }
    
    // 更新用户信息
    if (!empty($new_password)) {
        $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        $affected = $db->update(
            "UPDATE users SET nickname = ?, email = ?, password = ?, updated_at = NOW() WHERE id = ?",
            [$nickname, $email, $password_hash, $current_user['id']]
        );
    } else {
        $affected = $db->update(
            "UPDATE users SET nickname = ?, email = ?, updated_at = NOW() WHERE id = ?",
            [$nickname, $email, $current_user['id']]
        );
    }
    
    if ($affected === 0) {
        throw new Exception('没有数据被更新');
    }
    
    // 记录操作日志
    Auth::logOperation('profile_update', '更新个人资料', [
        'nickname' => $nickname,
        'email' => $email,
        'password_changed' => !empty($new_password)
    ]);
    
    // 更新会话中的用户信息
    $_SESSION['user_email'] = $email;
    $_SESSION['user_name'] = $nickname ?: $current_user['username'];
    
    // 获取更新后的用户信息
    $updated_user = $db->fetch(
        "SELECT id, username, email, nickname, role, user_group, created_at, updated_at FROM users WHERE id = ?",
        [$current_user['id']]
    );
    
    echo json_encode([
        'success' => true,
        'message' => '个人资料更新成功',
        'user' => [
            'id' => $updated_user['id'],
            'username' => $updated_user['username'],
            'name' => $updated_user['nickname'] ?: $updated_user['username'],
            'email' => $updated_user['email'],
            'role' => $updated_user['role'],
            'group' => $updated_user['user_group'],
            'created_at' => $updated_user['created_at'],
            'updated_at' => $updated_user['updated_at']
        ],
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => time()
    ]);
}
?>
