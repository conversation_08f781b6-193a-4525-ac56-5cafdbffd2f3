<?php
/**
 * 🔧 系统设置页面
 * 
 * 功能：系统配置管理，包含网站基本信息、邮件设置、安全设置等
 */

require_once '../config/database.php';
require_once '../auth/auth.php';
require_once '../auth/SystemConfig.php';

// 检查网站访问权限
Auth::requireSiteAccess();

// 获取数据库连接
global $pdo;

// 检查管理员权限
if (!Auth::isLoggedIn() || !Auth::hasRole(Auth::ROLE_ADMIN)) {
    header('Location: ../login.php');
    exit;
}

$current_user = Auth::getCurrentUser();
$page_title = '系统设置';

// 获取当前系统设置
try {
    $stmt = $pdo->query("SELECT * FROM settings ORDER BY category, setting_key");
    $all_settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 按分类组织设置
    $settings = [];
    foreach ($all_settings as $setting) {
        $settings[$setting['category']][$setting['setting_key']] = $setting['setting_value'];
    }
} catch (PDOException $e) {
    $settings = [];
}

// 默认设置值
$default_settings = [
    'website' => [
        'site_name' => '管理系统',
        'site_description' => '现代化PHP管理系统',
        'site_url' => 'http://localhost:8080',
        'admin_email' => '<EMAIL>',
        'timezone' => 'Asia/Shanghai',
        'language' => 'zh-CN'
    ],
    'email' => [
        'smtp_host' => '',
        'smtp_port' => '587',
        'smtp_username' => '',
        'smtp_password' => '',
        'smtp_encryption' => 'tls',
        'from_email' => '',
        'from_name' => ''
    ],
    'security' => [
        'session_timeout' => '3600',
        'max_login_attempts' => '5',
        'password_min_length' => '6',
        'password_require_uppercase' => '0',
        'password_require_lowercase' => '0',
        'password_require_numbers' => '0',
        'password_require_symbols' => '0',
        'login_attempt_lockout_time' => '900',
        'remember_me_duration' => '2592000',
        'require_email_verification' => '1',
        'enable_two_factor' => '0',
        'login_log_retention' => '30',
        'enable_login' => '1', // 网站开放访问
        'enable_register' => '1',
        'enable_password_reset' => '1',
        'captcha_type' => 'math',
        'enable_login_captcha' => '1',
        'enable_register_captcha' => '1',
        'enable_reset_captcha' => '1',
        'captcha_difficulty' => 'medium'
    ],
    'email_template' => [
        'register_verify_subject' => '欢迎注册 - 请验证您的邮箱',
        'register_verify_template' => 'register_verify.html',
        'password_reset_subject' => '密码重置请求',
        'password_reset_template' => 'password_reset.html',
        'verify_code_subject' => '您的验证码',
        'verify_code_template' => 'verify_code.html',
        'code_expire_time' => '30'
    ]
];

// 合并默认设置和数据库设置
foreach ($default_settings as $category => $category_settings) {
    foreach ($category_settings as $key => $default_value) {
        if (!isset($settings[$category][$key])) {
            $settings[$category][$key] = $default_value;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SystemConfig::getPageTitle($page_title); ?></title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="../assets/css/variables.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/toast-styles.css">
    <link rel="stylesheet" href="../assets/components/settings-panel.css">
    <link rel="stylesheet" href="../assets/components/theme-switcher.css">
    <link rel="stylesheet" href="../assets/components/loading-spinner.css">
    <link rel="stylesheet" href="../assets/components/modal.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/settings.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- 页头 -->
        <?php include 'includes/header.php'; ?>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                        </svg>
                        系统设置
                    </h1>
                    <p class="page-subtitle">配置系统基本参数、邮件服务和安全策略</p>
                </div>

                <!-- 设置内容 -->
                <div class="settings-container">
                    <!-- 设置导航 -->
                    <div class="settings-nav">
                        <div class="nav-tabs">
                            <button class="nav-tab active" data-tab="website">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <line x1="2" y1="12" x2="22" y2="12"/>
                                    <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
                                </svg>
                                网站信息
                            </button>
                            <button class="nav-tab" data-tab="email">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                    <polyline points="22,6 12,13 2,6"/>
                                </svg>
                                邮件设置
                            </button>
                            <button class="nav-tab" data-tab="security">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                                </svg>
                                安全设置
                            </button>
                            <button class="nav-tab" data-tab="email_template">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                    <polyline points="22,6 12,13 2,6"/>
                                </svg>
                                邮件模板
                            </button>
                        </div>
                    </div>

                    <!-- 设置表单 -->
                    <div class="settings-content">
                        <form id="settingsForm" class="settings-form">
                            <!-- 网站信息设置 -->
                            <div class="tab-content active" id="website-tab">
                                <div class="settings-section">
                                    <h3 class="section-title">网站基本信息</h3>
                                    <p class="section-description">配置网站的基本信息和显示设置</p>

                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="site_name">网站名称</label>
                                            <input type="text" id="site_name" name="website[site_name]" 
                                                   value="<?php echo htmlspecialchars($settings['website']['site_name']); ?>" 
                                                   placeholder="请输入网站名称">
                                        </div>

                                        <div class="form-group">
                                            <label for="site_url">网站地址</label>
                                            <input type="url" id="site_url" name="website[site_url]" 
                                                   value="<?php echo htmlspecialchars($settings['website']['site_url']); ?>" 
                                                   placeholder="https://example.com">
                                        </div>

                                        <div class="form-group full-width">
                                            <label for="site_description">网站描述</label>
                                            <textarea id="site_description" name="website[site_description]" 
                                                      placeholder="请输入网站描述"><?php echo htmlspecialchars($settings['website']['site_description']); ?></textarea>
                                        </div>

                                        <div class="form-group">
                                            <label for="admin_email">管理员邮箱</label>
                                            <input type="email" id="admin_email" name="website[admin_email]" 
                                                   value="<?php echo htmlspecialchars($settings['website']['admin_email']); ?>" 
                                                   placeholder="<EMAIL>">
                                        </div>

                                        <div class="form-group">
                                            <label for="timezone">时区设置</label>
                                            <select id="timezone" name="website[timezone]">
                                                <option value="Asia/Shanghai" <?php echo ($settings['website']['timezone'] === 'Asia/Shanghai') ? 'selected' : ''; ?>>Asia/Shanghai (北京时间)</option>
                                                <option value="UTC" <?php echo ($settings['website']['timezone'] === 'UTC') ? 'selected' : ''; ?>>UTC (协调世界时)</option>
                                                <option value="America/New_York" <?php echo ($settings['website']['timezone'] === 'America/New_York') ? 'selected' : ''; ?>>America/New_York (东部时间)</option>
                                                <option value="Europe/London" <?php echo ($settings['website']['timezone'] === 'Europe/London') ? 'selected' : ''; ?>>Europe/London (伦敦时间)</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="language">默认语言</label>
                                            <select id="language" name="website[language]">
                                                <option value="zh-CN" <?php echo ($settings['website']['language'] === 'zh-CN') ? 'selected' : ''; ?>>简体中文</option>
                                                <option value="en-US" <?php echo ($settings['website']['language'] === 'en-US') ? 'selected' : ''; ?>>English</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 邮件设置 -->
                            <div class="tab-content" id="email-tab">
                                <div class="settings-section">
                                    <h3 class="section-title">SMTP 邮件服务</h3>
                                    <p class="section-description">配置邮件发送服务器设置</p>

                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="smtp_host">SMTP 服务器</label>
                                            <input type="text" id="smtp_host" name="email[smtp_host]"
                                                   value="<?php echo htmlspecialchars($settings['email']['smtp_host']); ?>"
                                                   placeholder="smtp.example.com">
                                        </div>

                                        <div class="form-group">
                                            <label for="smtp_port">SMTP 端口</label>
                                            <input type="number" id="smtp_port" name="email[smtp_port]"
                                                   value="<?php echo htmlspecialchars($settings['email']['smtp_port']); ?>"
                                                   placeholder="587">
                                        </div>

                                        <div class="form-group">
                                            <label for="smtp_username">SMTP 用户名</label>
                                            <input type="text" id="smtp_username" name="email[smtp_username]"
                                                   value="<?php echo htmlspecialchars($settings['email']['smtp_username']); ?>"
                                                   placeholder="<EMAIL>">
                                        </div>

                                        <div class="form-group">
                                            <label for="smtp_password">SMTP 密码</label>
                                            <input type="password" id="smtp_password" name="email[smtp_password]"
                                                   value="<?php echo htmlspecialchars($settings['email']['smtp_password']); ?>"
                                                   placeholder="请输入SMTP密码">
                                        </div>

                                        <div class="form-group">
                                            <label for="smtp_encryption">加密方式</label>
                                            <select id="smtp_encryption" name="email[smtp_encryption]">
                                                <option value="tls" <?php echo ($settings['email']['smtp_encryption'] === 'tls') ? 'selected' : ''; ?>>TLS</option>
                                                <option value="ssl" <?php echo ($settings['email']['smtp_encryption'] === 'ssl') ? 'selected' : ''; ?>>SSL</option>
                                                <option value="none" <?php echo ($settings['email']['smtp_encryption'] === 'none') ? 'selected' : ''; ?>>无加密</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="from_email">发件人邮箱</label>
                                            <input type="email" id="from_email" name="email[from_email]"
                                                   value="<?php echo htmlspecialchars($settings['email']['from_email']); ?>"
                                                   placeholder="<EMAIL>">
                                        </div>

                                        <div class="form-group">
                                            <label for="from_name">发件人名称</label>
                                            <input type="text" id="from_name" name="email[from_name]"
                                                   value="<?php echo htmlspecialchars($settings['email']['from_name']); ?>"
                                                   placeholder="系统通知">
                                        </div>
                                    </div>

                                    <!-- 邮件配置说明 -->
                                    <div class="email-config-info">
                                        <div class="info-header">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="var(--primary-color)" stroke-width="2">
                                                <circle cx="12" cy="12" r="10"/>
                                                <path d="M12 6v6l4 2"/>
                                            </svg>
                                            <span>常用SMTP配置参考</span>
                                        </div>
                                        <div class="smtp-configs">
                                            <div class="smtp-item">
                                                <strong>QQ邮箱：</strong>smtp.qq.com，端口 465 (SSL) 或 587 (TLS)
                                            </div>
                                            <div class="smtp-item">
                                                <strong>163邮箱：</strong>smtp.163.com，端口 465 (SSL) 或 25 (无加密)
                                            </div>
                                            <div class="smtp-item">
                                                <strong>Gmail：</strong>smtp.gmail.com，端口 465 (SSL) 或 587 (TLS)
                                            </div>
                                            <div class="smtp-note">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"/>
                                                </svg>
                                                注意：大部分邮箱需要使用授权码而非登录密码作为SMTP密码
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button type="button" class="btn btn-secondary" onclick="testEmailSettings()">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                                                <polyline points="22,4 12,14.01 9,11.01"/>
                                            </svg>
                                            测试邮件发送
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 安全设置 -->
                            <div class="tab-content" id="security-tab">
                                <div class="settings-section">
                                    <h3 class="section-title">安全策略</h3>
                                    <p class="section-description">配置系统安全相关设置</p>

                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="session_timeout">会话超时时间（秒）</label>
                                            <input type="number" id="session_timeout" name="security[session_timeout]"
                                                   value="<?php echo htmlspecialchars($settings['security']['session_timeout']); ?>"
                                                   placeholder="3600" min="300" max="86400">
                                            <small class="form-help">用户无操作后自动退出的时间</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="max_login_attempts">最大登录尝试次数</label>
                                            <input type="number" id="max_login_attempts" name="security[max_login_attempts]"
                                                   value="<?php echo htmlspecialchars($settings['security']['max_login_attempts']); ?>"
                                                   placeholder="5" min="3" max="20">
                                            <small class="form-help">超过此次数将锁定账户</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="password_min_length">密码最小长度</label>
                                            <input type="number" id="password_min_length" name="security[password_min_length]"
                                                   value="<?php echo htmlspecialchars($settings['security']['password_min_length']); ?>"
                                                   placeholder="6" min="4" max="20">
                                        </div>

                                        <div class="form-group">
                                            <label for="login_attempt_lockout_time">登录失败锁定时间（秒）</label>
                                            <input type="number" id="login_attempt_lockout_time" name="security[login_attempt_lockout_time]"
                                                   value="<?php echo htmlspecialchars($settings['security']['login_attempt_lockout_time']); ?>"
                                                   placeholder="900" min="300" max="3600">
                                            <small class="form-help">登录失败达到最大次数后的锁定时间</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="remember_me_duration">记住我持续时间（秒）</label>
                                            <input type="number" id="remember_me_duration" name="security[remember_me_duration]"
                                                   value="<?php echo htmlspecialchars($settings['security']['remember_me_duration']); ?>"
                                                   placeholder="2592000" min="86400" max="7776000">
                                            <small class="form-help">记住我功能的有效期（默认30天）</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="login_log_retention">登录日志保留天数</label>
                                            <input type="number" id="login_log_retention" name="security[login_log_retention]"
                                                   value="<?php echo htmlspecialchars($settings['security']['login_log_retention']); ?>"
                                                   placeholder="30" min="7" max="365">
                                        </div>

                                        <div class="form-group full-width">
                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="security[require_email_verification]" value="1"
                                                           <?php echo ($settings['security']['require_email_verification'] === '1') ? 'checked' : ''; ?>>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <strong>要求邮箱验证</strong>
                                                        <small>新用户注册后需要验证邮箱才能登录</small>
                                                    </span>
                                                </label>
                                            </div>
                                        </div>

                                        <!-- 密码复杂度要求 -->
                                        <div class="form-group full-width">
                                            <h4 style="margin: 20px 0 15px 0; color: var(--text-primary); font-size: 16px;">密码复杂度要求</h4>
                                            <div class="form-grid" style="grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 12px;">
                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="security[password_require_uppercase]" value="1"
                                                               <?php echo ($settings['security']['password_require_uppercase'] === '1') ? 'checked' : ''; ?>>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <strong>必须包含大写字母</strong>
                                                            <small>密码必须包含至少一个大写字母</small>
                                                        </span>
                                                    </label>
                                                </div>

                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="security[password_require_lowercase]" value="1"
                                                               <?php echo ($settings['security']['password_require_lowercase'] === '1') ? 'checked' : ''; ?>>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <strong>必须包含小写字母</strong>
                                                            <small>密码必须包含至少一个小写字母</small>
                                                        </span>
                                                    </label>
                                                </div>

                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="security[password_require_numbers]" value="1"
                                                               <?php echo ($settings['security']['password_require_numbers'] === '1') ? 'checked' : ''; ?>>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <strong>必须包含数字</strong>
                                                            <small>密码必须包含至少一个数字</small>
                                                        </span>
                                                    </label>
                                                </div>

                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="security[password_require_symbols]" value="1"
                                                               <?php echo ($settings['security']['password_require_symbols'] === '1') ? 'checked' : ''; ?>>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <strong>必须包含特殊字符</strong>
                                                            <small>密码必须包含至少一个特殊字符</small>
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group full-width">
                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="security[enable_two_factor]" value="1"
                                                           <?php echo ($settings['security']['enable_two_factor'] === '1') ? 'checked' : ''; ?>>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <strong>启用双因素认证</strong>
                                                        <small>为管理员账户启用双因素认证（开发中）</small>
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 功能开关 -->
                                    <div class="settings-section function-switches">
                                        <h3 class="section-title">功能开关</h3>
                                        <p class="section-description">控制系统核心功能的启用状态</p>

                                        <div class="form-grid">
                                            <div class="form-group">
                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="security[enable_login]" value="1"
                                                               <?php echo ($settings['security']['enable_login'] === '1') ? 'checked' : ''; ?>>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <strong>网站开放访问</strong>
                                                            <small>关闭后仅允许管理员登录系统</small>
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="security[enable_register]" value="1"
                                                               <?php echo ($settings['security']['enable_register'] === '1') ? 'checked' : ''; ?>>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <strong>启用注册功能</strong>
                                                            <small>关闭后新用户将无法注册账户</small>
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="security[enable_password_reset]" value="1"
                                                               <?php echo ($settings['security']['enable_password_reset'] === '1') ? 'checked' : ''; ?>>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <strong>启用密码找回功能</strong>
                                                            <small>关闭后用户将无法通过邮箱找回密码</small>
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="security[profile_require_verify_code]" value="1"
                                                               <?php echo ($settings['security']['profile_require_verify_code'] === '1') ? 'checked' : ''; ?>>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <strong>个人资料修改需要验证码</strong>
                                                            <small>修改密码等敏感操作需要邮箱验证码验证</small>
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 验证码设置 -->
                                    <div class="settings-section captcha-config">
                                        <h3 class="section-title">验证码配置</h3>
                                        <p class="section-description">配置验证码类型和使用场景</p>

                                        <div class="form-grid">
                                            <div class="form-group">
                                                <label for="captcha_type" class="form-label">验证码类型</label>
                                                <select id="captcha_type" name="security[captcha_type]" class="form-control">
                                                    <option value="math" <?php echo ($settings['security']['captcha_type'] ?? 'math') === 'math' ? 'selected' : ''; ?>>数字计算</option>
                                                    <option value="alphanumeric" <?php echo ($settings['security']['captcha_type'] ?? '') === 'alphanumeric' ? 'selected' : ''; ?>>字母数字组合</option>
                                                    <option value="slider" <?php echo ($settings['security']['captcha_type'] ?? '') === 'slider' ? 'selected' : ''; ?>>滑块验证</option>
                                                </select>
                                                <small class="form-help">选择验证码的显示类型</small>
                                            </div>

                                            <div class="form-group">
                                                <label for="captcha_difficulty" class="form-label">验证码难度</label>
                                                <select id="captcha_difficulty" name="security[captcha_difficulty]" class="form-control">
                                                    <option value="easy" <?php echo ($settings['security']['captcha_difficulty'] ?? 'medium') === 'easy' ? 'selected' : ''; ?>>简单</option>
                                                    <option value="medium" <?php echo ($settings['security']['captcha_difficulty'] ?? 'medium') === 'medium' ? 'selected' : ''; ?>>中等</option>
                                                    <option value="hard" <?php echo ($settings['security']['captcha_difficulty'] ?? 'medium') === 'hard' ? 'selected' : ''; ?>>困难</option>
                                                </select>
                                                <small class="form-help">调整验证码的复杂程度</small>
                                            </div>
                                        </div>

                                        <div class="form-grid" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="security[enable_login_captcha]" value="1"
                                                               <?php echo ($settings['security']['enable_login_captcha'] === '1') ? 'checked' : ''; ?>>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <strong>登录时启用验证码</strong>
                                                            <small>用户登录时需要输入验证码</small>
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="security[enable_register_captcha]" value="1"
                                                               <?php echo ($settings['security']['enable_register_captcha'] === '1') ? 'checked' : ''; ?>>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <strong>注册时启用验证码</strong>
                                                            <small>用户注册时需要输入验证码</small>
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="security[enable_reset_captcha]" value="1"
                                                               <?php echo ($settings['security']['enable_reset_captcha'] === '1') ? 'checked' : ''; ?>>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <strong>密码找回时启用验证码</strong>
                                                            <small>用户找回密码时需要输入验证码</small>
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 验证码预览 -->
                                        <div class="captcha-preview">
                                            <h4>验证码预览</h4>
                                            <div class="preview-container">
                                                <div id="captcha-preview-area">
                                                    <div class="captcha-loading">选择验证码类型查看预览</div>
                                                </div>
                                                <button type="button" class="btn btn-secondary btn-sm" onclick="refreshCaptchaPreview()">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <polyline points="1,4 1,10 7,10"/>
                                                        <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                                                    </svg>
                                                    刷新预览
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 邮件模板设置 -->
                            <div class="tab-content" id="email_template-tab">
                                <div class="settings-section">
                                    <h3 class="section-title">邮件模板配置</h3>
                                    <p class="section-description">配置系统邮件模板和相关设置</p>

                                    <div class="form-grid">
                                        <!-- 注册验证邮件 -->
                                        <div class="form-group">
                                            <label for="register_verify_subject" class="form-label">注册验证邮件主题</label>
                                            <input type="text" id="register_verify_subject" name="email_template[register_verify_subject]"
                                                   class="form-control" value="<?php echo htmlspecialchars($settings['email_template']['register_verify_subject'] ?? ''); ?>"
                                                   placeholder="欢迎注册 - 请验证您的邮箱">
                                        </div>

                                        <div class="form-group">
                                            <label for="register_verify_template" class="form-label">注册验证邮件模板</label>
                                            <div class="template-selector">
                                                <select id="register_verify_template" name="email_template[register_verify_template]" class="form-control">
                                                    <option value="register_verify.html" <?php echo ($settings['email_template']['register_verify_template'] ?? '') === 'register_verify.html' ? 'selected' : ''; ?>>默认注册验证模板</option>
                                                </select>
                                                <button type="button" class="btn btn-secondary btn-sm" onclick="editTemplate('register_verify')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                                    </svg>
                                                    编辑模板
                                                </button>
                                            </div>
                                        </div>

                                        <!-- 密码重置邮件 -->
                                        <div class="form-group">
                                            <label for="password_reset_subject" class="form-label">密码重置邮件主题</label>
                                            <input type="text" id="password_reset_subject" name="email_template[password_reset_subject]"
                                                   class="form-control" value="<?php echo htmlspecialchars($settings['email_template']['password_reset_subject'] ?? ''); ?>"
                                                   placeholder="密码重置请求">
                                        </div>

                                        <div class="form-group">
                                            <label for="password_reset_template" class="form-label">密码重置邮件模板</label>
                                            <div class="template-selector">
                                                <select id="password_reset_template" name="email_template[password_reset_template]" class="form-control">
                                                    <option value="password_reset.html" <?php echo ($settings['email_template']['password_reset_template'] ?? '') === 'password_reset.html' ? 'selected' : ''; ?>>默认密码重置模板</option>
                                                </select>
                                                <button type="button" class="btn btn-secondary btn-sm" onclick="editTemplate('password_reset')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                                    </svg>
                                                    编辑模板
                                                </button>
                                            </div>
                                        </div>

                                        <!-- 通用验证码邮件 -->
                                        <div class="form-group">
                                            <label for="verify_code_subject" class="form-label">验证码邮件主题</label>
                                            <input type="text" id="verify_code_subject" name="email_template[verify_code_subject]"
                                                   class="form-control" value="<?php echo htmlspecialchars($settings['email_template']['verify_code_subject'] ?? ''); ?>"
                                                   placeholder="您的验证码">
                                        </div>

                                        <div class="form-group">
                                            <label for="verify_code_template" class="form-label">验证码邮件模板</label>
                                            <div class="template-selector">
                                                <select id="verify_code_template" name="email_template[verify_code_template]" class="form-control">
                                                    <option value="verify_code.html" <?php echo ($settings['email_template']['verify_code_template'] ?? '') === 'verify_code.html' ? 'selected' : ''; ?>>默认验证码模板</option>
                                                </select>
                                                <button type="button" class="btn btn-secondary btn-sm" onclick="editTemplate('verify_code')">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                                    </svg>
                                                    编辑模板
                                                </button>
                                            </div>
                                        </div>

                                        <!-- 验证码有效期 -->
                                        <div class="form-group">
                                            <label for="code_expire_time" class="form-label">验证码有效期（分钟）</label>
                                            <input type="number" id="code_expire_time" name="email_template[code_expire_time]"
                                                   class="form-control" value="<?php echo htmlspecialchars($settings['email_template']['code_expire_time'] ?? '30'); ?>"
                                                   min="5" max="1440" placeholder="30">
                                            <small class="form-text">验证码的有效时间，建议设置为5-60分钟</small>
                                        </div>
                                    </div>

                                    <!-- 模板变量说明 -->
                                    <div class="template-variables">
                                        <h4>可用模板变量</h4>
                                        <div class="variables-grid">
                                            <div class="variable-item">
                                                <code>{{SITE_NAME}}</code>
                                                <span>网站名称</span>
                                            </div>
                                            <div class="variable-item">
                                                <code>{{USERNAME}}</code>
                                                <span>用户名</span>
                                            </div>
                                            <div class="variable-item">
                                                <code>{{VERIFY_CODE}}</code>
                                                <span>验证码</span>
                                            </div>
                                            <div class="variable-item">
                                                <code>{{VERIFY_URL}}</code>
                                                <span>验证链接</span>
                                            </div>
                                            <div class="variable-item">
                                                <code>{{RESET_CODE}}</code>
                                                <span>重置码</span>
                                            </div>
                                            <div class="variable-item">
                                                <code>{{RESET_URL}}</code>
                                                <span>重置链接</span>
                                            </div>
                                            <div class="variable-item">
                                                <code>{{EXPIRE_TIME}}</code>
                                                <span>有效期（分钟）</span>
                                            </div>
                                            <div class="variable-item">
                                                <code>{{CURRENT_YEAR}}</code>
                                                <span>当前年份</span>
                                            </div>
                                            <div class="variable-item">
                                                <code>{{ADMIN_EMAIL}}</code>
                                                <span>管理员邮箱</span>
                                            </div>
                                            <div class="variable-item">
                                                <code>{{REQUEST_TIME}}</code>
                                                <span>请求时间</span>
                                            </div>
                                            <div class="variable-item">
                                                <code>{{REQUEST_IP}}</code>
                                                <span>请求IP</span>
                                            </div>
                                            <div class="variable-item">
                                                <code>{{ACTION_TYPE}}</code>
                                                <span>操作类型</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 保存按钮 -->
                            <div class="form-footer">
                                <div class="form-actions">
                                    <button type="button" class="btn btn-secondary" onclick="resetSettings()">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="1,4 1,10 7,10"/>
                                            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                                        </svg>
                                        重置
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                                            <polyline points="17,21 17,13 7,13 7,21"/>
                                            <polyline points="7,3 7,8 15,8"/>
                                        </svg>
                                        保存设置
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>

        <!-- 页脚 -->
        <?php include 'includes/footer.php'; ?>
    </div>

    <!-- 邮件模板编辑器样式 -->
    <style>
        /* 邮件配置说明框样式 */
        .email-config-info {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            font-size: 14px;
        }

        .info-header {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
        }

        .smtp-configs {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .smtp-item {
            color: var(--text-secondary);
            line-height: 1.4;
        }

        .smtp-item strong {
            color: var(--text-primary);
            font-weight: 600;
        }

        .smtp-note {
            display: flex;
            align-items: flex-start;
            gap: 6px;
            margin-top: 12px;
            padding: 8px 12px;
            background: var(--bg-tertiary);
            border-radius: 6px;
            color: var(--text-tertiary);
            font-size: 12px;
            line-height: 1.4;
        }

        .smtp-note svg {
            margin-top: 1px;
            flex-shrink: 0;
        }

        .template-selector {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .template-selector select {
            flex: 1;
        }

        .template-variables {
            margin-top: 30px;
            padding: 20px;
            background: var(--bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .template-variables h4 {
            margin: 0 0 15px 0;
            color: var(--text-primary);
            font-size: 16px;
        }

        .variables-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .variable-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: 6px;
            font-size: 14px;
        }

        .variable-item code {
            background: var(--primary-light);
            color: var(--primary-color);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .variable-item span {
            color: var(--text-secondary);
            font-size: 12px;
        }

        .template-editor-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
        }

        .template-editor-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            max-width: 1000px;
            height: 80%;
            background: var(--bg-primary);
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
        }

        .template-editor-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .template-editor-body {
            flex: 1;
            padding: 20px;
            overflow: hidden;
        }

        .template-editor-textarea {
            width: 100%;
            height: 100%;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            outline: none;
        }

        .template-editor-footer {
            padding: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* 验证码预览样式 */
        .captcha-preview {
            margin-top: 30px;
            padding: 20px;
            background: var(--bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .captcha-preview h4 {
            margin: 0 0 15px 0;
            color: var(--text-primary);
            font-size: 16px;
        }

        .preview-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        #captcha-preview-area {
            flex: 1;
            min-height: 60px;
            padding: 15px;
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .captcha-loading {
            color: var(--text-secondary);
            font-style: italic;
        }

        /* 数字计算验证码 */
        .math-captcha {
            font-size: 18px;
            font-weight: bold;
            color: var(--text-primary);
            font-family: 'Courier New', monospace;
        }

        /* 字母数字验证码 */
        .alphanumeric-captcha {
            font-size: 24px;
            font-weight: bold;
            letter-spacing: 3px;
            color: var(--primary-color);
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            padding: 10px 20px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        /* 滑块验证码 */
        .slider-captcha {
            width: 300px;
            height: 40px;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .slider-track {
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, var(--primary-light), var(--primary-color));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .slider-thumb {
            position: absolute;
            left: 2px;
            top: 2px;
            width: 36px;
            height: 36px;
            background: white;
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .slider-thumb:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
    </style>

    <!-- 系统设置页面额外样式 -->
    <style>
        /* 优化安全设置布局 */
        .settings-section {
            margin-bottom: 30px;
        }

        .settings-section:last-child {
            margin-bottom: 0;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .checkbox-group {
            margin-bottom: 15px;
        }

        .checkbox-group:last-child {
            margin-bottom: 0;
        }

        .checkbox-label {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            cursor: pointer;
            padding: 12px;
            border-radius: 8px;
            transition: background-color 0.2s ease;
        }

        .checkbox-label:hover {
            background: rgba(59, 130, 246, 0.05);
        }

        .checkbox-text {
            flex: 1;
        }

        .checkbox-text strong {
            display: block;
            margin-bottom: 4px;
            color: var(--text-primary);
            font-weight: 600;
        }

        .checkbox-text small {
            color: var(--text-secondary);
            font-size: 13px;
            line-height: 1.4;
        }

        /* 紧凑的功能开关布局 */
        .function-switches .form-grid {
            gap: 8px;
        }

        .function-switches .checkbox-label {
            padding: 10px 12px;
            border: 1px solid var(--border-light);
            border-radius: 6px;
            background: var(--bg-primary);
        }

        .function-switches .checkbox-label:hover {
            border-color: var(--primary-color);
            background: rgba(59, 130, 246, 0.02);
        }

        /* 验证码配置区域 */
        .captcha-config .form-grid {
            gap: 15px;
        }

        .captcha-config .checkbox-group {
            margin-bottom: 8px;
        }
    </style>

    <!-- 系统设置页面脚本 -->
    <script>
        /**
         * 系统设置页面初始化
         */
        function initializeSettings() {
            console.log('⚙️ 系统设置页面已加载');

            // 初始化标签页切换
            initializeTabSwitching();

            // 初始化表单提交
            initializeFormSubmission();

            // 初始化验证码预览
            initializeCaptchaPreview();
        }

        /**
         * 初始化标签页切换功能
         */
        function initializeTabSwitching() {
            const navTabs = document.querySelectorAll('.nav-tab');
            const tabContents = document.querySelectorAll('.tab-content');

            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // 移除所有活动状态
                    navTabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // 添加当前活动状态
                    this.classList.add('active');
                    document.getElementById(targetTab + '-tab').classList.add('active');
                });
            });
        }

        /**
         * 初始化表单提交
         */
        function initializeFormSubmission() {
            const form = document.getElementById('settingsForm');

            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                await saveSettings();
            });
        }

        /**
         * 保存系统设置
         */
        async function saveSettings() {
            const form = document.getElementById('settingsForm');
            const formData = new FormData(form);

            // 转换为JSON格式
            const settings = {};
            for (let [key, value] of formData.entries()) {
                const parts = key.match(/^(\w+)\[(\w+)\]$/);
                if (parts) {
                    const [, category, setting] = parts;
                    if (!settings[category]) {
                        settings[category] = {};
                    }
                    settings[category][setting] = value;
                }
            }

            // 处理复选框（未选中的复选框不会出现在FormData中）
            const checkboxes = form.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                const parts = checkbox.name.match(/^(\w+)\[(\w+)\]$/);
                if (parts) {
                    const [, category, setting] = parts;
                    if (!settings[category]) {
                        settings[category] = {};
                    }
                    if (!settings[category][setting]) {
                        settings[category][setting] = '0';
                    }
                }
            });

            try {
                const response = await fetch('../api/settings.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'save',
                        settings: settings
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showToast('设置保存成功', 'success');
                } else {
                    showToast(result.message || '保存设置失败', 'error');
                }
            } catch (error) {
                console.error('保存设置错误:', error);
                showToast('网络错误，请重试', 'error');
            }
        }

        /**
         * 测试邮件设置
         */
        async function testEmailSettings() {
            console.log('开始测试邮件设置...');

            const emailInputs = document.querySelectorAll('#email-tab input, #email-tab select');
            const emailSettings = {};

            emailInputs.forEach(input => {
                const parts = input.name.match(/^email\[(\w+)\]$/);
                if (parts) {
                    emailSettings[parts[1]] = input.value;
                }
            });

            console.log('邮件设置:', emailSettings);

            if (!emailSettings.smtp_host || !emailSettings.smtp_username || !emailSettings.smtp_password) {
                showToast('请先填写完整的SMTP配置（服务器、用户名、密码）', 'warning');
                return;
            }

            if (!emailSettings.from_email) {
                showToast('请先填写发件人邮箱', 'warning');
                return;
            }

            // 创建自定义弹窗HTML
            const modalHtml = `
                <div id="test-email-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                    <div style="background: var(--bg-primary); border-radius: 12px; padding: 2rem; max-width: 400px; width: 90%; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
                        <div style="text-align: center; margin-bottom: 1.5rem;">
                            <div style="width: 64px; height: 64px; margin: 0 auto 1rem; background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light)); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                    <polyline points="22,6 12,13 2,6"/>
                                </svg>
                            </div>
                            <h3 style="margin: 0 0 0.5rem 0; color: var(--text-primary); font-size: 20px; font-weight: 600;">测试邮件发送</h3>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 14px;">请输入接收测试邮件的邮箱地址</p>
                        </div>

                        <div style="margin-bottom: 1.5rem;">
                            <input type="email" id="test-email-input" placeholder="请输入邮箱地址"
                                   style="width: 100%; padding: 14px 16px; border: 2px solid var(--border-color); border-radius: 8px;
                                          font-size: 14px; background: var(--bg-secondary); color: var(--text-primary);
                                          transition: all 0.2s ease; box-sizing: border-box; outline: none;">
                        </div>

                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button id="test-email-cancel" style="padding: 12px 24px; border: 1px solid var(--border-color); background: var(--bg-secondary); color: var(--text-primary); border-radius: 6px; cursor: pointer;">取消</button>
                            <button id="test-email-send" style="padding: 12px 24px; border: none; background: var(--primary-color); color: white; border-radius: 6px; cursor: pointer;">发送测试邮件</button>
                        </div>
                    </div>
                </div>
            `;

            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            const modal = document.getElementById('test-email-modal');
            const emailInput = document.getElementById('test-email-input');
            const cancelBtn = document.getElementById('test-email-cancel');
            const sendBtn = document.getElementById('test-email-send');

            // 聚焦输入框
            setTimeout(() => emailInput.focus(), 100);

            // 处理按钮点击
            const handleSend = async () => {
                const email = emailInput.value.trim();
                if (!email) {
                    showToast('请输入邮箱地址', 'warning');
                    return;
                }

                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    showToast('请输入有效的邮箱地址', 'warning');
                    return;
                }

                modal.remove();
                await sendTestEmail(emailSettings, email);
            };

            const handleCancel = () => {
                modal.remove();
            };

            // 绑定事件
            sendBtn.addEventListener('click', handleSend);
            cancelBtn.addEventListener('click', handleCancel);
            emailInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleSend();
                }
            });

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    handleCancel();
                }
            });
        }

        /**
         * 发送测试邮件
         */
        async function sendTestEmail(emailSettings, testEmail) {
            showToast('正在发送测试邮件...', 'info');
            console.log('发送测试邮件到:', testEmail);

            try {
                const response = await fetch('../api/verify-email.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'test_email',
                        settings: emailSettings,
                        test_email: testEmail
                    })
                });

                console.log('API响应状态:', response.status);
                const responseText = await response.text();
                console.log('API响应内容:', responseText);

                let result_data;
                try {
                    result_data = JSON.parse(responseText);
                } catch (e) {
                    console.error('JSON解析错误:', e);
                    showToast('服务器响应格式错误: ' + responseText, 'error');
                    return;
                }

                console.log('解析后的结果:', result_data);

                if (result_data.success) {
                    showToast(result_data.message || '测试邮件发送成功', 'success');
                } else {
                    showToast(result_data.message || '邮件发送测试失败', 'error');
                }
            } catch (error) {
                console.error('发送邮件网络错误:', error);
                showToast('网络错误：' + error.message, 'error');
            }
        }





        /**
         * 重置设置
         */
        function resetSettings() {
            if (window.showModal && typeof window.showModal.confirm === 'function') {
                window.showModal.confirm({
                    title: '重置设置',
                    message: '确定要重置所有设置到默认值吗？此操作不可撤销。',
                    confirmText: '确定重置',
                    cancelText: '取消'
                }).then(confirmed => {
                    if (confirmed) {
                        location.reload();
                    }
                });
            } else {
                if (confirm('确定要重置所有设置到默认值吗？此操作不可撤销。')) {
                    location.reload();
                }
            }
        }

        /**
         * 编辑邮件模板
         */
        function editTemplate(templateType) {
            const templateFiles = {
                'register_verify': 'register_verify.html',
                'password_reset': 'password_reset.html',
                'verify_code': 'verify_code.html'
            };

            const templateNames = {
                'register_verify': '注册验证邮件模板',
                'password_reset': '密码重置邮件模板',
                'verify_code': '验证码邮件模板'
            };

            const filename = templateFiles[templateType];
            const templateName = templateNames[templateType];

            if (!filename) {
                showToast('未知的模板类型', 'error');
                return;
            }

            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'template-editor-modal';
            modal.innerHTML = `
                <div class="template-editor-content">
                    <div class="template-editor-header">
                        <h3>📧 编辑 ${templateName}</h3>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="closeTemplateEditor()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                        </button>
                    </div>
                    <div class="template-editor-main">
                        <div class="template-editor-sidebar">
                            <h4>📝 可用变量</h4>
                            <div class="template-variables">
                                <div class="variable-group">
                                    <h5>基础变量</h5>
                                    <div class="variable-item" onclick="insertVariable('{{SITE_NAME}}')">
                                        <code>{{SITE_NAME}}</code>
                                        <span>网站名称</span>
                                    </div>
                                    <div class="variable-item" onclick="insertVariable('{{USERNAME}}')">
                                        <code>{{USERNAME}}</code>
                                        <span>用户名</span>
                                    </div>
                                    <div class="variable-item" onclick="insertVariable('{{ADMIN_EMAIL}}')">
                                        <code>{{ADMIN_EMAIL}}</code>
                                        <span>管理员邮箱</span>
                                    </div>
                                    <div class="variable-item" onclick="insertVariable('{{CURRENT_YEAR}}')">
                                        <code>{{CURRENT_YEAR}}</code>
                                        <span>当前年份</span>
                                    </div>
                                </div>
                                <div class="variable-group">
                                    <h5>验证相关</h5>
                                    <div class="variable-item" onclick="insertVariable('{{VERIFY_URL}}')">
                                        <code>{{VERIFY_URL}}</code>
                                        <span>验证链接</span>
                                    </div>
                                    <div class="variable-item" onclick="insertVariable('{{RESET_URL}}')">
                                        <code>{{RESET_URL}}</code>
                                        <span>重置链接</span>
                                    </div>
                                    <div class="variable-item" onclick="insertVariable('{{RESET_CODE}}')">
                                        <code>{{RESET_CODE}}</code>
                                        <span>重置验证码</span>
                                    </div>
                                    <div class="variable-item" onclick="insertVariable('{{EXPIRE_TIME}}')">
                                        <code>{{EXPIRE_TIME}}</code>
                                        <span>过期时间</span>
                                    </div>
                                </div>
                                <div class="variable-group">
                                    <h5>请求信息</h5>
                                    <div class="variable-item" onclick="insertVariable('{{REQUEST_TIME}}')">
                                        <code>{{REQUEST_TIME}}</code>
                                        <span>请求时间</span>
                                    </div>
                                    <div class="variable-item" onclick="insertVariable('{{REQUEST_IP}}')">
                                        <code>{{REQUEST_IP}}</code>
                                        <span>请求IP</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="template-editor-workspace">
                            <div class="editor-toolbar">
                                <div class="toolbar-left">
                                    <div class="editor-mode-switcher">
                                        <button type="button" class="mode-btn active" data-mode="code" onclick="switchEditorMode('code')">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="16 18 22 12 16 6"/>
                                                <polyline points="8 6 2 12 8 18"/>
                                            </svg>
                                            代码模式
                                        </button>
                                        <button type="button" class="mode-btn" data-mode="visual" onclick="switchEditorMode('visual')">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                                <circle cx="12" cy="12" r="3"/>
                                            </svg>
                                            可视化模式
                                        </button>
                                    </div>
                                    <span class="editor-hint">点击左侧变量可快速插入</span>
                                </div>
                                <div class="toolbar-right">
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="formatTemplate()">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="16 18 22 12 16 6"/>
                                            <polyline points="8 6 2 12 8 18"/>
                                        </svg>
                                        格式化代码
                                    </button>
                                </div>
                            </div>
                            <div class="editor-content">
                                <textarea class="template-editor-textarea" id="templateContent" placeholder="正在加载模板内容...&#10;&#10;💡 提示：&#10;• 点击左侧变量可快速插入到光标位置&#10;• 使用格式化按钮可以整理代码格式&#10;• 支持HTML、CSS和JavaScript代码"></textarea>
                                <div class="template-visual-editor" id="visualEditor" style="display: none;">
                                    <iframe id="visualPreview" style="width: 100%; height: 100%; border: none; background: white;"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="template-editor-footer">
                        <button type="button" class="btn btn-secondary" onclick="previewTemplate('${templateType}')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                            预览模板
                        </button>
                        <button type="button" class="btn btn-warning" onclick="resetTemplate('${templateType}')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="1 4 1 10 7 10"/>
                                <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                            </svg>
                            重置模板
                        </button>
                        <button type="button" class="btn btn-primary" onclick="saveTemplate('${templateType}', '${filename}')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                                <polyline points="17,21 17,13 7,13 7,21"/>
                                <polyline points="7,3 7,8 15,8"/>
                            </svg>
                            保存模板
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            modal.style.display = 'block';

            // 加载模板内容
            loadTemplateContent(filename);

            // 添加代码编辑器的实时预览功能
            setTimeout(() => {
                const textarea = document.getElementById('templateContent');
                if (textarea) {
                    textarea.addEventListener('input', debounce(() => {
                        const visualEditor = document.getElementById('visualEditor');
                        if (visualEditor && visualEditor.style.display !== 'none') {
                            updateVisualPreview();
                        }
                    }, 500));
                }
            }, 100);
        }

        /**
         * 加载模板内容
         */
        async function loadTemplateContent(filename) {
            try {
                // 显示加载状态
                document.getElementById('templateContent').value = '<!-- 正在加载模板... -->';

                // 先尝试直接读取文件
                try {
                    const directResponse = await fetch(`muban/${filename}`);
                    if (directResponse.ok) {
                        const content = await directResponse.text();
                        document.getElementById('templateContent').value = content;
                        return;
                    }
                } catch (directError) {
                    console.log('直接读取失败，尝试API方式:', directError);
                }

                // 如果直接读取失败，使用API
                const response = await fetch(`../api/template.api.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load',
                        filename: filename
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        document.getElementById('templateContent').value = result.content;
                    } else {
                        // 如果API也失败，加载默认模板
                        loadDefaultTemplate(filename);
                    }
                } else {
                    // 如果API请求失败，加载默认模板
                    loadDefaultTemplate(filename);
                }
            } catch (error) {
                console.error('加载模板失败:', error);
                loadDefaultTemplate(filename);
            }
        }

        /**
         * 加载默认模板
         */
        function loadDefaultTemplate(filename) {
            const templateType = Object.keys({
                'register_verify.html': 'register_verify',
                'password_reset.html': 'password_reset',
                'verify_code.html': 'verify_code'
            }).find(key => key === filename);

            if (templateType) {
                const defaultTemplates = {
                    'register_verify.html': getDefaultRegisterTemplate(),
                    'password_reset.html': getDefaultPasswordResetTemplate(),
                    'verify_code.html': getDefaultVerifyCodeTemplate()
                };

                document.getElementById('templateContent').value = defaultTemplates[filename] || '<!-- 无法加载模板 -->';
                showToast('已加载默认模板，您可以在此基础上进行修改', 'info');
            } else {
                document.getElementById('templateContent').value = '<!-- 模板文件不存在 -->';
                showToast('模板文件不存在', 'error');
            }
        }

        /**
         * 保存模板
         */
        async function saveTemplate(templateType, filename) {
            const content = document.getElementById('templateContent').value;

            if (!content.trim()) {
                showToast('模板内容不能为空', 'error');
                return;
            }

            try {
                const response = await fetch('../api/template.api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'save',
                        filename: filename,
                        content: content
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showToast('模板保存成功', 'success');
                    closeTemplateEditor();
                } else {
                    showToast(result.message || '保存模板失败', 'error');
                }
            } catch (error) {
                console.error('保存模板错误:', error);
                showToast('网络错误，请重试', 'error');
            }
        }

        /**
         * 预览模板
         */
        function previewTemplate(templateType) {
            const content = document.getElementById('templateContent').value;

            // 替换模板变量为示例数据
            const sampleData = {
                '{{SITE_NAME}}': '管理系统',
                '{{USERNAME}}': '张三',
                '{{VERIFY_CODE}}': '123456',
                '{{VERIFY_URL}}': 'http://localhost:8080/verify?token=sample',
                '{{RESET_CODE}}': '654321',
                '{{RESET_URL}}': 'http://localhost:8080/reset?token=sample',
                '{{EXPIRE_TIME}}': '30',
                '{{CURRENT_YEAR}}': new Date().getFullYear(),
                '{{ADMIN_EMAIL}}': '<EMAIL>',
                '{{REQUEST_TIME}}': new Date().toLocaleString(),
                '{{REQUEST_IP}}': '*************',
                '{{ACTION_TYPE}}': '邮箱验证',
                '{{EXPIRE_TIME_FULL}}': new Date(Date.now() + 30 * 60 * 1000).toLocaleString()
            };

            let previewContent = content;
            for (const [key, value] of Object.entries(sampleData)) {
                previewContent = previewContent.replace(new RegExp(key.replace(/[{}]/g, '\\$&'), 'g'), value);
            }

            // 在新窗口中打开预览
            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(previewContent);
            previewWindow.document.close();
        }

        /**
         * 防抖函数
         */
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        /**
         * 插入变量到模板
         */
        function insertVariable(variable) {
            const textarea = document.getElementById('templateContent');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const text = textarea.value;

            textarea.value = text.substring(0, start) + variable + text.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + variable.length, start + variable.length);

            // 如果当前是可视化模式，更新预览
            const visualEditor = document.getElementById('visualEditor');
            if (visualEditor && visualEditor.style.display !== 'none') {
                updateVisualPreview();
            }

            // 显示提示
            showToast(`已插入变量: ${variable}`, 'success');
        }

        /**
         * 切换编辑器模式
         */
        function switchEditorMode(mode) {
            const codeEditor = document.getElementById('templateContent');
            const visualEditor = document.getElementById('visualEditor');
            const modeBtns = document.querySelectorAll('.mode-btn');

            // 更新按钮状态
            modeBtns.forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.mode === mode) {
                    btn.classList.add('active');
                }
            });

            if (mode === 'code') {
                // 切换到代码模式
                codeEditor.style.display = 'block';
                visualEditor.style.display = 'none';
            } else if (mode === 'visual') {
                // 切换到可视化模式
                codeEditor.style.display = 'none';
                visualEditor.style.display = 'block';
                updateVisualPreview();
            }
        }

        /**
         * 更新可视化预览
         */
        function updateVisualPreview() {
            const content = document.getElementById('templateContent').value;
            const iframe = document.getElementById('visualPreview');

            // 替换模板变量为示例数据
            const sampleData = {
                '{{SITE_NAME}}': '管理系统',
                '{{USERNAME}}': '张三',
                '{{VERIFY_CODE}}': '123456',
                '{{VERIFY_URL}}': 'http://localhost:8080/verify?token=sample',
                '{{RESET_CODE}}': '654321',
                '{{RESET_URL}}': 'http://localhost:8080/reset?token=sample',
                '{{EXPIRE_TIME}}': '30',
                '{{CURRENT_YEAR}}': new Date().getFullYear(),
                '{{ADMIN_EMAIL}}': '<EMAIL>',
                '{{REQUEST_TIME}}': new Date().toLocaleString(),
                '{{REQUEST_IP}}': '*************',
                '{{ACTION_TYPE}}': '邮箱验证',
                '{{EXPIRE_TIME_FULL}}': new Date(Date.now() + 30 * 60 * 1000).toLocaleString()
            };

            let previewContent = content;
            for (const [key, value] of Object.entries(sampleData)) {
                previewContent = previewContent.replace(new RegExp(key.replace(/[{}]/g, '\\$&'), 'g'), value);
            }

            // 写入iframe
            iframe.contentDocument.open();
            iframe.contentDocument.write(previewContent);
            iframe.contentDocument.close();
        }

        /**
         * 格式化模板代码
         */
        function formatTemplate() {
            const textarea = document.getElementById('templateContent');
            let content = textarea.value;

            try {
                // 简单的HTML格式化
                content = content
                    .replace(/></g, '>\n<')
                    .replace(/^\s+|\s+$/gm, '')
                    .split('\n')
                    .map((line, index, array) => {
                        let indent = 0;
                        for (let i = 0; i < index; i++) {
                            const prevLine = array[i].trim();
                            if (prevLine.match(/<[^\/][^>]*[^\/]>$/)) {
                                indent++;
                            }
                            if (prevLine.match(/<\/[^>]+>$/)) {
                                indent--;
                            }
                        }

                        const currentLine = line.trim();
                        if (currentLine.match(/^<\/[^>]+>$/)) {
                            indent--;
                        }

                        return '    '.repeat(Math.max(0, indent)) + currentLine;
                    })
                    .join('\n');

                textarea.value = content;

                // 如果当前是可视化模式，更新预览
                const visualEditor = document.getElementById('visualEditor');
                if (visualEditor.style.display !== 'none') {
                    updateVisualPreview();
                }

                showToast('代码格式化完成', 'success');
            } catch (error) {
                showToast('格式化失败，请检查代码语法', 'error');
            }
        }

        /**
         * 重置模板到默认状态
         */
        async function resetTemplate(templateType) {
            const confirmed = await window.showModal.confirm(
                '重置模板',
                '确定要重置模板到默认状态吗？当前的修改将会丢失！'
            );

            if (!confirmed) return;

            const defaultTemplates = {
                'register_verify': getDefaultRegisterTemplate(),
                'password_reset': getDefaultPasswordResetTemplate(),
                'verify_code': getDefaultVerifyCodeTemplate()
            };

            const defaultContent = defaultTemplates[templateType];
            if (defaultContent) {
                document.getElementById('templateContent').value = defaultContent;
                showToast('模板已重置到默认状态', 'success');
            }
        }

        /**
         * 关闭模板编辑器
         */
        function closeTemplateEditor() {
            const modal = document.querySelector('.template-editor-modal');
            if (modal) {
                modal.remove();
            }
        }

        /**
         * 获取默认注册验证模板
         */
        function getDefaultRegisterTemplate() {
            return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册验证邮件</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .email-container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 15px;
        }
        .verify-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 40px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div style="font-size: 48px; margin-bottom: 20px;">🎉</div>
            <div class="logo">{{SITE_NAME}}</div>
            <h1>欢迎注册！</h1>
        </div>
        <div class="content">
            <p>亲爱的 <strong>{{USERNAME}}</strong>，</p>
            <p>感谢您注册 {{SITE_NAME}}！请点击下面的按钮验证您的邮箱：</p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{VERIFY_URL}}" class="verify-button">🔐 立即验证邮箱</a>
            </div>
            <p>验证链接有效期为 {{EXPIRE_TIME}} 分钟。</p>
        </div>
        <div style="text-align: center; font-size: 14px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 30px; margin-top: 40px;">
            <p>© {{CURRENT_YEAR}} {{SITE_NAME}}. 保留所有权利</p>
        </div>
    </div>
</body>
</html>`;
        }

        /**
         * 获取默认密码重置模板
         */
        function getDefaultPasswordResetTemplate() {
            return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码重置邮件</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        .email-container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 15px;
        }
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 16px 40px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
        }
        .code-box {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: 4px;
            margin: 25px 0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div style="font-size: 48px; margin-bottom: 20px;">🔐</div>
            <div class="logo">{{SITE_NAME}}</div>
            <h1>密码重置请求</h1>
        </div>
        <div class="content">
            <p>亲爱的 <strong>{{USERNAME}}</strong>，</p>
            <p>您的重置验证码是：</p>
            <div class="code-box">{{RESET_CODE}}</div>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{RESET_URL}}" class="reset-button">🔑 立即重置密码</a>
            </div>
            <p>重置链接有效期为 {{EXPIRE_TIME}} 分钟。</p>
        </div>
        <div style="text-align: center; font-size: 14px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 30px; margin-top: 40px;">
            <p>© {{CURRENT_YEAR}} {{SITE_NAME}}. 保留所有权利</p>
        </div>
    </div>
</body>
</html>`;
        }

        /**
         * 获取默认验证码模板
         */
        function getDefaultVerifyCodeTemplate() {
            return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码邮件</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .email-container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .code-box {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            font-size: 32px;
            font-weight: 700;
            letter-spacing: 6px;
            margin: 25px 0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div style="text-align: center; margin-bottom: 40px;">
            <div style="font-size: 48px; margin-bottom: 20px;">📧</div>
            <h1>{{SITE_NAME}}</h1>
            <h2>验证码</h2>
        </div>
        <div>
            <p>您的验证码是：</p>
            <div class="code-box">{{VERIFY_CODE}}</div>
            <p>验证码有效期为 {{EXPIRE_TIME}} 分钟。</p>
        </div>
        <div style="text-align: center; font-size: 14px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 30px; margin-top: 40px;">
            <p>© {{CURRENT_YEAR}} {{SITE_NAME}}. 保留所有权利</p>
        </div>
    </div>
</body>
</html>`;
        }

        /**
         * 刷新验证码预览
         */
        function refreshCaptchaPreview() {
            const captchaType = document.getElementById('captcha_type').value;
            const difficulty = document.getElementById('captcha_difficulty').value;
            const previewArea = document.getElementById('captcha-preview-area');

            switch (captchaType) {
                case 'math':
                    previewArea.innerHTML = generateMathCaptcha(difficulty);
                    break;
                case 'alphanumeric':
                    previewArea.innerHTML = generateAlphanumericCaptcha(difficulty);
                    break;
                case 'slider':
                    previewArea.innerHTML = generateSliderCaptcha();
                    break;
                default:
                    previewArea.innerHTML = '<div class="captcha-loading">未知的验证码类型</div>';
            }
        }

        /**
         * 生成数字计算验证码
         */
        function generateMathCaptcha(difficulty) {
            let num1, num2, operator, question, answer;

            switch (difficulty) {
                case 'easy':
                    num1 = Math.floor(Math.random() * 10) + 1;
                    num2 = Math.floor(Math.random() * 10) + 1;
                    operator = Math.random() > 0.5 ? '+' : '-';
                    if (operator === '-' && num1 < num2) {
                        [num1, num2] = [num2, num1]; // 确保结果为正数
                    }
                    break;
                case 'medium':
                    num1 = Math.floor(Math.random() * 50) + 1;
                    num2 = Math.floor(Math.random() * 50) + 1;
                    operator = ['+', '-', '×'][Math.floor(Math.random() * 3)];
                    if (operator === '-' && num1 < num2) {
                        [num1, num2] = [num2, num1];
                    }
                    if (operator === '×') {
                        num1 = Math.floor(Math.random() * 10) + 1;
                        num2 = Math.floor(Math.random() * 10) + 1;
                    }
                    break;
                case 'hard':
                    num1 = Math.floor(Math.random() * 100) + 1;
                    num2 = Math.floor(Math.random() * 100) + 1;
                    operator = ['+', '-', '×', '÷'][Math.floor(Math.random() * 4)];
                    if (operator === '-' && num1 < num2) {
                        [num1, num2] = [num2, num1];
                    }
                    if (operator === '×') {
                        num1 = Math.floor(Math.random() * 20) + 1;
                        num2 = Math.floor(Math.random() * 20) + 1;
                    }
                    if (operator === '÷') {
                        num2 = Math.floor(Math.random() * 10) + 1;
                        num1 = num2 * (Math.floor(Math.random() * 10) + 1);
                    }
                    break;
            }

            question = `${num1} ${operator} ${num2} = ?`;

            return `<div class="math-captcha">${question}</div>`;
        }

        /**
         * 生成字母数字验证码
         */
        function generateAlphanumericCaptcha(difficulty) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let length;

            switch (difficulty) {
                case 'easy':
                    length = 4;
                    break;
                case 'medium':
                    length = 5;
                    break;
                case 'hard':
                    length = 6;
                    break;
            }

            let code = '';
            for (let i = 0; i < length; i++) {
                code += chars.charAt(Math.floor(Math.random() * chars.length));
            }

            return `<div class="alphanumeric-captcha">${code}</div>`;
        }

        /**
         * 生成滑块验证码
         */
        function generateSliderCaptcha() {
            return `
                <div class="slider-captcha">
                    <div class="slider-track">
                        拖动滑块完成验证
                    </div>
                    <div class="slider-thumb">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="9,18 15,12 9,6"/>
                        </svg>
                    </div>
                </div>
            `;
        }

        /**
         * 初始化验证码预览
         */
        function initializeCaptchaPreview() {
            const captchaTypeSelect = document.getElementById('captcha_type');
            const difficultySelect = document.getElementById('captcha_difficulty');

            if (captchaTypeSelect && difficultySelect) {
                // 监听选择变化
                captchaTypeSelect.addEventListener('change', refreshCaptchaPreview);
                difficultySelect.addEventListener('change', refreshCaptchaPreview);

                // 初始化预览
                refreshCaptchaPreview();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializeSettings);
    </script>
</body>
</html>
