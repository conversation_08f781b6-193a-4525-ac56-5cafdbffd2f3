/**
 * 🔧 系统设置页面样式
 * 
 * 功能：系统设置页面的专用样式
 */

/* 设置容器 */
.settings-container {
    display: flex;
    gap: var(--spacing-6);
    max-width: 1200px;
    margin: 0 auto;
}

/* 设置导航 */
.settings-nav {
    flex-shrink: 0;
    width: 240px;
}

.nav-tabs {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
}

.nav-tab {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;
}

.nav-tab:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.nav-tab.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
}

.nav-tab svg {
    flex-shrink: 0;
    opacity: 0.8;
}

.nav-tab.active svg {
    opacity: 1;
}

/* 设置内容 */
.settings-content {
    flex: 1;
    min-width: 0;
}

.settings-form {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

/* 标签页内容 */
.tab-content {
    display: none;
    padding: var(--spacing-6);
}

.tab-content.active {
    display: block;
}

/* 设置分组 */
.settings-section {
    margin-bottom: var(--spacing-6);
}

.settings-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-2) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.section-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0 0 var(--spacing-4) 0;
    line-height: 1.5;
}

/* 表单网格 */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-5);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.form-help {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: var(--spacing-1);
}

/* 复选框组 */
.checkbox-group {
    margin: var(--spacing-3) 0;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    cursor: pointer;
    padding: var(--spacing-3);
    border-radius: var(--radius-md);
    transition: background-color 0.2s ease;
}

.checkbox-label:hover {
    background: var(--bg-hover);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-primary);
    position: relative;
    flex-shrink: 0;
    transition: all 0.2s ease;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: translate(-50%, -60%) rotate(45deg);
}

.checkbox-text {
    flex: 1;
}

.checkbox-text strong {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: var(--spacing-1);
}

.checkbox-text small {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
}

/* 表单操作区域 */
.form-actions {
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;
    align-items: center;
}

.form-footer {
    padding: var(--spacing-4) var(--spacing-6);
    background: var(--bg-hover);
    border-top: 1px solid var(--border-color);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-color-dark, #4f46e5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
    border-color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .settings-container {
        flex-direction: column;
        gap: var(--spacing-4);
    }
    
    .settings-nav {
        width: 100%;
    }
    
    .nav-tabs {
        flex-direction: row;
        overflow-x: auto;
        padding: var(--spacing-2);
    }
    
    .nav-tab {
        white-space: nowrap;
        flex-shrink: 0;
    }
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }
    
    .tab-content {
        padding: var(--spacing-4);
    }
    
    .form-footer {
        padding: var(--spacing-3) var(--spacing-4);
    }
    
    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .nav-tabs {
        flex-direction: column;
    }

    .checkbox-label {
        padding: var(--spacing-2);
    }

    .checkbox-text strong {
        font-size: 0.9rem;
    }

    .checkbox-text small {
        font-size: 0.75rem;
    }
}

/* ===== 模板编辑器样式 ===== */
.template-editor-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.template-editor-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
    width: 95%;
    max-width: 1400px;
    height: 90%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.template-editor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.template-editor-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.template-editor-main {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.template-editor-sidebar {
    width: 300px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-4);
    overflow-y: auto;
}

.template-editor-sidebar h4 {
    margin: 0 0 var(--spacing-4) 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.template-variables {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.variable-group h5 {
    margin: 0 0 var(--spacing-2) 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.variable-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.variable-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
}

.variable-item code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.75rem;
    color: var(--primary-color);
    background: var(--primary-light);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.variable-item span {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.template-editor-workspace {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.editor-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-2) var(--spacing-4);
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.editor-mode-switcher {
    display: flex;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    padding: 2px;
    border: 1px solid var(--border-color);
}

.mode-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-1) var(--spacing-2);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: calc(var(--radius-md) - 2px);
    cursor: pointer;
    transition: all 0.2s ease;
}

.mode-btn:hover {
    color: var(--text-primary);
    background: var(--bg-hover);
}

.mode-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toolbar-left .editor-hint {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-style: italic;
}

.toolbar-right {
    display: flex;
    gap: var(--spacing-2);
}

.editor-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.template-editor-textarea {
    flex: 1;
    border: none;
    outline: none;
    padding: var(--spacing-4);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    background: var(--bg-primary);
    color: var(--text-primary);
    resize: none;
    tab-size: 2;
    white-space: pre;
    overflow-wrap: normal;
    overflow-x: auto;
}

.template-visual-editor {
    flex: 1;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    margin: var(--spacing-2);
    overflow: hidden;
}

.template-editor-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-3);
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

/* 模板编辑器响应式 */
@media (max-width: 768px) {
    .template-editor-content {
        width: 100%;
        height: 100%;
        border-radius: 0;
    }

    .template-editor-main {
        flex-direction: column;
    }

    .template-editor-sidebar {
        width: 100%;
        max-height: 200px;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .template-editor-footer {
        flex-wrap: wrap;
        gap: var(--spacing-2);
    }
}
